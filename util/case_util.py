import logging
import re
import os

from httprunner import HttpRunner, Config, Step, RunRequest

from util.url_util import extract_path_from_url


def case_step_split():
    """拆分多接口为单接口，且拆分的文件保存在同级目录"""
    # 读取原始测试用例文件路径
    original_file_path = r'../testcases/api/palmpay/betting/Betting_zihao_test_test_xx.py'
    original_file_path = r'/testcases/api/palmpay\giftCard\giftCardApi_test_test.py'
    original_dir = os.path.dirname(original_file_path)
    logging.info(f"Original_dir: {original_dir}")

    # 读取原始测试用例文件内容
    with open(original_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
        print("文件内容已正确读取")

    # 使用正则表达式匹配步骤内容并拆分成单独文件
    # 注意拆分点需要加上[逗号]，避免[)]小括号的匹配影响
    steps = re.findall(r'Step\((.*?)\),', content, re.DOTALL)
    print("steps:", steps)

    for index, step in enumerate(steps):
        # 正则筛选出get或post的请求url，并根据path路径处理新生成的文件和测试类
        # 匹配数据可能出现漏的情况 \s* 解决空格和换行的影响
        url_match = re.search(r'\.get\(\s*"(.*?)"\s*\)|\.post\(\s*"(.*?)"\s*\)', step, re.IGNORECASE | re.DOTALL)
        print("url_match:", url_match)
        if url_match:
            url = url_match.group(1) or url_match.group(2)
            logging.info(f"解析出的url为：{url}")
            print(f"解析出的url为：{url}")
            # 提取path
            path = extract_path_from_url(url)
            logging.info(f"提取的path为：{path}")
            print(f"提取的path为：{path}")
            path_parts = path.split('/')
            # step内容中 url 替换为 path
            step = step.replace(url, path)
            # 处理setup内容 step.setup_hook('${setup_hooks_request($request)}')
            # 定义正则表达式匹配模式，匹配 RunRequest() 方法中的 .get() 或者 .post()
            # pattern = r'RunRequest\("[^"]*"\)\s*\.\s*(get|post)\("[^"]*"\)'
            pattern = r'RunRequest\("[^"]*"\)'

            # 匹配 RunRequest() 方法并获取结果
            match = re.search(pattern, step)
            if match:
                # method_type = match.group(1)  # 获取匹配到的方法类型（get 或者 post）
                matched_text = match.group()  # 获取匹配到的文本内容
                print(f"匹配到的文本内容：{matched_text}")
                modified_text = matched_text + ".setup_hook('${setup_hooks_request($request)}')"  # 在匹配到的文本后增加 .setup(xx) 部分

                # 替换原文本中的匹配部分
                step = re.sub(pattern, modified_text, step)
                print(f"step处理后的部分：{step}")

            # 处理文件名和类名
            if len(path_parts) >= 2:
                # 兼容文件名或类名中划线（-）的异常
                file_name = f'{path_parts[-2]}_{path_parts[-1]}_test.py'.replace("-", "_")
                class_name = f'Test{path_parts[-2].capitalize()}{path_parts[-1].capitalize()}Test'.replace("-", "")
                new_file_path = os.path.join(original_dir, file_name)

                # 说明：`${ENV(base_url_C)}`  模版表达式：`${{...}}`, .base_url("${{ENV(base_url_C)}}")
                step_content = f'''
# NOTE: Generated By HttpRunner v4.3.5
# FROM: {original_file_path}
from httprunner import HttpRunner, Config, Step, RunRequest

class {class_name}(HttpRunner):
    config = Config("step name").base_url("${{ENV(base_url_C)}}")

    teststeps = [
        Step({step})
    ]

if __name__ == "__main__":
    {class_name}().test_start()
'''

                with open(new_file_path, 'w', encoding='utf-8') as new_file:
                    new_file.write(step_content)
                    logging.info("新文件已创建file: %s" % new_file_path)
                    print("新文件已创建file: %s" % new_file_path)

    print("拆分完成！")


if __name__ == '__main__':
    case_step_split()
