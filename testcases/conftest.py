import subprocess

import pytest
import os
from loguru import logger

from common.get_token import write_token_to_yml
from common.get_token_bussiness import write_bussiness_token_to_yml


@pytest.fixture(scope="session", autouse=True)
def session_fixture(request):
    """setup and teardown each task"""
    logger.warning(f"-------session级别前置---------")
    logger.warning(f"---------C端：全局写入token为")
    write_token_to_yml()
    logger.warning(f"---------B端：全局写入token为")
    write_bussiness_token_to_yml()
    total_testcases_num = request.node.testscollected
    testcases = []
    for item in request.node.items:
        testcase = {
            "name": item.cls.config.name,
            "path": item.cls.config.path,
            "node_id": item.nodeid,
        }
        testcases.append(testcase)

    logger.debug(f"collected {total_testcases_num} testcases: {testcases}")

    yield

    logger.debug(f"teardown task fixture")
    logger.warning(f"-------session模块级别后置---------")


# Define the CLI command for MidScene.
# It might be `midscene` or `npx midscene` depending on installation.
MIDSCENE_COMMAND = "npx"
MIDSCENE_ARG = "midscene"


def pytest_collect_file(parent, file_path):
    """
    Pytest hook to discover test files.
    We'll teach it to find MidScene's YAML files.
    """
    # We only care about .yml or .yaml files inside the 'UI/' directory
    if (file_path.suffix == ".yml" or file_path.suffix == ".yaml") and "UI" in file_path.parts:
        return MidSceneFile.from_parent(parent, path=file_path)


class MidSceneFile(pytest.File):
    """A custom pytest File collector for MidScene YAML files."""

    def collect(self):
        """Yields a single test item for the entire file."""
        yield MidSceneItem.from_parent(self, name=self.path.stem)


class MidSceneItem(pytest.Item):
    """A custom pytest Item that runs a MidScene test."""

    def __init__(self, *, parent, name, **kwargs):
        super().__init__(name=name, parent=parent, **kwargs)

    def runtest(self):
        """
        Executes the test by calling the MidScene CLI as a subprocess.
        This is the core of the integration.
        """
        # Construct the command: `npx midscene run tests/ui/your_test.yml`
        command = [MIDSCENE_ARG, str(self.path)]

        # Run the command
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            cwd=self.path.parent.parent.parent  # Run from the project root
        )

        # Check the result and report back to pytest
        if result.returncode != 0:
            # If the command fails, fail the pytest test and show the output
            error_message = f"MidScene test failed with exit code {result.returncode}\n"
            error_message += f"----- STDOUT -----\n{result.stdout}\n"
            error_message += f"----- STDERR -----\n{result.stderr}\n"
            pytest.fail(error_message, pytrace=False)

        # Optionally, you can log stdout for successful runs for verbosity
        self.add_report_section("call", "midscene_stdout", result.stdout)

    def repr_failure(self, excinfo):
        """Custom failure reporting."""
        if excinfo.errisinstance(Exception):
            return str(excinfo.value)
        return super().repr_failure(excinfo)

    def reportinfo(self):
        """Returns the test's name and path for reporting."""
        return self.path, 0, f"test: {self.name}"
