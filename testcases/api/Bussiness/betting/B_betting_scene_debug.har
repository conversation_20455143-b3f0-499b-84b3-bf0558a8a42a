{"log": {"version": "1.2", "creator": {"name": "<PERSON>", "version": "4.6.4"}, "entries": [{"startedDateTime": "2024-06-24T11:35:31.239+08:00", "time": 232, "request": {"method": "GET", "url": "https://ng-partner.palmpay.app/api/cfront/quickteller/queryFistClass?categoryId=8", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/cfront/quickteller/queryFistClass?categoryId=8"}, {"name": ":authority", "value": "ng-partner.palmpay.app"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "PARTNER_c77d77a4a7568dd8012345"}, {"name": "pp_black_box", "value": "qGPVs1719194428MCN3zTAnlo8"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "4.2.0&606201849"}, {"name": "pp_timestamp", "value": "1719200131212"}, {"name": "pp_country_code", "value": "NG"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay Business/4.2.0&606201849 (Android 13)"}, {"name": "pp_lat", "value": "22.576576576576578"}, {"name": "pp_lng", "value": "113.93863889459115"}, {"name": "pp_token", "value": "fdb0735f-d780-4f90-992c-02b0236b5621"}, {"name": "pp_req_sign", "value": "ev0dC%2BDmXa3uhUvS6IfPg95sTE9qj88h3g%2F%2BZiW%2BZgJMKjIgd6bwDZQ3QrTAFESAcadykhqtlVVCZPvxiF698LjqCcbb%2BLIX0WYAuKqx%2BqIo5shtlP7GTSWP%2B9X7j%2FTjFjbiDRoz5Zoa59%2Bro67FWPmHbE3RPtoiNiouNNc%2FjAs%3D"}, {"name": "pp_req_sign_2", "value": "ev0dC%2BDmXa3uhUvS6IfPg95sTE9qj88h3g%2F%2BZiW%2BZgJMKjIgd6bwDZQ3QrTAFESAcadykhqtlVVCZPvxiF698LjqCcbb%2BLIX0WYAuKqx%2BqIo5shtlP7GTSWP%2B9X7j%2FTjFjbiDRoz5Zoa59%2Bro67FWPmHbE3RPtoiNiouNNc%2FjAs%3D"}, {"name": "accept-encoding", "value": "br,gzip"}], "queryString": [{"name": "categoryId", "value": "8"}], "headersSize": 403, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 24 Jun 2024 03:35:32 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,appSource,memberId"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "partner-front:prod:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "21"}, {"name": "x-envoy-decorator-operation", "value": "partner-front.prod.svc.cluster.local:80/*"}, {"name": "cf-cache-status", "value": "DYNAMIC"}, {"name": "server", "value": "cloudflare"}, {"name": "cf-ray", "value": "8989ab198919887d-LHR"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 14758, "compression": 13336, "mimeType": "application/json;charset=UTF-8", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 59, "bodySize": 1422}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 231, "receive": 1}}, {"startedDateTime": "2024-06-24T11:35:31.296+08:00", "time": 229, "request": {"method": "GET", "url": "https://ng-partner.palmpay.app/api/cfront/quickteller/billerAnnouncement?categoryId=8", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/cfront/quickteller/billerAnnouncement?categoryId=8"}, {"name": ":authority", "value": "ng-partner.palmpay.app"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "PARTNER_c77d77a4a7568dd8012345"}, {"name": "pp_black_box", "value": "qGPVf17191944283CN3zTAnlU8"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "4.2.0&606201849"}, {"name": "pp_timestamp", "value": "1719200131212"}, {"name": "pp_country_code", "value": "NG"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay Business/4.2.0&606201849 (Android 13)"}, {"name": "pp_lat", "value": "22.576576576576578"}, {"name": "pp_lng", "value": "113.93863889459115"}, {"name": "pp_token", "value": "fdb0735f-d780-4f90-992c-02b0236b5621"}, {"name": "pp_req_sign", "value": "ev0dC%2BDmXa3uhUvS6IfPg95sTE9qj88h3g%2F%2BZiW%2BZgJMKjIgd6bwDZQ3QrTAFESAcadykhqtlVVCZPvxiF698LjqCcbb%2BLIX0WYAuKqx%2BqIo5shtlP7GTSWP%2B9X7j%2FTjFjbiDRoz5Zoa59%2Bro67FWPmHbE3RPtoiNiouNNc%2FjAs%3D"}, {"name": "pp_req_sign_2", "value": "ev0dC%2BDmXa3uhUvS6IfPg95sTE9qj88h3g%2F%2BZiW%2BZgJMKjIgd6bwDZQ3QrTAFESAcadykhqtlVVCZPvxiF698LjqCcbb%2BLIX0WYAuKqx%2BqIo5shtlP7GTSWP%2B9X7j%2FTjFjbiDRoz5Zoa59%2Bro67FWPmHbE3RPtoiNiouNNc%2FjAs%3D"}, {"name": "accept-encoding", "value": "br,gzip"}], "queryString": [{"name": "categoryId", "value": "8"}], "headersSize": 82, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 24 Jun 2024 03:35:32 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,appSource,memberId"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "partner-front:prod:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "19"}, {"name": "x-envoy-decorator-operation", "value": "partner-front.prod.svc.cluster.local:80/*"}, {"name": "cf-cache-status", "value": "DYNAMIC"}, {"name": "server", "value": "cloudflare"}, {"name": "cf-ray", "value": "8989ab19f948887d-LHR"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 55, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpudWxsfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 37, "bodySize": 72}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 229, "receive": 0}}, {"startedDateTime": "2024-06-24T11:35:53.248+08:00", "time": 273, "request": {"method": "POST", "url": "https://ng-partner.palmpay.app/api/cfront/quickteller/querySecondClass", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/quickteller/querySecondClass"}, {"name": ":authority", "value": "ng-partner.palmpay.app"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "PARTNER_c77d77a4a7568dd8012345"}, {"name": "pp_black_box", "value": "qGPVC1719194428hCN3zTAnlp8"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "4.2.0&606201849"}, {"name": "pp_timestamp", "value": "1719200152985"}, {"name": "pp_country_code", "value": "NG"}, {"name": "user-agent", "value": "PalmPay Business/4.2.0&606201849 (Android 13)"}, {"name": "pp_lat", "value": "22.576576576576578"}, {"name": "pp_lng", "value": "113.93863889459115"}, {"name": "pp_token", "value": "fdb0735f-d780-4f90-992c-02b0236b5621"}, {"name": "pp_req_sign", "value": "T4lAvHcS%2BGU3RNXi3yq5IZMgp6v%2BaTNnUSE%2Br6NUxAf7g7qzKM2ZWhBhmu6l3xAo4Sz3Ndl2s13YGopxPdCV76azVrHq2rIxUQYGOoRfVVR124rBVzfCzZnZgWa0oyK0YnHX6%2FAWOBhxnxllmHjk0va%2Feij95gYjyUzCjrBxz8M%3D"}, {"name": "pp_req_sign_2", "value": "VDeJYZvrnLoPcP7IiP%2BBYcvfdLis48DuSWL0UmSj1Pbfj6mxxw%2FXeCm6Tm%2BRfr8XEfp4HVBIMP8KcPgNapad9QjRlGGLs5RztMtEldp6S5%2FPYBetDXpJsqbx0puZbc6aFksptxyBvFe2xIUjjJ5%2F9nwt3B2AJFpeh%2FXwwz4Fq%2BA%3D"}, {"name": "accept-encoding", "value": "br,gzip"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "36"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"billerId\":\"2269\",\"categoryId\":\"8\"}"}, "headersSize": 613, "bodySize": 36}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 24 Jun 2024 03:35:54 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,appSource,memberId"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "partner-front:prod:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "19"}, {"name": "x-envoy-decorator-operation", "value": "partner-front.prod.svc.cluster.local:80/*"}, {"name": "cf-cache-status", "value": "DYNAMIC"}, {"name": "server", "value": "cloudflare"}, {"name": "cf-ray", "value": "8989aba31d45887d-LHR"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 840, "compression": 377, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbeyJpZCI6bnVsbCwiY2F0ZWdvcnlJZCI6IjgiLCJiaWxsZXJJZCI6IjIyNjkiLCJpc0Ftb3VudEZpeGVkIjoxLCJwYXltZW50SXRlbUlkIjoiMjAwNTE0MDU1NjI2Mjk1ODQ4IiwicGF5bWVudENvZGUiOiIiLCJwYXltZW50SXRlbU5hbWUiOiJGdW5kIEJldCA5amEiLCJhbW91bnQiOjEwMDAwLCJjb2RlIjpudWxsLCJjdXJyZW5jeVN5bWJvbCI6bnVsbCwiaXRlbUZlZSI6bnVsbCwiY2F0ZWdvcnlOYW1lIjoiQmV0dGluZyIsImNhdGVnb3J5RGVzY3JpcHRpb24iOm51bGwsImJpbGxlck5hbWUiOiJCZXQ5amEiLCJjdXN0b21lckZpZWxkMSI6IiIsImN1c3RvbWVyRmllbGQyIjpudWxsLCJwYXlEaXJlY3RQcm9kdWN0SWQiOm51bGwsInBheURpcmVjdEluc3RpdHV0aW9uSWQiOm51bGwsIm5hcnJhdGlvbiI6bnVsbCwic2hvcnROYW1lIjpudWxsLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsIm15RmF2b3JpdGUiOm51bGwsInN0YXR1cyI6MSwibWluQW1vdW50IjoxMDAwMCwibWF4QW1vdW50IjoyMDAwMDAwMCwib21zTWluQW1vdW50IjoxMDAwMCwib21zTWF4QW1vdW50IjoyMDAwMDAwMCwiaW5zdGl0dXRpb25Mb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtaW1hZ2UtcHJvZC5zMy5ldS13ZXN0LTEuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNjIwNjE2MTE0MTMyMTItQmV0JTIwOSUyMGphLnBuZyIsInByb21wdFRleHQiOiIiLCJyZWNvbW1lbmQiOmZhbHNlfV19", "encoding": "base64"}, "redirectURL": null, "headersSize": 56, "bodySize": 463}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 273, "receive": 0}}, {"startedDateTime": "2024-06-24T11:36:05.358+08:00", "time": 237, "request": {"method": "POST", "url": "https://ng-partner.palmpay.app/api/cfront/bill/transactionHistoryPartner/v2", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/bill/transactionHistoryPartner/v2"}, {"name": ":authority", "value": "ng-partner.palmpay.app"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "PARTNER_c77d77a4a7568dd8012345"}, {"name": "pp_black_box", "value": "qGPV81719194428WCN3zTAnlx8"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "4.2.0&606201849"}, {"name": "pp_timestamp", "value": "1719200165327"}, {"name": "pp_country_code", "value": "NG"}, {"name": "user-agent", "value": "PalmPay Business/4.2.0&606201849 (Android 13)"}, {"name": "pp_lat", "value": "22.576576576576578"}, {"name": "pp_lng", "value": "113.93863889459115"}, {"name": "pp_token", "value": "fdb0735f-d780-4f90-992c-02b0236b5621"}, {"name": "pp_req_sign", "value": "PyOImzTKXAimVNvgHa5tZilrduOWlkhO4382ZpB1GGAowShtq9e2%2BfPHQjd%2FDdYmz7cImV2M1%2FXVJEiGLi1TU0Sh%2FBBWgEHeU3knD5MdaYI1kyrExsiv2ZNxI2ydAylJi1Z7iyUSYbkMxbkVsuOpuJr5vr%2BYw9SQFggOri7NTQ8%3D"}, {"name": "pp_req_sign_2", "value": "dGYWFBvtqC%2B3C%2F%2FVk0uH2TFWBgJQblXxkIpDj0zxD%2BFofoCVwYcWyRGR%2B54DKhV3T5C5UoeUvHlbSMC7Ms2FgTpQzZIC8wVyT%2F8hmCP2ziQtKwb%2By8AFJXMy%2F0mraO5%2B0VogDDAKHNtQKUz3mBMk9kijLwMFj9O8EGiAIU2eVig%3D"}, {"name": "accept-encoding", "value": "br,gzip"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "114"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"beginTime\":1716608165326,\"defaultShop\":false,\"endTime\":1719200165326,\"pageNum\":1,\"pageSize\":20,\"transType\":\"e8\"}"}, "headersSize": 396, "bodySize": 114}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 24 Jun 2024 03:36:06 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,appSource,memberId"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "partner-front:prod:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "25"}, {"name": "x-envoy-decorator-operation", "value": "partner-front.prod.svc.cluster.local:80/*"}, {"name": "cf-cache-status", "value": "DYNAMIC"}, {"name": "server", "value": "cloudflare"}, {"name": "cf-ray", "value": "8989abeec86b887d-LHR"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 142, "compression": 12, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7InRvdGFsIjowLCJ0b3RhbFBhZ2UiOjAsImN1clBhZ2UiOjEsInBhZ2VTaXplIjoyMCwiZGF0YU1hcCI6bnVsbCwiZXh0RGF0YSI6bnVsbCwibGlzdCI6W119fQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 59, "bodySize": 130}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 235, "receive": 1}}]}}