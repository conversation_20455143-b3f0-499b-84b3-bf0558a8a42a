# NOTE: Generated By HttpRunner v4.3.5
# FROM: testcases\Bussiness\data\Bussiness_data_20240410_test.json
from httprunner import HttpRunner, Config, Step, RunRequest


class TestBillerAnnouncementTest(HttpRunner):
    config = Config("Bussiness端:betting的biller公告").base_url("${ENV(base_url_B)}")

    teststeps = [
        Step(
            RunRequest("请求标题：B端betting的biller公告")
            .setup_hook('${setup_hooks_request_business($request)}')
            .get(
                "/api/cfront/quickteller/queryFistClass"
            )
            .with_params(**{"categoryId": "8"})
            .with_headers(
                **{
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Wed, 28 Feb 2024 06:30:02 GMT",
                    # "pp_channel": "googleplay",
                    # "pp_client_ver": "5.3.0&602280702",
                    # "pp_device_id": "bee648d4b0a5ab70012345",
                    # "pp_device_type": "ANDROID",
                    # "pp_req_sign": "ZDfAAOuQ4o8I8UVzS80MrwGv7ZHRHwPyZL27ZuWCbkM45wxQ41DMokcYx3P7%2BNZxuIlgjDZqPKlOe0u3lCiOeN6CxNqgxN%2FUCyHrFYCjNqHQt7A%2FQsSt6c9rog0j6aF1rZTK7K5kqTESmpvVlbPOkUh4cmjHVxWlotIkO3%2FO2aQ%3D",
                    # "pp_timestamp": "${get_pp_timestamp()}",
                    # "pp_token": "22761d93-cea3-47cf-a8f9-a8087e540409",
                    "user-agent": "PalmPay/5.3.0&602280702 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
    ]


if __name__ == "__main__":
    TestBillerAnnouncementTest().test_start()
