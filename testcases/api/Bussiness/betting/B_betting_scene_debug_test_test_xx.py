# NOTE: Generated By HttpRunner v4.3.5
# FROM: testcases\Bussiness\betting\B_betting_scene_debug_test.json
from httprunner import HttpRunner, Config, Step, RunRequest


class TestCaseBBettingSceneDebugTest(HttpRunner):

    config = Config("testcase description")

    teststeps = [
        Step(
            RunRequest("")
            .get("https://ng-partner.palmpay.app/api/cfront/quickteller/queryFistClass")
            .with_params(**{"categoryId": "8"})
            .with_headers(
                **{
                    ":authority": "ng-partner.palmpay.app",
                    ":method": "GET",
                    ":path": "/api/cfront/quickteller/queryFistClass?categoryId=8",
                    ":scheme": "https",
                    "accept-encoding": "br,gzip",
                    "content-type": "application/json",
                    "pp_black_box": "qGPVs1719194428MCN3zTAnlo8",
                    "pp_client_ver": "4.2.0&606201849",
                    "pp_country_code": "NG",
                    "pp_device_id": "PARTNER_c77d77a4a7568dd8012345",
                    "pp_device_type": "ANDROID",
                    "pp_lat": "22.576576576576578",
                    "pp_lng": "113.93863889459115",
                    "pp_req_sign": "ev0dC%2BDmXa3uhUvS6IfPg95sTE9qj88h3g%2F%2BZiW%2BZgJMKjIgd6bwDZQ3QrTAFESAcadykhqtlVVCZPvxiF698LjqCcbb%2BLIX0WYAuKqx%2BqIo5shtlP7GTSWP%2B9X7j%2FTjFjbiDRoz5Zoa59%2Bro67FWPmHbE3RPtoiNiouNNc%2FjAs%3D",
                    "pp_req_sign_2": "ev0dC%2BDmXa3uhUvS6IfPg95sTE9qj88h3g%2F%2BZiW%2BZgJMKjIgd6bwDZQ3QrTAFESAcadykhqtlVVCZPvxiF698LjqCcbb%2BLIX0WYAuKqx%2BqIo5shtlP7GTSWP%2B9X7j%2FTjFjbiDRoz5Zoa59%2Bro67FWPmHbE3RPtoiNiouNNc%2FjAs%3D",
                    "pp_timestamp": "1719200131212",
                    "pp_token": "fdb0735f-d780-4f90-992c-02b0236b5621",
                    "user-agent": "PalmPay Business/4.2.0&606201849 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .get(
                "https://ng-partner.palmpay.app/api/cfront/quickteller/billerAnnouncement"
            )
            .with_params(**{"categoryId": "8"})
            .with_headers(
                **{
                    ":authority": "ng-partner.palmpay.app",
                    ":method": "GET",
                    ":path": "/api/cfront/quickteller/billerAnnouncement?categoryId=8",
                    ":scheme": "https",
                    "accept-encoding": "br,gzip",
                    "content-type": "application/json",
                    "pp_black_box": "qGPVf17191944283CN3zTAnlU8",
                    "pp_client_ver": "4.2.0&606201849",
                    "pp_country_code": "NG",
                    "pp_device_id": "PARTNER_c77d77a4a7568dd8012345",
                    "pp_device_type": "ANDROID",
                    "pp_lat": "22.576576576576578",
                    "pp_lng": "113.93863889459115",
                    "pp_req_sign": "ev0dC%2BDmXa3uhUvS6IfPg95sTE9qj88h3g%2F%2BZiW%2BZgJMKjIgd6bwDZQ3QrTAFESAcadykhqtlVVCZPvxiF698LjqCcbb%2BLIX0WYAuKqx%2BqIo5shtlP7GTSWP%2B9X7j%2FTjFjbiDRoz5Zoa59%2Bro67FWPmHbE3RPtoiNiouNNc%2FjAs%3D",
                    "pp_req_sign_2": "ev0dC%2BDmXa3uhUvS6IfPg95sTE9qj88h3g%2F%2BZiW%2BZgJMKjIgd6bwDZQ3QrTAFESAcadykhqtlVVCZPvxiF698LjqCcbb%2BLIX0WYAuKqx%2BqIo5shtlP7GTSWP%2B9X7j%2FTjFjbiDRoz5Zoa59%2Bro67FWPmHbE3RPtoiNiouNNc%2FjAs%3D",
                    "pp_timestamp": "1719200131212",
                    "pp_token": "fdb0735f-d780-4f90-992c-02b0236b5621",
                    "user-agent": "PalmPay Business/4.2.0&606201849 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.data", None, "assert response body data")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .post(
                "https://ng-partner.palmpay.app/api/cfront/quickteller/querySecondClass"
            )
            .with_headers(
                **{
                    ":authority": "ng-partner.palmpay.app",
                    ":method": "POST",
                    ":path": "/api/cfront/quickteller/querySecondClass",
                    ":scheme": "https",
                    "accept-encoding": "br,gzip",
                    "content-length": "36",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_black_box": "qGPVC1719194428hCN3zTAnlp8",
                    "pp_client_ver": "4.2.0&606201849",
                    "pp_country_code": "NG",
                    "pp_device_id": "PARTNER_c77d77a4a7568dd8012345",
                    "pp_device_type": "ANDROID",
                    "pp_lat": "22.576576576576578",
                    "pp_lng": "113.93863889459115",
                    "pp_req_sign": "T4lAvHcS%2BGU3RNXi3yq5IZMgp6v%2BaTNnUSE%2Br6NUxAf7g7qzKM2ZWhBhmu6l3xAo4Sz3Ndl2s13YGopxPdCV76azVrHq2rIxUQYGOoRfVVR124rBVzfCzZnZgWa0oyK0YnHX6%2FAWOBhxnxllmHjk0va%2Feij95gYjyUzCjrBxz8M%3D",
                    "pp_req_sign_2": "VDeJYZvrnLoPcP7IiP%2BBYcvfdLis48DuSWL0UmSj1Pbfj6mxxw%2FXeCm6Tm%2BRfr8XEfp4HVBIMP8KcPgNapad9QjRlGGLs5RztMtEldp6S5%2FPYBetDXpJsqbx0puZbc6aFksptxyBvFe2xIUjjJ5%2F9nwt3B2AJFpeh%2FXwwz4Fq%2BA%3D",
                    "pp_timestamp": "1719200152985",
                    "pp_token": "fdb0735f-d780-4f90-992c-02b0236b5621",
                    "user-agent": "PalmPay Business/4.2.0&606201849 (Android 13)",
                }
            )
            .with_data({"billerId": "2269", "categoryId": "8"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .post(
                "https://ng-partner.palmpay.app/api/cfront/bill/transactionHistoryPartner/v2"
            )
            .with_headers(
                **{
                    ":authority": "ng-partner.palmpay.app",
                    ":method": "POST",
                    ":path": "/api/cfront/bill/transactionHistoryPartner/v2",
                    ":scheme": "https",
                    "accept-encoding": "br,gzip",
                    "content-length": "114",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_black_box": "qGPV81719194428WCN3zTAnlx8",
                    "pp_client_ver": "4.2.0&606201849",
                    "pp_country_code": "NG",
                    "pp_device_id": "PARTNER_c77d77a4a7568dd8012345",
                    "pp_device_type": "ANDROID",
                    "pp_lat": "22.576576576576578",
                    "pp_lng": "113.93863889459115",
                    "pp_req_sign": "PyOImzTKXAimVNvgHa5tZilrduOWlkhO4382ZpB1GGAowShtq9e2%2BfPHQjd%2FDdYmz7cImV2M1%2FXVJEiGLi1TU0Sh%2FBBWgEHeU3knD5MdaYI1kyrExsiv2ZNxI2ydAylJi1Z7iyUSYbkMxbkVsuOpuJr5vr%2BYw9SQFggOri7NTQ8%3D",
                    "pp_req_sign_2": "dGYWFBvtqC%2B3C%2F%2FVk0uH2TFWBgJQblXxkIpDj0zxD%2BFofoCVwYcWyRGR%2B54DKhV3T5C5UoeUvHlbSMC7Ms2FgTpQzZIC8wVyT%2F8hmCP2ziQtKwb%2By8AFJXMy%2F0mraO5%2B0VogDDAKHNtQKUz3mBMk9kijLwMFj9O8EGiAIU2eVig%3D",
                    "pp_timestamp": "1719200165327",
                    "pp_token": "fdb0735f-d780-4f90-992c-02b0236b5621",
                    "user-agent": "PalmPay Business/4.2.0&606201849 (Android 13)",
                }
            )
            .with_data(
                {
                    "beginTime": 1716608165326,
                    "defaultShop": False,
                    "endTime": 1719200165326,
                    "pageNum": 1,
                    "pageSize": 20,
                    "transType": "e8",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
    ]


if __name__ == "__main__":
    TestCaseBBettingSceneDebugTest().test_start()
