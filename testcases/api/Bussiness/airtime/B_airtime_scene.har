{"log": {"version": "1.2", "creator": {"name": "<PERSON>", "version": "4.6.4"}, "entries": [{"startedDateTime": "2024-06-24T10:28:04.774+08:00", "time": 544, "request": {"method": "GET", "url": "https://ng-partner.palmpay.app/api/scene-business-product/airtime/menuList", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/scene-business-product/airtime/menuList"}, {"name": ":authority", "value": "ng-partner.palmpay.app"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "PARTNER_c77d77a4a7568dd8012345"}, {"name": "pp_black_box", "value": "qGPVo1719194428LCN3zTAnlm8"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "4.2.0&606201849"}, {"name": "pp_timestamp", "value": "1719196084726"}, {"name": "pp_country_code", "value": "NG"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay Business/4.2.0&606201849 (Android 13)"}, {"name": "pp_lat", "value": "22.576576576576578"}, {"name": "pp_lng", "value": "113.93863889459115"}, {"name": "pp_token", "value": "fdb0735f-d780-4f90-992c-02b0236b5621"}, {"name": "pp_req_sign", "value": "XWfsDY6bVG34q3YBiUHBpxUUPzb5DN2ITif%2FJxm1yBaVDqCozypDnMpLjxavqLCd9neF2J3A4mMqGJPAZ9c0k5TXlzN5ABl5nViOxbOr07T0xBrcI41g8LTysaqW5RcBVVtG6EdqZbbSWrRT3TyvN%2Bxn46X7vvNXTi0kJDpGyYo%3D"}, {"name": "pp_req_sign_2", "value": "XWfsDY6bVG34q3YBiUHBpxUUPzb5DN2ITif%2FJxm1yBaVDqCozypDnMpLjxavqLCd9neF2J3A4mMqGJPAZ9c0k5TXlzN5ABl5nViOxbOr07T0xBrcI41g8LTysaqW5RcBVVtG6EdqZbbSWrRT3TyvN%2Bxn46X7vvNXTi0kJDpGyYo%3D"}, {"name": "accept-encoding", "value": "br,gzip"}, {"name": "if-modified-since", "value": "Mon, 24 Jun 2024 02:27:23 GMT"}], "queryString": [], "headersSize": 402, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 24 Jun 2024 02:28:05 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "61cafb2e3fe60cec98ab1d4101e2b004"}, {"name": "x-envoy-upstream-service-time", "value": "290"}, {"name": "x-envoy-decorator-operation", "value": "mgw-core.prod.svc.cluster.local:80/*"}, {"name": "cf-cache-status", "value": "DYNAMIC"}, {"name": "server", "value": "cloudflare"}, {"name": "cf-ray", "value": "8989484f1a599586-LHR"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 7439, "compression": 6759, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbeyJiaWxsZXJJZCI6Ik1UTiIsImJpbGxlck5hbWUiOiJNVE4iLCJiaWxsZXJJY29uIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtaW1hZ2UtcHJvZC5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE3MTgyNjcwMjgxNzU3LU1UTi5wbmciLCJtaW5BbW91bnQiOjUwMDAsIm1heEFtb3VudCI6NTAwMDAwMCwidGlwcyI6IiIsInVzZXJJZFRpcHMiOiJFbnRlciBQaG9uZSBOdW1iZXIiLCJhbXRUaXBzIjpudWxsLCJiaWxsZXJUaXBVcmwiOm51bGwsImRhcmtUaXBVcmwiOm51bGwsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsIml0ZW1MaXN0IjpbeyJiaWxsZXJJZCI6bnVsbCwiaXRlbUlkIjoiNDg1MzkzMjEiLCJpdGVtTmFtZSI6bnVsbCwicHJpY2UiOjUwMDAsInNlcmlhbE5vIjoxLCJzdXNwZW5kIjpmYWxzZSwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOjAsInJld2FyZHNBbW91bnRUZXh0IjpudWxsLCJyZW1hcmsiOm51bGwsImRpc3BsYXlPcmRlciI6bnVsbH0seyJiaWxsZXJJZCI6bnVsbCwiaXRlbUlkIjoiNDg1MzkzNjAiLCJpdGVtTmFtZSI6bnVsbCwicHJpY2UiOjEwMDAwLCJzZXJpYWxObyI6Miwic3VzcGVuZCI6ZmFsc2UsIm1pbkFtb3VudCI6bnVsbCwibWF4QW1vdW50IjpudWxsLCJyZXdhcmRzQW1vdW50VGV4dCI6bnVsbCwicmVtYXJrIjpudWxsLCJkaXNwbGF5T3JkZXIiOm51bGx9LHsiYmlsbGVySWQiOm51bGwsIml0ZW1JZCI6IjQ4NTM5NDA4IiwiaXRlbU5hbWUiOm51bGwsInByaWNlIjoyMDAwMCwic2VyaWFsTm8iOjMsInN1c3BlbmQiOmZhbHNlLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwicmV3YXJkc0Ftb3VudFRleHQiOm51bGwsInJlbWFyayI6bnVsbCwiZGlzcGxheU9yZGVyIjpudWxsfSx7ImJpbGxlcklkIjpudWxsLCJpdGVtSWQiOiI0ODUzOTQ0NSIsIml0ZW1OYW1lIjpudWxsLCJwcmljZSI6NTAwMDAsInNlcmlhbE5vIjo0LCJzdXNwZW5kIjpmYWxzZSwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsInJld2FyZHNBbW91bnRUZXh0IjpudWxsLCJyZW1hcmsiOm51bGwsImRpc3BsYXlPcmRlciI6bnVsbH0seyJiaWxsZXJJZCI6bnVsbCwiaXRlbUlkIjoiNDg1Mzk3NDciLCJpdGVtTmFtZSI6bnVsbCwicHJpY2UiOjEwMDAwMCwic2VyaWFsTm8iOjUsInN1c3BlbmQiOmZhbHNlLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6MCwicmV3YXJkc0Ftb3VudFRleHQiOm51bGwsInJlbWFyayI6bnVsbCwiZGlzcGxheU9yZGVyIjpudWxsfSx7ImJpbGxlcklkIjpudWxsLCJpdGVtSWQiOiI0ODUzOTc4OCIsIml0ZW1OYW1lIjpudWxsLCJwcmljZSI6MjAwMDAwLCJzZXJpYWxObyI6Niwic3VzcGVuZCI6ZmFsc2UsIm1pbkFtb3VudCI6bnVsbCwibWF4QW1vdW50IjpudWxsLCJyZXdhcmRzQW1vdW50VGV4dCI6bnVsbCwicmVtYXJrIjpudWxsLCJkaXNwbGF5T3JkZXIiOm51bGx9XX0seyJiaWxsZXJJZCI6IkFJUlRFTCIsImJpbGxlck5hbWUiOiJBaXJ0ZWwiLCJiaWxsZXJJY29uIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtaW1hZ2UtcHJvZC5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE3MTgyNjcwNTU3MzgxMC1BSVJURUwucG5nIiwibWluQW1vdW50Ijo1MDAwLCJtYXhBbW91bnQiOjUwMDAwMDAsInRpcHMiOiIiLCJ1c2VySWRUaXBzIjoiRW50ZXIgUGhvbmUgTnVtYmVyIiwiYW10VGlwcyI6bnVsbCwiYmlsbGVyVGlwVXJsIjpudWxsLCJkYXJrVGlwVXJsIjpudWxsLCJzdXNwZW5kIjpmYWxzZSwic3RhdHVzIjoxLCJpdGVtTGlzdCI6W3siYmlsbGVySWQiOm51bGwsIml0ZW1JZCI6IjQ4NTM5MDE1IiwiaXRlbU5hbWUiOm51bGwsInByaWNlIjo1MDAwLCJzZXJpYWxObyI6MSwic3VzcGVuZCI6ZmFsc2UsIm1pbkFtb3VudCI6bnVsbCwibWF4QW1vdW50IjpudWxsLCJyZXdhcmRzQW1vdW50VGV4dCI6bnVsbCwicmVtYXJrIjpudWxsLCJkaXNwbGF5T3JkZXIiOm51bGx9LHsiYmlsbGVySWQiOm51bGwsIml0ZW1JZCI6IjQ4NTM5MDMzIiwiaXRlbU5hbWUiOm51bGwsInByaWNlIjoxMDAwMCwic2VyaWFsTm8iOjIsInN1c3BlbmQiOmZhbHNlLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwicmV3YXJkc0Ftb3VudFRleHQiOm51bGwsInJlbWFyayI6bnVsbCwiZGlzcGxheU9yZGVyIjpudWxsfSx7ImJpbGxlcklkIjpudWxsLCJpdGVtSWQiOiI0ODUzOTA4OCIsIml0ZW1OYW1lIjpudWxsLCJwcmljZSI6MjAwMDAsInNlcmlhbE5vIjozLCJzdXNwZW5kIjpmYWxzZSwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsInJld2FyZHNBbW91bnRUZXh0IjpudWxsLCJyZW1hcmsiOm51bGwsImRpc3BsYXlPcmRlciI6bnVsbH0seyJiaWxsZXJJZCI6bnVsbCwiaXRlbUlkIjoiNDg1MzkxMDUiLCJpdGVtTmFtZSI6bnVsbCwicHJpY2UiOjUwMDAwLCJzZXJpYWxObyI6NCwic3VzcGVuZCI6ZmFsc2UsIm1pbkFtb3VudCI6bnVsbCwibWF4QW1vdW50IjpudWxsLCJyZXdhcmRzQW1vdW50VGV4dCI6bnVsbCwicmVtYXJrIjpudWxsLCJkaXNwbGF5T3JkZXIiOm51bGx9LHsiYmlsbGVySWQiOm51bGwsIml0ZW1JZCI6IjQ4NTM5MTIzIiwiaXRlbU5hbWUiOm51bGwsInByaWNlIjoxMDAwMDAsInNlcmlhbE5vIjo1LCJzdXNwZW5kIjpmYWxzZSwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsInJld2FyZHNBbW91bnRUZXh0IjpudWxsLCJyZW1hcmsiOm51bGwsImRpc3BsYXlPcmRlciI6bnVsbH0seyJiaWxsZXJJZCI6bnVsbCwiaXRlbUlkIjoiNDg1MzkxMzYiLCJpdGVtTmFtZSI6bnVsbCwicHJpY2UiOjIwMDAwMCwic2VyaWFsTm8iOjYsInN1c3BlbmQiOmZhbHNlLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwicmV3YXJkc0Ftb3VudFRleHQiOm51bGwsInJlbWFyayI6bnVsbCwiZGlzcGxheU9yZGVyIjpudWxsfV19LHsiYmlsbGVySWQiOiJHTE8iLCJiaWxsZXJOYW1lIjoiR0xPIiwiYmlsbGVySWNvbiI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWltYWdlLXByb2QuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNzE4MjY3MDAxODI0MjAtR2xvYmFjb20ucG5nIiwibWluQW1vdW50Ijo1MDAwLCJtYXhBbW91bnQiOjUwMDAwMDAsInRpcHMiOiIiLCJ1c2VySWRUaXBzIjoiRW50ZXIgUGhvbmUgTnVtYmVyIiwiYW10VGlwcyI6bnVsbCwiYmlsbGVyVGlwVXJsIjpudWxsLCJkYXJrVGlwVXJsIjpudWxsLCJzdXNwZW5kIjpmYWxzZSwic3RhdHVzIjoxLCJpdGVtTGlzdCI6W3siYmlsbGVySWQiOm51bGwsIml0ZW1JZCI6IjQ4NTM5MjgwIiwiaXRlbU5hbWUiOm51bGwsInByaWNlIjo1MDAwLCJzZXJpYWxObyI6MSwic3VzcGVuZCI6ZmFsc2UsIm1pbkFtb3VudCI6bnVsbCwibWF4QW1vdW50IjpudWxsLCJyZXdhcmRzQW1vdW50VGV4dCI6bnVsbCwicmVtYXJrIjpudWxsLCJkaXNwbGF5T3JkZXIiOm51bGx9LHsiYmlsbGVySWQiOm51bGwsIml0ZW1JZCI6IjQ4NTM5MjY2IiwiaXRlbU5hbWUiOm51bGwsInByaWNlIjoxMDAwMCwic2VyaWFsTm8iOjIsInN1c3BlbmQiOmZhbHNlLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwicmV3YXJkc0Ftb3VudFRleHQiOm51bGwsInJlbWFyayI6bnVsbCwiZGlzcGxheU9yZGVyIjpudWxsfSx7ImJpbGxlcklkIjpudWxsLCJpdGVtSWQiOiI0ODUzOTI0MCIsIml0ZW1OYW1lIjpudWxsLCJwcmljZSI6MjAwMDAsInNlcmlhbE5vIjozLCJzdXNwZW5kIjpmYWxzZSwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsInJld2FyZHNBbW91bnRUZXh0IjpudWxsLCJyZW1hcmsiOm51bGwsImRpc3BsYXlPcmRlciI6bnVsbH0seyJiaWxsZXJJZCI6bnVsbCwiaXRlbUlkIjoiNDg1MzkyMjciLCJpdGVtTmFtZSI6bnVsbCwicHJpY2UiOjUwMDAwLCJzZXJpYWxObyI6NCwic3VzcGVuZCI6ZmFsc2UsIm1pbkFtb3VudCI6bnVsbCwibWF4QW1vdW50IjpudWxsLCJyZXdhcmRzQW1vdW50VGV4dCI6bnVsbCwicmVtYXJrIjpudWxsLCJkaXNwbGF5T3JkZXIiOm51bGx9LHsiYmlsbGVySWQiOm51bGwsIml0ZW1JZCI6IjQ4NTM5MjExIiwiaXRlbU5hbWUiOm51bGwsInByaWNlIjoxMDAwMDAsInNlcmlhbE5vIjo1LCJzdXNwZW5kIjpmYWxzZSwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsInJld2FyZHNBbW91bnRUZXh0IjpudWxsLCJyZW1hcmsiOm51bGwsImRpc3BsYXlPcmRlciI6bnVsbH0seyJiaWxsZXJJZCI6bnVsbCwiaXRlbUlkIjoiNDg1MzkxNDgiLCJpdGVtTmFtZSI6bnVsbCwicHJpY2UiOjIwMDAwMCwic2VyaWFsTm8iOjYsInN1c3BlbmQiOmZhbHNlLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwicmV3YXJkc0Ftb3VudFRleHQiOm51bGwsInJlbWFyayI6bnVsbCwiZGlzcGxheU9yZGVyIjpudWxsfV19LHsiYmlsbGVySWQiOiJOSU5FTU9CSUxFIiwiYmlsbGVyTmFtZSI6Ijltb2JpbGUiLCJiaWxsZXJJY29uIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtaW1hZ2UtcHJvZC5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE3MTgyNjY5NzI1MTkxMy05TU9CSUxFLnBuZyIsIm1pbkFtb3VudCI6NTAwMCwibWF4QW1vdW50Ijo1MDAwMDAwLCJ0aXBzIjoiIiwidXNlcklkVGlwcyI6IkVudGVyIFBob25lIE51bWJlciIsImFtdFRpcHMiOm51bGwsImJpbGxlclRpcFVybCI6bnVsbCwiZGFya1RpcFVybCI6bnVsbCwic3VzcGVuZCI6ZmFsc2UsInN0YXR1cyI6MSwiaXRlbUxpc3QiOlt7ImJpbGxlcklkIjpudWxsLCJpdGVtSWQiOiI0ODUzOTMzMyIsIml0ZW1OYW1lIjpudWxsLCJwcmljZSI6NTAwMCwic2VyaWFsTm8iOjEsInN1c3BlbmQiOmZhbHNlLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwicmV3YXJkc0Ftb3VudFRleHQiOm51bGwsInJlbWFyayI6bnVsbCwiZGlzcGxheU9yZGVyIjpudWxsfSx7ImJpbGxlcklkIjpudWxsLCJpdGVtSWQiOiI0ODUzOTM3MCIsIml0ZW1OYW1lIjpudWxsLCJwcmljZSI6MTAwMDAsInNlcmlhbE5vIjoyLCJzdXNwZW5kIjpmYWxzZSwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOjUwMDAwMDAsInJld2FyZHNBbW91bnRUZXh0IjpudWxsLCJyZW1hcmsiOm51bGwsImRpc3BsYXlPcmRlciI6bnVsbH0seyJiaWxsZXJJZCI6bnVsbCwiaXRlbUlkIjoiNDg1Mzk0MTgiLCJpdGVtTmFtZSI6bnVsbCwicHJpY2UiOjIwMDAwLCJzZXJpYWxObyI6Mywic3VzcGVuZCI6ZmFsc2UsIm1pbkFtb3VudCI6bnVsbCwibWF4QW1vdW50IjowLCJyZXdhcmRzQW1vdW50VGV4dCI6bnVsbCwicmVtYXJrIjpudWxsLCJkaXNwbGF5T3JkZXIiOm51bGx9LHsiYmlsbGVySWQiOm51bGwsIml0ZW1JZCI6IjQ4NTM5NDU3IiwiaXRlbU5hbWUiOm51bGwsInByaWNlIjo1MDAwMCwic2VyaWFsTm8iOjQsInN1c3BlbmQiOmZhbHNlLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6MCwicmV3YXJkc0Ftb3VudFRleHQiOm51bGwsInJlbWFyayI6bnVsbCwiZGlzcGxheU9yZGVyIjpudWxsfSx7ImJpbGxlcklkIjpudWxsLCJpdGVtSWQiOiI0ODUzOTc2MSIsIml0ZW1OYW1lIjpudWxsLCJwcmljZSI6MTAwMDAwLCJzZXJpYWxObyI6NSwic3VzcGVuZCI6ZmFsc2UsIm1pbkFtb3VudCI6bnVsbCwibWF4QW1vdW50IjowLCJyZXdhcmRzQW1vdW50VGV4dCI6bnVsbCwicmVtYXJrIjpudWxsLCJkaXNwbGF5T3JkZXIiOm51bGx9LHsiYmlsbGVySWQiOm51bGwsIml0ZW1JZCI6IjQ4NTM5ODAyIiwiaXRlbU5hbWUiOm51bGwsInByaWNlIjoyMDAwMDAsInNlcmlhbE5vIjo2LCJzdXNwZW5kIjpmYWxzZSwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsInJld2FyZHNBbW91bnRUZXh0IjpudWxsLCJyZW1hcmsiOm51bGwsImRpc3BsYXlPcmRlciI6bnVsbH1dfSx7ImJpbGxlcklkIjoiU01JTEUiLCJiaWxsZXJOYW1lIjoiU21pbGUiLCJiaWxsZXJJY29uIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtaW1hZ2UtcHJvZC5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE3MTgyNjcwNzkyMzQ5LVNtaWxlLnBuZyIsIm1pbkFtb3VudCI6NTAwMCwibWF4QW1vdW50Ijo1MDAwMDAwLCJ0aXBzIjoiIiwidXNlcklkVGlwcyI6IkVudGVyIFBob25lIE51bWJlciIsImFtdFRpcHMiOm51bGwsImJpbGxlclRpcFVybCI6bnVsbCwiZGFya1RpcFVybCI6bnVsbCwic3VzcGVuZCI6ZmFsc2UsInN0YXR1cyI6MSwiaXRlbUxpc3QiOlt7ImJpbGxlcklkIjpudWxsLCJpdGVtSWQiOiI3MTYwMTE4NzczNzU5MTAiLCJpdGVtTmFtZSI6bnVsbCwicHJpY2UiOjUwMDAsInNlcmlhbE5vIjoxLCJzdXNwZW5kIjpmYWxzZSwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsInJld2FyZHNBbW91bnRUZXh0IjpudWxsLCJyZW1hcmsiOm51bGwsImRpc3BsYXlPcmRlciI6bnVsbH0seyJiaWxsZXJJZCI6bnVsbCwiaXRlbUlkIjoiNzE2MDExODc3Mzc1OTExIiwiaXRlbU5hbWUiOm51bGwsInByaWNlIjoxMDAwMCwic2VyaWFsTm8iOjIsInN1c3BlbmQiOmZhbHNlLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwicmV3YXJkc0Ftb3VudFRleHQiOm51bGwsInJlbWFyayI6bnVsbCwiZGlzcGxheU9yZGVyIjpudWxsfSx7ImJpbGxlcklkIjpudWxsLCJpdGVtSWQiOiI3MTYwMTE4NzczNzU5MTIiLCJpdGVtTmFtZSI6bnVsbCwicHJpY2UiOjIwMDAwLCJzZXJpYWxObyI6Mywic3VzcGVuZCI6ZmFsc2UsIm1pbkFtb3VudCI6bnVsbCwibWF4QW1vdW50IjpudWxsLCJyZXdhcmRzQW1vdW50VGV4dCI6bnVsbCwicmVtYXJrIjpudWxsLCJkaXNwbGF5T3JkZXIiOm51bGx9LHsiYmlsbGVySWQiOm51bGwsIml0ZW1JZCI6IjcxNjAxMTg3NzM3NTkxNCIsIml0ZW1OYW1lIjpudWxsLCJwcmljZSI6NTAwMDAsInNlcmlhbE5vIjo0LCJzdXNwZW5kIjpmYWxzZSwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsInJld2FyZHNBbW91bnRUZXh0IjpudWxsLCJyZW1hcmsiOm51bGwsImRpc3BsYXlPcmRlciI6bnVsbH0seyJiaWxsZXJJZCI6bnVsbCwiaXRlbUlkIjoiNzE2MDExODc3Mzc1OTE1IiwiaXRlbU5hbWUiOm51bGwsInByaWNlIjoxMDAwMDAsInNlcmlhbE5vIjo1LCJzdXNwZW5kIjpmYWxzZSwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsInJld2FyZHNBbW91bnRUZXh0IjpudWxsLCJyZW1hcmsiOm51bGwsImRpc3BsYXlPcmRlciI6bnVsbH0seyJiaWxsZXJJZCI6bnVsbCwiaXRlbUlkIjoiOTczNDY2ODA2NzAxOTMwIiwiaXRlbU5hbWUiOm51bGwsInByaWNlIjoyMDAwMDAsInNlcmlhbE5vIjo2LCJzdXNwZW5kIjpmYWxzZSwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsInJld2FyZHNBbW91bnRUZXh0IjpudWxsLCJyZW1hcmsiOm51bGwsImRpc3BsYXlPcmRlciI6bnVsbH1dfV0sInN0YXR1cyI6dHJ1ZX0=", "encoding": "base64"}, "redirectURL": null, "headersSize": 69, "bodySize": 680}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 544, "receive": 0}}, {"startedDateTime": "2024-06-24T10:28:04.82+08:00", "time": 248, "request": {"method": "POST", "url": "https://ng-partner.palmpay.app/api/scene-business-product/common/query/auto-onoff-sale-info", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/scene-business-product/common/query/auto-onoff-sale-info"}, {"name": ":authority", "value": "ng-partner.palmpay.app"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "PARTNER_c77d77a4a7568dd8012345"}, {"name": "pp_black_box", "value": "qGPVy1719194428xCN3zTAnl68"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "4.2.0&606201849"}, {"name": "pp_timestamp", "value": "1719196084768"}, {"name": "pp_country_code", "value": "NG"}, {"name": "user-agent", "value": "PalmPay Business/4.2.0&606201849 (Android 13)"}, {"name": "pp_lat", "value": "22.576576576576578"}, {"name": "pp_lng", "value": "113.93863889459115"}, {"name": "pp_token", "value": "fdb0735f-d780-4f90-992c-02b0236b5621"}, {"name": "pp_req_sign", "value": "BByFw%2BMm0tY84liwEiBZJaZCZl44cpSVkj883m3XoW8MieWfp6%2BC5vN5tQY6WbIeKQzxy9pnsQ%2FavEdr3EHJ9VfrHYNk2KNrDlDytkoe9SkG0NK1n608IJH886APGN%2FeFtA95EblfcJ%2ByIBCYrgHvLyA9YRQ1E1s3QqGT9BYXXE%3D"}, {"name": "pp_req_sign_2", "value": "C%2B4BI1lNc6eBHTU2FMlwZx0Mm4gXybFCXZuimQpS7ADxiwZFRByqeX9ccs44I45AfVl44Oslq2KBLzpbaVH5J%2F4hMQeBjOYrCH9C8Swb2Obq2C71HMarbNbrMN7igruB%2F9gCyRrMqk%2Ba9QcD8xDwLu0QortKQ5jeXJBuCCu4O5U%3D"}, {"name": "accept-encoding", "value": "br,gzip"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "21"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"categoryId\":[\"10\"]}"}, "headersSize": 647, "bodySize": 21}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 24 Jun 2024 02:28:05 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "eagleeye-traceid", "value": "af1fa9342c2c061dd47d92a275b6205f"}, {"name": "x-envoy-upstream-service-time", "value": "39"}, {"name": "x-envoy-decorator-operation", "value": "mgw-core.prod.svc.cluster.local:80/*"}, {"name": "cf-cache-status", "value": "DYNAMIC"}, {"name": "server", "value": "cloudflare"}, {"name": "cf-ray", "value": "8989484f6a8e9586-LHR"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 67, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbXSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 80, "bodySize": 84}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 248, "receive": 0}}, {"startedDateTime": "2024-06-24T10:28:04.82+08:00", "time": 275, "request": {"method": "GET", "url": "https://ng-partner.palmpay.app/api/scene-business-product/common/resolveNetworkByMobileNo?mobileNo=***********", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/scene-business-product/common/resolveNetworkByMobileNo?mobileNo=***********"}, {"name": ":authority", "value": "ng-partner.palmpay.app"}, {"name": ":scheme", "value": "https"}, {"name": "no-token", "value": "yes"}, {"name": "pp_device_id", "value": "PARTNER_c77d77a4a7568dd8012345"}, {"name": "pp_black_box", "value": "qGPVw17191944284CN3zTAnlH8"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "4.2.0&606201849"}, {"name": "pp_timestamp", "value": "1719196084761"}, {"name": "pp_country_code", "value": "NG"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay Business/4.2.0&606201849 (Android 13)"}, {"name": "pp_lat", "value": "22.576576576576578"}, {"name": "pp_lng", "value": "113.93863889459115"}, {"name": "pp_token", "value": "fdb0735f-d780-4f90-992c-02b0236b5621"}, {"name": "pp_req_sign", "value": "pZnxHNUbutNS4FROHlwDKFIapR1mgki9%2FRgSdCzoWfqmn8wcOPQlmlglzcltG%2B2ogiVnvJdnqlTeDYnvxeDveDk6VwvNmXlV8qrUmhk8zs80wfBbOzy0eNBL42OgNe11CbyuPq7dZa1TmEgizEjxVHxHnRga7LbAl2gnP84VOz0%3D"}, {"name": "pp_req_sign_2", "value": "pZnxHNUbutNS4FROHlwDKFIapR1mgki9%2FRgSdCzoWfqmn8wcOPQlmlglzcltG%2B2ogiVnvJdnqlTeDYnvxeDveDk6VwvNmXlV8qrUmhk8zs80wfBbOzy0eNBL42OgNe11CbyuPq7dZa1TmEgizEjxVHxHnRga7LbAl2gnP84VOz0%3D"}, {"name": "accept-encoding", "value": "br,gzip"}, {"name": "if-modified-since", "value": "Mon, 24 Jun 2024 02:27:23 GMT"}], "queryString": [{"name": "mobileNo", "value": "***********"}], "headersSize": 413, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 24 Jun 2024 02:28:05 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "eagleeye-traceid", "value": "f024195ae9386ca09db336a37e04133c"}, {"name": "x-envoy-upstream-service-time", "value": "62"}, {"name": "x-envoy-decorator-operation", "value": "mgw-core.prod.svc.cluster.local:80/*"}, {"name": "cf-cache-status", "value": "DYNAMIC"}, {"name": "server", "value": "cloudflare"}, {"name": "cf-ray", "value": "8989484f6a8c9586-LHR"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 73, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjoiQUlSVEVMIiwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 57, "bodySize": 90}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 274, "receive": 1}}, {"startedDateTime": "2024-06-24T10:28:04.82+08:00", "time": 273, "request": {"method": "GET", "url": "https://ng-partner.palmpay.app/api/scene-business-product/airtime/item/tips?billerId=AIRTEL", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/scene-business-product/airtime/item/tips?billerId=AIRTEL"}, {"name": ":authority", "value": "ng-partner.palmpay.app"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "PARTNER_c77d77a4a7568dd8012345"}, {"name": "pp_black_box", "value": "qGPVX1719194428hCN3zTAnl58"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "4.2.0&606201849"}, {"name": "pp_timestamp", "value": "1719196084778"}, {"name": "pp_country_code", "value": "NG"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay Business/4.2.0&606201849 (Android 13)"}, {"name": "pp_lat", "value": "22.576576576576578"}, {"name": "pp_lng", "value": "113.93863889459115"}, {"name": "pp_token", "value": "fdb0735f-d780-4f90-992c-02b0236b5621"}, {"name": "pp_req_sign", "value": "QErOH5icd9YXoDccUR0iqDmE1zW2temBKyPOfhSClqZ%2FnuTfICg7MvSwR8MgRStgqhXACy5Dy%2FlNGkKa9ut6XwPuWnAynnDsj%2FREMANpjQhWAlwbKg4ofefX7p%2Fub%2FrE4R1O6aEWxdTFuODhs1ewzhwqjWFBQxOiLJjoziiBPr4%3D"}, {"name": "pp_req_sign_2", "value": "QErOH5icd9YXoDccUR0iqDmE1zW2temBKyPOfhSClqZ%2FnuTfICg7MvSwR8MgRStgqhXACy5Dy%2FlNGkKa9ut6XwPuWnAynnDsj%2FREMANpjQhWAlwbKg4ofefX7p%2Fub%2FrE4R1O6aEWxdTFuODhs1ewzhwqjWFBQxOiLJjoziiBPr4%3D"}, {"name": "accept-encoding", "value": "br,gzip"}, {"name": "if-modified-since", "value": "Mon, 24 Jun 2024 02:27:23 GMT"}], "queryString": [{"name": "billerId", "value": "AIRTEL"}], "headersSize": 411, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 24 Jun 2024 02:28:05 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "eagleeye-traceid", "value": "9000971e0e4f30ff8a8d650b53174155"}, {"name": "x-envoy-upstream-service-time", "value": "63"}, {"name": "x-envoy-decorator-operation", "value": "mgw-core.prod.svc.cluster.local:80/*"}, {"name": "cf-cache-status", "value": "DYNAMIC"}, {"name": "server", "value": "cloudflare"}, {"name": "cf-ray", "value": "8989484f6a919586-LHR"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 67, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbXSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 57, "bodySize": 84}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 272, "receive": 1}}]}}