# NOTE: Generated By HttpRunner v4.3.5
# FROM: testcases\Bussiness\data\Bussiness_data_20240410_test.json
import pytest
from httprunner import HttpRunner, Config, Step, RunRequest, Parameters


class TestResolveNetworkByMobileNoTest(HttpRunner):
    @pytest.mark.parametrize(
        "param", Parameters({"biller-phone": "${P(CSV_DATA/biller_phone.csv)}"})
    )
    def test_start(self, param):
        super().test_start(param)

    config = Config("Bussiness端:根据手机号解析运营商").base_url("${ENV(base_url_B)}")

    teststeps = [
        Step(
            RunRequest("请求标题：B端根据手机号解析运营商")
            .setup_hook('${setup_hooks_request_business($request)}')
            .get(
                "/api/scene-business-product/common/resolveNetworkByMobileNo"
            )
            # .with_params(**{"mobileNo": "***********"})
            .with_params(**{"mobileNo": "${phone}"})
            .with_headers(
                **{
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Wed, 28 Feb 2024 06:30:02 GMT",
                    # "pp_channel": "googleplay",
                    # "pp_client_ver": "5.3.0&602280702",
                    # "pp_device_id": "bee648d4b0a5ab70012345",
                    # "pp_device_type": "ANDROID",
                    # "pp_req_sign": "ZDfAAOuQ4o8I8UVzS80MrwGv7ZHRHwPyZL27ZuWCbkM45wxQ41DMokcYx3P7%2BNZxuIlgjDZqPKlOe0u3lCiOeN6CxNqgxN%2FUCyHrFYCjNqHQt7A%2FQsSt6c9rog0j6aF1rZTK7K5kqTESmpvVlbPOkUh4cmjHVxWlotIkO3%2FO2aQ%3D",
                    # "pp_timestamp": "${get_pp_timestamp()}",
                    # "pp_token": "22761d93-cea3-47cf-a8f9-a8087e540409",
                    "user-agent": "PalmPay/5.3.0&602280702 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            # .assert_equal("body.data", "${biller}", "assert response body data")  # 存在实际响应：9MOBILE，预期：NINEMOBILE的异常
            # 针对 9mobile 的特殊处理
            .assert_contained_by("body.data", ["${biller}", "9MOBILE"], "assert response body data")
        ),
    ]


if __name__ == "__main__":
    TestResolveNetworkByMobileNoTest().test_start()
