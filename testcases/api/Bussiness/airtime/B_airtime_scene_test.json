{"config": {"name": "testcase description"}, "teststeps": [{"name": "", "request": {"method": "GET", "url": "https://ng-partner.palmpay.app/api/scene-business-product/airtime/menuList", "headers": {":authority": "ng-partner.palmpay.app", ":method": "GET", ":path": "/api/scene-business-product/airtime/menuList", ":scheme": "https", "accept-encoding": "br,gzip", "content-type": "application/json", "if-modified-since": "Mon, 24 Jun 2024 02:27:23 GMT", "pp_black_box": "qGPVo1719194428LCN3zTAnlm8", "pp_client_ver": "4.2.0&606201849", "pp_country_code": "NG", "pp_device_id": "PARTNER_c77d77a4a7568dd8012345", "pp_device_type": "ANDROID", "pp_lat": "22.576576576576578", "pp_lng": "113.93863889459115", "pp_req_sign": "XWfsDY6bVG34q3YBiUHBpxUUPzb5DN2ITif%2FJxm1yBaVDqCozypDnMpLjxavqLCd9neF2J3A4mMqGJPAZ9c0k5TXlzN5ABl5nViOxbOr07T0xBrcI41g8LTysaqW5RcBVVtG6EdqZbbSWrRT3TyvN%2Bxn46X7vvNXTi0kJDpGyYo%3D", "pp_req_sign_2": "XWfsDY6bVG34q3YBiUHBpxUUPzb5DN2ITif%2FJxm1yBaVDqCozypDnMpLjxavqLCd9neF2J3A4mMqGJPAZ9c0k5TXlzN5ABl5nViOxbOr07T0xBrcI41g8LTysaqW5RcBVVtG6EdqZbbSWrRT3TyvN%2Bxn46X7vvNXTi0kJDpGyYo%3D", "pp_timestamp": "1719196084726", "pp_token": "fdb0735f-d780-4f90-992c-02b0236b5621", "user-agent": "PalmPay Business/4.2.0&606201849 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}, {"check": "body.status", "assert": "equals", "expect": true, "msg": "assert response body status"}]}, {"name": "", "request": {"method": "POST", "url": "https://ng-partner.palmpay.app/api/scene-business-product/common/query/auto-onoff-sale-info", "headers": {":authority": "ng-partner.palmpay.app", ":method": "POST", ":path": "/api/scene-business-product/common/query/auto-onoff-sale-info", ":scheme": "https", "accept-encoding": "br,gzip", "content-length": "21", "content-type": "application/json; charset=UTF-8", "pp_black_box": "qGPVy1719194428xCN3zTAnl68", "pp_client_ver": "4.2.0&606201849", "pp_country_code": "NG", "pp_device_id": "PARTNER_c77d77a4a7568dd8012345", "pp_device_type": "ANDROID", "pp_lat": "22.576576576576578", "pp_lng": "113.93863889459115", "pp_req_sign": "BByFw%2BMm0tY84liwEiBZJaZCZl44cpSVkj883m3XoW8MieWfp6%2BC5vN5tQY6WbIeKQzxy9pnsQ%2FavEdr3EHJ9VfrHYNk2KNrDlDytkoe9SkG0NK1n608IJH886APGN%2FeFtA95EblfcJ%2ByIBCYrgHvLyA9YRQ1E1s3QqGT9BYXXE%3D", "pp_req_sign_2": "C%2B4BI1lNc6eBHTU2FMlwZx0Mm4gXybFCXZuimQpS7ADxiwZFRByqeX9ccs44I45AfVl44Oslq2KBLzpbaVH5J%2F4hMQeBjOYrCH9C8Swb2Obq2C71HMarbNbrMN7igruB%2F9gCyRrMqk%2Ba9QcD8xDwLu0QortKQ5jeXJBuCCu4O5U%3D", "pp_timestamp": "1719196084768", "pp_token": "fdb0735f-d780-4f90-992c-02b0236b5621", "user-agent": "PalmPay Business/4.2.0&606201849 (Android 13)"}, "body": {"categoryId": ["10"]}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}, {"check": "body.status", "assert": "equals", "expect": true, "msg": "assert response body status"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-partner.palmpay.app/api/scene-business-product/common/resolveNetworkByMobileNo", "params": {"mobileNo": "***********"}, "headers": {":authority": "ng-partner.palmpay.app", ":method": "GET", ":path": "/api/scene-business-product/common/resolveNetworkByMobileNo?mobileNo=***********", ":scheme": "https", "accept-encoding": "br,gzip", "content-type": "application/json", "if-modified-since": "Mon, 24 Jun 2024 02:27:23 GMT", "no-token": "yes", "pp_black_box": "qGPVw17191944284CN3zTAnlH8", "pp_client_ver": "4.2.0&606201849", "pp_country_code": "NG", "pp_device_id": "PARTNER_c77d77a4a7568dd8012345", "pp_device_type": "ANDROID", "pp_lat": "22.576576576576578", "pp_lng": "113.93863889459115", "pp_req_sign": "pZnxHNUbutNS4FROHlwDKFIapR1mgki9%2FRgSdCzoWfqmn8wcOPQlmlglzcltG%2B2ogiVnvJdnqlTeDYnvxeDveDk6VwvNmXlV8qrUmhk8zs80wfBbOzy0eNBL42OgNe11CbyuPq7dZa1TmEgizEjxVHxHnRga7LbAl2gnP84VOz0%3D", "pp_req_sign_2": "pZnxHNUbutNS4FROHlwDKFIapR1mgki9%2FRgSdCzoWfqmn8wcOPQlmlglzcltG%2B2ogiVnvJdnqlTeDYnvxeDveDk6VwvNmXlV8qrUmhk8zs80wfBbOzy0eNBL42OgNe11CbyuPq7dZa1TmEgizEjxVHxHnRga7LbAl2gnP84VOz0%3D", "pp_timestamp": "1719196084761", "pp_token": "fdb0735f-d780-4f90-992c-02b0236b5621", "user-agent": "PalmPay Business/4.2.0&606201849 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.data", "assert": "equals", "expect": "AIRTEL", "msg": "assert response body data"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}, {"check": "body.status", "assert": "equals", "expect": true, "msg": "assert response body status"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-partner.palmpay.app/api/scene-business-product/airtime/item/tips", "params": {"billerId": "AIRTEL"}, "headers": {":authority": "ng-partner.palmpay.app", ":method": "GET", ":path": "/api/scene-business-product/airtime/item/tips?billerId=AIRTEL", ":scheme": "https", "accept-encoding": "br,gzip", "content-type": "application/json", "if-modified-since": "Mon, 24 Jun 2024 02:27:23 GMT", "pp_black_box": "qGPVX1719194428hCN3zTAnl58", "pp_client_ver": "4.2.0&606201849", "pp_country_code": "NG", "pp_device_id": "PARTNER_c77d77a4a7568dd8012345", "pp_device_type": "ANDROID", "pp_lat": "22.576576576576578", "pp_lng": "113.93863889459115", "pp_req_sign": "QErOH5icd9YXoDccUR0iqDmE1zW2temBKyPOfhSClqZ%2FnuTfICg7MvSwR8MgRStgqhXACy5Dy%2FlNGkKa9ut6XwPuWnAynnDsj%2FREMANpjQhWAlwbKg4ofefX7p%2Fub%2FrE4R1O6aEWxdTFuODhs1ewzhwqjWFBQxOiLJjoziiBPr4%3D", "pp_req_sign_2": "QErOH5icd9YXoDccUR0iqDmE1zW2temBKyPOfhSClqZ%2FnuTfICg7MvSwR8MgRStgqhXACy5Dy%2FlNGkKa9ut6XwPuWnAynnDsj%2FREMANpjQhWAlwbKg4ofefX7p%2Fub%2FrE4R1O6aEWxdTFuODhs1ewzhwqjWFBQxOiLJjoziiBPr4%3D", "pp_timestamp": "1719196084778", "pp_token": "fdb0735f-d780-4f90-992c-02b0236b5621", "user-agent": "PalmPay Business/4.2.0&606201849 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}, {"check": "body.status", "assert": "equals", "expect": true, "msg": "assert response body status"}]}]}