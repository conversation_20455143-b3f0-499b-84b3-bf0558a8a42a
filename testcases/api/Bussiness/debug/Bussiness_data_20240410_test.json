{"config": {"name": "testcase description"}, "teststeps": [{"name": "", "request": {"method": "GET", "url": "https://ng-pa-apptest.transspay.net/api/scene-business-product/recharge/records/latest", "headers": {":authority": "ng-pa-apptest.transspay.net", ":method": "GET", ":path": "/api/scene-business-product/recharge/records/latest", ":scheme": "https", "accept-encoding": "br,gzip", "content-type": "application/json", "pp_black_box": "jGPVt1712735392BwsqAvsdYP7", "pp_client_ver": "3.12.4&604021107", "pp_country_code": "NG", "pp_device_id": "PARTNER_c77d77a4a7568dd8012345", "pp_device_type": "ANDROID", "pp_req_sign": "FTA%2Fe81bKwOENGR0CVfdIHS25BgWq3llwVB4u1ZK9J4j90%2BkDjquhi6v%2BLd4YKdLArMGCGY2%2BfBWUU8At3oRY2Rj3u6aAwpoJifWoLcvWSwdj6IjkO0GA2sPf01dFU%2BdLWKxqJ7OUDmBdfmfSen3b7NwB0eZDLf9ksE9LnTvPbQ%3D", "pp_req_sign_2": "FTA%2Fe81bKwOENGR0CVfdIHS25BgWq3llwVB4u1ZK9J4j90%2BkDjquhi6v%2BLd4YKdLArMGCGY2%2BfBWUU8At3oRY2Rj3u6aAwpoJifWoLcvWSwdj6IjkO0GA2sPf01dFU%2BdLWKxqJ7OUDmBdfmfSen3b7NwB0eZDLf9ksE9LnTvPbQ%3D", "pp_timestamp": "1712735463394", "pp_token": "d1a71b76-e7f0-4c31-8d9e-5d7f6ca655c9", "user-agent": "PalmPay Business/3.12.4&604021107 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json; charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.data", "assert": "equals", "expect": false, "msg": "assert response body data"}, {"check": "body.respCode", "assert": "equals", "expect": "CFRONT_000016", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "invalid url router", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-pa-apptest.transspay.net/api/scene-business-product/common/resolveNetworkByMobileNo", "params": {"mobileNo": "***********"}, "headers": {":authority": "ng-pa-apptest.transspay.net", ":method": "GET", ":path": "/api/scene-business-product/common/resolveNetworkByMobileNo?mobileNo=***********", ":scheme": "https", "accept-encoding": "br,gzip", "content-type": "application/json", "no-token": "yes", "pp_black_box": "jGPVD1712735392WwsqAvsdYT7", "pp_client_ver": "3.12.4&604021107", "pp_country_code": "NG", "pp_device_id": "PARTNER_c77d77a4a7568dd8012345", "pp_device_type": "ANDROID", "pp_req_sign": "EI53e%2B45HlY2aQvLLeajvccdkbJ4B1dmJYKqIWZBeglQiBj5lAKu81La6Y27K4QyKG67toiFs3s1TnvFcs0fU0lY25J0dFNGZBhOcasg%2FFLM02J8JDvnX7%2BldiKZV6kyXBEtIAKnn9sIW0YjsvaSBbnX9g%2BqgCMi7czxApfRoq0%3D", "pp_req_sign_2": "EI53e%2B45HlY2aQvLLeajvccdkbJ4B1dmJYKqIWZBeglQiBj5lAKu81La6Y27K4QyKG67toiFs3s1TnvFcs0fU0lY25J0dFNGZBhOcasg%2FFLM02J8JDvnX7%2BldiKZV6kyXBEtIAKnn9sIW0YjsvaSBbnX9g%2BqgCMi7czxApfRoq0%3D", "pp_timestamp": "1712735463400", "pp_token": "d1a71b76-e7f0-4c31-8d9e-5d7f6ca655c9", "user-agent": "PalmPay Business/3.12.4&604021107 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.data", "assert": "equals", "expect": "AIRTEL", "msg": "assert response body data"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "POST", "url": "https://ng-pa-apptest.transspay.net/api/scene-business-product/common/query/auto-onoff-sale-info", "headers": {":authority": "ng-pa-apptest.transspay.net", ":method": "POST", ":path": "/api/scene-business-product/common/query/auto-onoff-sale-info", ":scheme": "https", "accept-encoding": "br,gzip", "content-length": "20", "content-type": "application/json; charset=UTF-8", "pp_black_box": "jGPVZ1712735392UwsqAvsdYW7", "pp_client_ver": "3.12.4&604021107", "pp_country_code": "NG", "pp_device_id": "PARTNER_c77d77a4a7568dd8012345", "pp_device_type": "ANDROID", "pp_req_sign": "xRUTNG8xVtN61vAsew0JE3v9aIgNGF0i0jxwl6u5x%2FZ%2FFX2GUcJ1IbZxR1C5ISPEBdMM4iKw%2F9BsogAmyT0i0oGtJl%2BxHVFdMoIrMvtag63IScrbtPbfO%2FghR2R%2BxH0rCk%2FvyHWBmMeCDrkbncpmDtvraYKd9JQXGuWsYZxs6AM%3D", "pp_req_sign_2": "w4g%2BPd4yszBk%2Bv9q9GAVNaQDBYUdU2UR1HCffwIxQiVOwfTMs5O3hTnyRytVIb6ewHfRQ%2BMHohMz9sz%2B1Nt893BfPSeUa4SrxOmv6Z3UCzLe2N%2BNm66AGpGSIPt%2BJZUhPbhWMcBdAwmdXK1dMARTd7JBXX3nzGkf%2BKEo5yqdF9g%3D", "pp_timestamp": "1712735463431", "pp_token": "d1a71b76-e7f0-4c31-8d9e-5d7f6ca655c9", "user-agent": "PalmPay Business/3.12.4&604021107 (Android 13)"}, "body": {"categoryId": ["6"]}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.data", "assert": "equals", "expect": null, "msg": "assert response body data"}, {"check": "body.respCode", "assert": "equals", "expect": "00000011", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "An unknown error occurred in the scene-business-product service", "msg": "assert response body respMsg"}, {"check": "body.status", "assert": "equals", "expect": false, "msg": "assert response body status"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-pa-apptest.transspay.net/api/scene-business-product/data-bundle/menuList", "headers": {":authority": "ng-pa-apptest.transspay.net", ":method": "GET", ":path": "/api/scene-business-product/data-bundle/menuList", ":scheme": "https", "accept-encoding": "br,gzip", "content-type": "application/json", "no-token": "yes", "pp_black_box": "jGPV71712735392QwsqAvsdYk7", "pp_client_ver": "3.12.4&604021107", "pp_country_code": "NG", "pp_device_id": "PARTNER_c77d77a4a7568dd8012345", "pp_device_type": "ANDROID", "pp_req_sign": "EI53e%2B45HlY2aQvLLeajvccdkbJ4B1dmJYKqIWZBeglQiBj5lAKu81La6Y27K4QyKG67toiFs3s1TnvFcs0fU0lY25J0dFNGZBhOcasg%2FFLM02J8JDvnX7%2BldiKZV6kyXBEtIAKnn9sIW0YjsvaSBbnX9g%2BqgCMi7czxApfRoq0%3D", "pp_req_sign_2": "EI53e%2B45HlY2aQvLLeajvccdkbJ4B1dmJYKqIWZBeglQiBj5lAKu81La6Y27K4QyKG67toiFs3s1TnvFcs0fU0lY25J0dFNGZBhOcasg%2FFLM02J8JDvnX7%2BldiKZV6kyXBEtIAKnn9sIW0YjsvaSBbnX9g%2BqgCMi7czxApfRoq0%3D", "pp_timestamp": "1712735463400", "pp_token": "d1a71b76-e7f0-4c31-8d9e-5d7f6ca655c9", "user-agent": "PalmPay Business/3.12.4&604021107 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-pa-apptest.transspay.net/api/scene-business-product/payBills", "params": {"location": "76"}, "headers": {":authority": "ng-pa-apptest.transspay.net", ":method": "GET", ":path": "/api/scene-business-product/payBills?location=76", ":scheme": "https", "accept-encoding": "br,gzip", "content-type": "application/json", "pp_black_box": "jGPVe1712735392TwsqAvsdYQ7", "pp_client_ver": "3.12.4&604021107", "pp_country_code": "NG", "pp_device_id": "PARTNER_c77d77a4a7568dd8012345", "pp_device_type": "ANDROID", "pp_req_sign": "c98GWSTsLy2H5ba1sMitQUxcygHHrn3ev4oAUw5KbRqbW8qeubzafYfNK1KjEe3QC9uG8SJFgqXCOI0snE1WGmnFfbx0%2Fgh5WJ5F6WnmD6P0%2F1xYNiijLSAE548d7TH6IQTm1J0gBOpMFGqDFdTZGx1j4P52XmJI8nf9CrG5c7M%3D", "pp_req_sign_2": "c98GWSTsLy2H5ba1sMitQUxcygHHrn3ev4oAUw5KbRqbW8qeubzafYfNK1KjEe3QC9uG8SJFgqXCOI0snE1WGmnFfbx0%2Fgh5WJ5F6WnmD6P0%2F1xYNiijLSAE548d7TH6IQTm1J0gBOpMFGqDFdTZGx1j4P52XmJI8nf9CrG5c7M%3D", "pp_timestamp": "1712735463443", "pp_token": "d1a71b76-e7f0-4c31-8d9e-5d7f6ca655c9", "user-agent": "PalmPay Business/3.12.4&604021107 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json; charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.data", "assert": "equals", "expect": false, "msg": "assert response body data"}, {"check": "body.respCode", "assert": "equals", "expect": "CFRONT_000016", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "invalid url router", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-pa-apptest.transspay.net/api/scene-business-product/discountPackage/batch", "params": {"billerId": "AIRTEL"}, "headers": {":authority": "ng-pa-apptest.transspay.net", ":method": "GET", ":path": "/api/scene-business-product/discountPackage/batch?billerId=AIRTEL", ":scheme": "https", "accept-encoding": "br,gzip", "content-type": "application/json", "pp_black_box": "jGPVn17127353922wsqAvsdYB7", "pp_client_ver": "3.12.4&604021107", "pp_country_code": "NG", "pp_device_id": "PARTNER_c77d77a4a7568dd8012345", "pp_device_type": "ANDROID", "pp_req_sign": "JOyacjynHE%2FBB4Di77q1qyBSJ%2BYJt8dTnuG%2F8qPHSRlTfxd9gAp%2BL0g6eMsURgvAJKyxEDZRLXnZruaIDlavQo3q8TrX4q0x3BTDY%2BoVxPWbbWs%2BiR0qfd%2Bt2adKEeJjsRJ8MZIDiYe8ZLi3rRGUwV9PNcijcWSipyrSAPTg%2Frc%3D", "pp_req_sign_2": "JOyacjynHE%2FBB4Di77q1qyBSJ%2BYJt8dTnuG%2F8qPHSRlTfxd9gAp%2BL0g6eMsURgvAJKyxEDZRLXnZruaIDlavQo3q8TrX4q0x3BTDY%2BoVxPWbbWs%2BiR0qfd%2Bt2adKEeJjsRJ8MZIDiYe8ZLi3rRGUwV9PNcijcWSipyrSAPTg%2Frc%3D", "pp_timestamp": "1712735465478", "pp_token": "d1a71b76-e7f0-4c31-8d9e-5d7f6ca655c9", "user-agent": "PalmPay Business/3.12.4&604021107 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json; charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.data", "assert": "equals", "expect": false, "msg": "assert response body data"}, {"check": "body.respCode", "assert": "equals", "expect": "CFRONT_000016", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "invalid url router", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-pa-apptest.transspay.net/api/scene-business-product/data-bundle/item/tips", "params": {"billerName": "AIRTEL"}, "headers": {":authority": "ng-pa-apptest.transspay.net", ":method": "GET", ":path": "/api/scene-business-product/data-bundle/item/tips?billerName=AIRTEL", ":scheme": "https", "accept-encoding": "br,gzip", "content-type": "application/json", "pp_black_box": "jGPVJ17127353923wsqAvsdYC7", "pp_client_ver": "3.12.4&604021107", "pp_country_code": "NG", "pp_device_id": "PARTNER_c77d77a4a7568dd8012345", "pp_device_type": "ANDROID", "pp_req_sign": "MrLZqurnEIUl%2Bu%2BYtYAAmyK87YN3y%2BYJWGNv4BmBXEG00m0NOtfVYVNKTmYk3TvqVxxg3e2uBPfFEFYeQuImlgn64iBqYjKlGjZiqKvSp88bZuJuQ%2BiI2KAkOC2NKCQ%2FwfucqWHChHDPiSKo4lmO2mBg%2FuC3xw0kNu1KqCHj7d8%3D", "pp_req_sign_2": "MrLZqurnEIUl%2Bu%2BYtYAAmyK87YN3y%2BYJWGNv4BmBXEG00m0NOtfVYVNKTmYk3TvqVxxg3e2uBPfFEFYeQuImlgn64iBqYjKlGjZiqKvSp88bZuJuQ%2BiI2KAkOC2NKCQ%2FwfucqWHChHDPiSKo4lmO2mBg%2FuC3xw0kNu1KqCHj7d8%3D", "pp_timestamp": "1712735465480", "pp_token": "d1a71b76-e7f0-4c31-8d9e-5d7f6ca655c9", "user-agent": "PalmPay Business/3.12.4&604021107 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-pa-apptest.transspay.net/api/scene-business-product/data-bundle/itemList", "params": {"billerName": "AIRTEL"}, "headers": {":authority": "ng-pa-apptest.transspay.net", ":method": "GET", ":path": "/api/scene-business-product/data-bundle/itemList?billerName=AIRTEL", ":scheme": "https", "accept-encoding": "br,gzip", "content-type": "application/json", "no-token": "yes", "pp_black_box": "jGPVL1712735392vwsqAvsdY47", "pp_client_ver": "3.12.4&604021107", "pp_country_code": "NG", "pp_device_id": "PARTNER_c77d77a4a7568dd8012345", "pp_device_type": "ANDROID", "pp_req_sign": "pHTDgi7yz6QC5eHnKMG1mj2OhHYM9FOtxTN9LeuK7hbL2RI%2FWX0J4%2B38zQbhwnATKsd%2FEnRXl65izZc2l5yRfg8CA9gbSTMoU9WD8SL4iADmQGYcRGhDf1UERoXRLipQjPMiYwR%2BuOf45FC4hGL3lS0WGgMPVP%2Fy0e1byO490ZE%3D", "pp_req_sign_2": "pHTDgi7yz6QC5eHnKMG1mj2OhHYM9FOtxTN9LeuK7hbL2RI%2FWX0J4%2B38zQbhwnATKsd%2FEnRXl65izZc2l5yRfg8CA9gbSTMoU9WD8SL4iADmQGYcRGhDf1UERoXRLipQjPMiYwR%2BuOf45FC4hGL3lS0WGgMPVP%2Fy0e1byO490ZE%3D", "pp_timestamp": "1712735465479", "pp_token": "d1a71b76-e7f0-4c31-8d9e-5d7f6ca655c9", "user-agent": "PalmPay Business/3.12.4&604021107 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}]}