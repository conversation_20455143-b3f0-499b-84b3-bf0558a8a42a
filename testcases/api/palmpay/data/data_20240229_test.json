{"config": {"name": "testcase description"}, "teststeps": [{"name": "", "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/cfront/airtime/recharge/records/latest", "headers": {":authority": "ng-apptest.transspay.net", ":method": "GET", ":path": "/api/cfront/airtime/recharge/records/latest", ":scheme": "https", "accept-encoding": "gzip", "content-type": "application/json", "if-modified-since": "Wed, 28 Feb 2024 06:30:02 GMT", "pp_channel": "googleplay", "pp_client_ver": "5.3.0&602280702", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "ZDfAAOuQ4o8I8UVzS80MrwGv7ZHRHwPyZL27ZuWCbkM45wxQ41DMokcYx3P7%2BNZxuIlgjDZqPKlOe0u3lCiOeN6CxNqgxN%2FUCyHrFYCjNqHQt7A%2FQsSt6c9rog0j6aF1rZTK7K5kqTESmpvVlbPOkUh4cmjHVxWlotIkO3%2FO2aQ%3D", "pp_timestamp": "1709174607106", "pp_token": "22761d93-cea3-47cf-a8f9-a8087e540409", "user-agent": "PalmPay/5.3.0&602280702 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/bundleMenuList", "headers": {":authority": "ng-apptest.transspay.net", ":method": "GET", ":path": "/api/airtime/bundleMenuList", ":scheme": "https", "accept-encoding": "gzip", "content-type": "application/json", "if-modified-since": "Thu, 29 Feb 2024 02:43:08 GMT", "pp_channel": "googleplay", "pp_client_ver": "5.3.0&602280702", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "E1p7TZJWrdcBdZvs3CJcdsq9e2rgb5NK4VhWKdaTUccWSAG%2By%2BIkNFekpp0jrQyjz4jNVkZhFpbqGHlkrwhJkF7ALYtdtZuFDhFWCtqaGdNXmhSvmeF%2FGcXUlXkJuU8qX93HvOB3xjNlx9RVXmyqSjrBXhXZE5SZM7skb%2By5%2FBA%3D", "pp_timestamp": "1709174607117", "pp_token": "22761d93-cea3-47cf-a8f9-a8087e540409", "user-agent": "PalmPay/5.3.0&602280702 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/cfront/quickteller/resolve/network/query", "params": {"mobileNo": "09551234911"}, "headers": {":authority": "ng-apptest.transspay.net", ":method": "GET", ":path": "/api/cfront/quickteller/resolve/network/query?mobileNo=09551234911", ":scheme": "https", "accept-encoding": "gzip", "content-type": "application/json", "if-modified-since": "Wed, 28 Feb 2024 06:21:55 GMT", "pp_channel": "googleplay", "pp_client_ver": "5.3.0&602280702", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "EYVceXMzzpG%2F27c7ytCLXL9yjIw0b%2BD4vJ5mtRSoNS%2BD%2F1SBcq54QvcukA1iHiVVCp5b6iQPLisyQsJcGkgM5jRgWeqtU%2BuU2PI6W0VjwTdVW5ZXi%2Bm0bM4aY2RrG%2ByglXc0ospxrQpkK6fSBqIS3IkKDD0Nw6Uc%2FReExD%2B%2FqOg%3D", "pp_timestamp": "1709174607122", "pp_token": "22761d93-cea3-47cf-a8f9-a8087e540409", "user-agent": "PalmPay/5.3.0&602280702 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.data", "assert": "equals", "expect": "GLO", "msg": "assert response body data"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/cfront/quickteller/resolve/network/query", "params": {"mobileNo": "09551234911"}, "headers": {":authority": "ng-apptest.transspay.net", ":method": "GET", ":path": "/api/cfront/quickteller/resolve/network/query?mobileNo=09551234911", ":scheme": "https", "accept-encoding": "gzip", "content-type": "application/json", "if-modified-since": "Wed, 28 Feb 2024 06:21:55 GMT", "pp_channel": "googleplay", "pp_client_ver": "5.3.0&602280702", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "jXYe8sNgcuAmp%2BelAjihOiJorQdngAS8gPzVfLcskL%2FrDawnnFZq4EDWIpti%2FOT3N%2BtM1toJPtoUG%2B5RfmWcC2CejG9yUwK5O%2F%2F6hdDRhLYnNwybLgJP6Jk4HD0SNeTAIEoQ137TkcdhZCo1xW8Kolj%2B6EOk%2BSMtRvQo86shFu0%3D", "pp_timestamp": "1709174607179", "pp_token": "22761d93-cea3-47cf-a8f9-a8087e540409", "user-agent": "PalmPay/5.3.0&602280702 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.data", "assert": "equals", "expect": "GLO", "msg": "assert response body data"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}]}