{"config": {"name": "testcase description"}, "teststeps": [{"name": "", "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/bundleMenuList", "headers": {":authority": "ng-apptest.transspay.net", ":method": "GET", ":path": "/api/airtime/bundleMenuList", ":scheme": "https", "accept-encoding": "gzip", "content-type": "application/json", "if-modified-since": "Fri, 01 Mar 2024 02:20:19 GMT", "pp_channel": "googleplay", "pp_client_ver": "5.3.0&602280702", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "CxdMx9yHwkH7yqGeTMD8f6Uet7zROZfu6t%2FeEvsoAXMGmP5saJqFVhGwjL%2F4vWe9XYEzSVhXNvGmD1INMDlkDKOiBySJ9OTC918l%2BeO9VLY68jlhBjUBU6niKNDXtZLNDiYWHfPqSJoQYshhcaaGiqTE5RhINFDdD9Xc0h9g%2FNk%3D", "pp_timestamp": "1709272574301", "pp_token": "2c64f24f-b02d-454d-ae9b-3aab57a4158e", "user-agent": "PalmPay/5.3.0&602280702 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}]}