# NOTE: Generated By HttpRunner v4.3.5
# FROM: testcases\data\data_20240229_test.json
import pytest
from httprunner import HttpRunner, Config, Step, RunRequest, Parameters


class TestCaseDataNetworkQueryTest(HttpRunner):

    @pytest.mark.parametrize(
        "param", Parameters(
            {"mobileNo-biller": [
                ["09551234911", "GLO"],
                ["08186897716", "9MOBILE"],
                ["07011698742", "AIRTEL"]
            ]}
        )
    )
    def test_start(self, param):
        super().test_start(param)

    config = Config("testcase description").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("data-手机号匹配运营商信息")
            .setup_hook('${setup_hooks_request($request)}')
            .get(
                "/api/cfront/quickteller/resolve/network/query"
            )
            .with_params(**{"mobileNo": "$mobileNo"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/cfront/quickteller/resolve/network/query?mobileNo=09551234911",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Wed, 28 Feb 2024 06:21:55 GMT",
                    "user-agent": "PalmPay/5.3.0&602280702 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.data", "$biller", "断言运营商信息")
        ),
    ]


if __name__ == "__main__":
    TestCaseDataNetworkQueryTest().test_start()
