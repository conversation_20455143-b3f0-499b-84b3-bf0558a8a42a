# NOTE: Generated By HttpRunner v4.3.5
import pytest
from httprunner import HttpRunner, Config, Step, RunRequest, Parameters


class TestDataAutoOnoffSaleTest(HttpRunner):
    @pytest.mark.parametrize(
        "param", Parameters(
            {"biller": ["9MO<PERSON>LE", "GLO", "AIRTEL"]}
        )
    )
    def test_start(self, param):
        super().test_start(param)

    config = Config("流量查询折扣套餐").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("流量查询折扣套餐")
            .setup_hook('${setup_hooks_request($request)}')
            .get("/api/airtime/discountPackage/batch")
            .with_params(**{"billerId": "$biller"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/bundleMenuList",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Fri, 01 Mar 2024 02:20:19 GMT",
                    # "pp_channel": "googleplay",
                    # "pp_client_ver": "5.3.0&602280702",
                    # "pp_device_id": "bee648d4b0a5ab70012345",
                    # "pp_device_type": "ANDROID",
                    # "pp_req_sign": "CxdMx9yHwkH7yqGeTMD8f6Uet7zROZfu6t%2FeEvsoAXMGmP5saJqFVhGwjL%2F4vWe9XYEzSVhXNvGmD1INMDlkDKOiBySJ9OTC918l%2BeO9VLY68jlhBjUBU6niKNDXtZLNDiYWHfPqSJoQYshhcaaGiqTE5RhINFDdD9Xc0h9g%2FNk%3D",
                    "pp_timestamp": "1709272574301",
                    "pp_token": "2c64f24f-b02d-454d-ae9b-3aab57a4158e",
                    "user-agent": "PalmPay/5.3.0&602280702 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
    ]


if __name__ == "__main__":
    TestDataAutoOnoffSaleTest().test_start()
