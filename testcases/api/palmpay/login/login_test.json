{"config": {"name": "testcase description"}, "teststeps": [{"name": "", "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/c-bff-product/start/loginV2", "headers": {":authority": "ng-apptest.transspay.net", ":method": "POST", ":path": "/api/c-bff-product/start/loginV2", ":scheme": "https", "accept-encoding": "gzip", "content-length": "634", "content-type": "application/json; charset=utf-8", "pp_channel": "googleplay", "pp_client_ver": "5.3.0&602280702", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "aC15udCC3o40xWKsuoZrNbdsmDPkYkxVVVpx9LVJXvCZIhj6L7y%2BR9bXR9oTXQ3R8BSMobTxVMRpVNFdRl9uGxJWMGas653Cf9MfkZ3msbCHqxNH2O%2FPvyBOkF19eQkrZaYWG7T%2B6NNe9fgOYJmRPRuy540uELFsKQ5gwcNdJdM%3D", "pp_req_sign_2": "pdCr2Pi8HVFkl5Ciq2HOprZSYSn5nHGHIgHOF59YGYu%2BMxvBfCr8j1N7BtFQrOcwFOuqHtpXEMmGTKhoW2ae5OXzK0e85D0pVJECbwa58YYEWDINQIBjhNdn9SjhuRgdUFVt5guSnwT7Kv2LChoB9Dbe%2Bi13u6pG0mGDQNJNoKU%3D", "pp_timestamp": "1709355579909", "pp_token": "", "user-agent": "PalmPay/5.3.0&602280702 (Android 13)"}, "body": {"appVersion": "5.3.0", "blackBox": "rGPVg17093555779dVfANRQoif", "brand": "INFINIX", "deviceInfo": "bee648d4b0a5ab70", "deviceModel": "InfinixX671B", "deviceTag": "", "deviceVersion": "Android13", "fcmToken": "eXIQkBq6S1iiC3EZOqcp52:APA91bF1BWaENypIZmaDktQ3OHdRYCQSNDozYSAcTK2cTirTtZZV4MbEDrQc7QmeXIl4Hv5S1As2ovOdFuW0ScxQxjNBpnlLi-XBvF59BUFSmKEiVvZi3wbzAjbfCYkXrVMdmI8mIyI9", "gaid": "715131e3-1810-4c6d-8dc3-c6776a8b63cc", "imei": "bee648d4b0a5ab70012345", "mobileNo": "023409551234911", "phoneLock": "0", "phoneLockIsActive": false, "phoneLockStatus": -128, "pin": "DE67E0C1D9E0493BB6659C6BFED4E851", "pinType": 1, "resolution": "2400X1080_480", "supplement": false}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}]}