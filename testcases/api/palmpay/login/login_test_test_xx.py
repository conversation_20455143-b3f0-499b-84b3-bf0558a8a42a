# NOTE: Generated By HttpRunner v4.3.5
# FROM: testcases\login\login_test.json
from httprunner import HttpRunner, Config, Step, RunRequest


class TestCaseLoginTest(HttpRunner):

    config = Config("testcase description").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("")
            .setup_hook('${setup_hooks_request($request)}')
            .post("/api/c-bff-product/start/loginV2")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/c-bff-product/start/loginV2",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "634",
                    "content-type": "application/json; charset=utf-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.3.0&602280702",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "aC15udCC3o40xWKsuoZrNbdsmDPkYkxVVVpx9LVJXvCZIhj6L7y%2BR9bXR9oTXQ3R8BSMobTxVMRpVNFdRl9uGxJWMGas653Cf9MfkZ3msbCHqxNH2O%2FPvyBOkF19eQkrZaYWG7T%2B6NNe9fgOYJmRPRuy540uELFsKQ5gwcNdJdM%3D",
                    "pp_req_sign_2": "pdCr2Pi8HVFkl5Ciq2HOprZSYSn5nHGHIgHOF59YGYu%2BMxvBfCr8j1N7BtFQrOcwFOuqHtpXEMmGTKhoW2ae5OXzK0e85D0pVJECbwa58YYEWDINQIBjhNdn9SjhuRgdUFVt5guSnwT7Kv2LChoB9Dbe%2Bi13u6pG0mGDQNJNoKU%3D",
                    "pp_timestamp": "${get_pp_timestamp()}",
                    "pp_token": "",
                    "user-agent": "PalmPay/5.3.0&602280702 (Android 13)",
                }
            )
            .with_data(
                {
                    "appVersion": "5.3.0",
                    "blackBox": "rGPVg17093555779dVfANRQoif",
                    "brand": "INFINIX",
                    "deviceInfo": "bee648d4b0a5ab70",
                    "deviceModel": "InfinixX671B",
                    "deviceTag": "",
                    "deviceVersion": "Android13",
                    "fcmToken": "eXIQkBq6S1iiC3EZOqcp52:APA91bF1BWaENypIZmaDktQ3OHdRYCQSNDozYSAcTK2cTirTtZZV4MbEDrQc7QmeXIl4Hv5S1As2ovOdFuW0ScxQxjNBpnlLi-XBvF59BUFSmKEiVvZi3wbzAjbfCYkXrVMdmI8mIyI9",
                    "gaid": "715131e3-1810-4c6d-8dc3-c6776a8b63cc",
                    "imei": "bee648d4b0a5ab70012345",
                    "mobileNo": "023409551234911",
                    "phoneLock": "0",
                    "phoneLockIsActive": False,
                    "phoneLockStatus": -128,
                    "pin": "DE67E0C1D9E0493BB6659C6BFED4E851",
                    "pinType": 1,
                    "resolution": "2400X1080_480",
                    "supplement": False,
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
    ]


if __name__ == "__main__":
    TestCaseLoginTest().test_start()
