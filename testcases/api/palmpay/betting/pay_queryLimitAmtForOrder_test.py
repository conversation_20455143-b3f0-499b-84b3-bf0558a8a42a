
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\betting\Betting_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestPayQuerylimitamtfororderTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("betting: 查询限额接口").setup_hook('${setup_hooks_request($request)}')
            .post(
                "/api/cfront/pay/queryLimitAmtForOrder"
            )
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/pay/queryLimitAmtForOrder",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "18",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "pZys%2FsuRLbotF4QL%2FBvDApmq%2B%2BAEs0SCk9rYm5UjYyqtY%2FDLtPbBStjsXogVOzfyKnHSDwySEUHoO%2FpMX0%2Fav%2BScvd9ew2ZFEFQguBO1i0rNVYnTrMjGEXUSydq9ZNQC4JUECi3oio7%2FuJ6gBGCi3%2FT00gc2qoIOugHEqoTaQ0I%3D",
                    "pp_req_sign_2": "Yxscngo9VXa7FGc7hzQ73fr8G0LIi8vUCcD6vGj%2BTfIWLAbNVDF%2BOM%2F03k%2FZ%2B4tqoos5rxBNuxzxCEwChLOM2omJQxNTZmXZaopnE02DK1hHNxUJFm4EcS06sbxSTO3Wh6nN0IT%2B%2BzJMeVicn1NfvM1AcQvieI1AMbKpe7xB2E4%3D",
                    "pp_timestamp": "1715082259942",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_json({"transType": "96"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        )
    ]

if __name__ == "__main__":
    TestPayQuerylimitamtfororderTest().test_start()
