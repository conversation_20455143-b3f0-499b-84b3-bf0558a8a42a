
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\betting\Betting_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestBettingInfoTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("betting: 查询具体运营商的信息").setup_hook('${setup_hooks_request($request)}')
            .get("/api/cfront/betting/info")
            .with_params(**{"billerId": "2269"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/cfront/betting/info?billerId=2269",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "jRKrhRZKveWA83u83v00jM%2FWqEzyD9eY85AqYqvKCettQwz5a%2BiYouatzRMRjXMo1hhwWklaq14Lib6rQ1K26GJmu9P645NW0TH%2BLXEwEbST5IyCqVYG7psEKrv2nzQd7jyCS8rsrn8f3G%2F88%2B1qJw9Hkt7e2L4rEO2OPpuhouk%3D",
                    "pp_timestamp": "1715082259940",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]

if __name__ == "__main__":
    TestBettingInfoTest().test_start()
