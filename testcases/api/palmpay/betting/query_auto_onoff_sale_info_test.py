
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\betting\Betting_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestQueryAutoonoffsaleinfoTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("betting: 自动上下架公告").setup_hook('${setup_hooks_request($request)}')
            .post(
                "/api/cfront/airtime/v2/query/auto-onoff-sale-info"
            )
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/airtime/v2/query/auto-onoff-sale-info",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "20",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "FtN%2FwgxWDllv5IYtKRgvSlx6%2F%2BFkeVcJmQAM%2B8c1S%2B1ethqVdj08qzJYTLImSPiKxyokKYxn2FWpi6%2BMm9JrwbKAhFOVIQ94Q9TOjPX6pK07EETGYI1grMWIIavny4AevO1M9Af%2FGMLYPJeipfaTLd57GLj0tG9L0UD63kDkOCw%3D",
                    "pp_req_sign_2": "PyIv%2F%2Bpl89VujiP5JtSGtjZFa968Cw1k5R%2B2QbVJbdWp1BF4fPfB1POytz7jIhdkkZrR%2BgEe0GWdWXISpt8cKOQ39VNF%2F7gcdTv2y5ZRgXgEmj74qYUHUzrWHB7BqfKwPwnZuzhRUGo3V1L1q8iYIw%2B815TJSjJcRfvsqdUEYiw%3D",
                    "pp_timestamp": "1715082257513",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_json({"categoryId": ["8"]})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        )
    ]

if __name__ == "__main__":
    TestQueryAutoonoffsaleinfoTest().test_start()
