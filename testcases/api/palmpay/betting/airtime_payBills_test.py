
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\betting\Betting_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestAirtimePaybillsTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("betting: 运营商列表快捷入口").setup_hook('${setup_hooks_request($request)}')
            .get("/api/airtime/payBills")
            .with_params(**{"location": "96"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/payBills?location=96",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "FaMWlGF1DJ8Eat7k8tvvtJsRoN06pT8awMmw5awi6FxyEQQxrPTFQeSnlhF6rUEmOKDk1WGGECyvqauoTgxfrsiiqWvU73Lsfx%2Fwx3gSSO5kxGle0DW00HekG46Ww8jJsrdSt5Sw5lbCT%2Ba25IkOaCSH%2BbdYi21iCAUjZyBWK5w%3D",
                    "pp_timestamp": "1715082257533",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]

if __name__ == "__main__":
    TestAirtimePaybillsTest().test_start()
