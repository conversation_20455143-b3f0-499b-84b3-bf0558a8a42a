# NOTE: Generated By HttpRunner v4.3.5
# FROM: testcases\palmpay\debug\Betting_zihao_test.json
from httprunner import HttpRunner, Config, Step, RunRequest


class TestCaseBettingZihaoTest(HttpRunner):

    config = Config("testcase description")

    teststeps = [
        Step(
            RunRequest("betting:代理商状态")
            .get(
                "https://ng-apptest.transspay.net/api/online-agent-product/agent/status"
            )
            .with_params(**{"categoryId": "8"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/online-agent-product/agent/status?categoryId=8",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "IeD0UGAF%2B%2FVj1s2Tq%2Fm%2BmDNdZWhHZT2GJLiYWidD%2BYFun87Hrxnr6bVLDP359X1FPp6CFc8%2BLyofyDvss4J%2B4BLOXT79bcJ2NY6piH618RAcGgi5hLwiUQbkIsQt%2BvbbHA7XX0gOLB6dbQEAm2PVYt98wd4d10NoWr4nYmL0FAk%3D",
                    "pp_timestamp": "1715082257351",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("betting: 主接口")
            .get("https://ng-apptest.transspay.net/api/cfront/betting/homepage")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/cfront/betting/homepage",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "o%2BqvP49EO0i0eQJuqJRWoahh91PphfYfmqypjnGmGZDyLFfX3kAq70sAtePnlvBH6xFkXt%2BBK%2FimC9JoloDPEzLbB5nD1iW4rYbhIcC3%2F%2Bk2aB59mr7qgrpxQ55FyLAA3yRZ1oBhWTtMW84dQXzZ%2FCajxUL2W5B9KzGNei%2FVzqo%3D",
                    "pp_timestamp": "1715082257480",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),

        Step(
            RunRequest("betting: 自动上下架公告")
            .post(
                "https://ng-apptest.transspay.net/api/cfront/airtime/v2/query/auto-onoff-sale-info"
            )
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/airtime/v2/query/auto-onoff-sale-info",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "20",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "FtN%2FwgxWDllv5IYtKRgvSlx6%2F%2BFkeVcJmQAM%2B8c1S%2B1ethqVdj08qzJYTLImSPiKxyokKYxn2FWpi6%2BMm9JrwbKAhFOVIQ94Q9TOjPX6pK07EETGYI1grMWIIavny4AevO1M9Af%2FGMLYPJeipfaTLd57GLj0tG9L0UD63kDkOCw%3D",
                    "pp_req_sign_2": "PyIv%2F%2Bpl89VujiP5JtSGtjZFa968Cw1k5R%2B2QbVJbdWp1BF4fPfB1POytz7jIhdkkZrR%2BgEe0GWdWXISpt8cKOQ39VNF%2F7gcdTv2y5ZRgXgEmj74qYUHUzrWHB7BqfKwPwnZuzhRUGo3V1L1q8iYIw%2B815TJSjJcRfvsqdUEYiw%3D",
                    "pp_timestamp": "1715082257513",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_data({"categoryId": ["8"]})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("betting： 可领券列表")
            .get(
                "https://ng-apptest.transspay.net/api/scene-activity-product/bill/pullNew/insidePullNewList"
            )
            .with_params(**{"showLocation": "BettingHomepage"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=BettingHomepage",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "MfJq3rxcQ34tBlCYU7raDe6tWr15IjVMg8PBej23xnH068ipxHA6oUeOmVbKIZN6eecy%2F6BQW9wNzU1qaUTat3Ajy0RD4zMdySCCyxog%2FWExqrAErzlyBvjJ1Z0G4AX2ok%2FtBS%2BFzcS3xHXLessaP7z20q%2BC5513U1WVJExNrh8%3D",
                    "pp_timestamp": "1715082257534",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("betting: 运营商列表快捷入口")
            .get("https://ng-apptest.transspay.net/api/airtime/payBills")
            .with_params(**{"location": "96"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/payBills?location=96",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "FaMWlGF1DJ8Eat7k8tvvtJsRoN06pT8awMmw5awi6FxyEQQxrPTFQeSnlhF6rUEmOKDk1WGGECyvqauoTgxfrsiiqWvU73Lsfx%2Fwx3gSSO5kxGle0DW00HekG46Ww8jJsrdSt5Sw5lbCT%2Ba25IkOaCSH%2BbdYi21iCAUjZyBWK5w%3D",
                    "pp_timestamp": "1715082257533",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("betting： 已领券列表")
            .post("https://ng-apptest.transspay.net/api/cfront/coupon/statistics")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/coupon/statistics",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "45",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "VQcWjaDMYb5g71gh12GRztzdZrWbaTFJbvto7dSBMyJpAFq7OAM4er9iloSy%2F%2F%2FDbGfyBTY%2FJnJ1JeZaOvbNQZIE9WYbuiQTZbB5HW4gahhuSOWMGwmZiuZxkhdb6z%2Bgek%2BKR9MNupvmx30bf5pwNdvm52ltFYJadoLYlLdsOQA%3D",
                    "pp_req_sign_2": "eoLycmKopxcL4nKaRyo%2FarlNMQt%2FseMPiFWpfio%2FoH5aRFK52xZC2bKtLWMJc3xn4lrsHjZiMwGbYk%2Fqiy6I7p4mCcbvHZ5ZeKh2ixprE5i1vtFMkWJci34%2B3zRaCVp7lYq09ST4IGeJz3ITbDNI8QHZVHRv4joJFjvq2Z9aI0o%3D",
                    "pp_timestamp": "1715082257547",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_data({"pageNum": 1, "pageSize": 100, "transType": "96"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("betting: 查询具体运营商的信息")
            .get("https://ng-apptest.transspay.net/api/cfront/betting/info")
            .with_params(**{"billerId": "2269"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/cfront/betting/info?billerId=2269",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "jRKrhRZKveWA83u83v00jM%2FWqEzyD9eY85AqYqvKCettQwz5a%2BiYouatzRMRjXMo1hhwWklaq14Lib6rQ1K26GJmu9P645NW0TH%2BLXEwEbST5IyCqVYG7psEKrv2nzQd7jyCS8rsrn8f3G%2F88%2B1qJw9Hkt7e2L4rEO2OPpuhouk%3D",
                    "pp_timestamp": "1715082259940",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("betting: 查询限额接口")
            .post(
                "https://ng-apptest.transspay.net/api/cfront/pay/queryLimitAmtForOrder"
            )
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/pay/queryLimitAmtForOrder",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "18",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "pZys%2FsuRLbotF4QL%2FBvDApmq%2B%2BAEs0SCk9rYm5UjYyqtY%2FDLtPbBStjsXogVOzfyKnHSDwySEUHoO%2FpMX0%2Fav%2BScvd9ew2ZFEFQguBO1i0rNVYnTrMjGEXUSydq9ZNQC4JUECi3oio7%2FuJ6gBGCi3%2FT00gc2qoIOugHEqoTaQ0I%3D",
                    "pp_req_sign_2": "Yxscngo9VXa7FGc7hzQ73fr8G0LIi8vUCcD6vGj%2BTfIWLAbNVDF%2BOM%2F03k%2FZ%2B4tqoos5rxBNuxzxCEwChLOM2omJQxNTZmXZaopnE02DK1hHNxUJFm4EcS06sbxSTO3Wh6nN0IT%2B%2BzJMeVicn1NfvM1AcQvieI1AMbKpe7xB2E4%3D",
                    "pp_timestamp": "1715082259942",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_data({"transType": "96"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("betting： betting match")
            .get("https://ng-apptest.transspay.net/api/gateway/betting/match")
            .with_params(**{"timeZone": "GMT+08:00"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/gateway/betting/match?timeZone=GMT%2B08%3A00",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "KFaf8TQF8vgMwCzBo5JeRLUFVrKLJlqUm15a0YPeNClk%2B9HZ26Ra8W0LcuR9ajEfWSdvtWzd6VESc9RLo48KnNMz2AAmyeXDH9HqbqSg0uKB23hlWagxTrXEz9e6qK4VBllma3SLAjSaWFu9E%2FSbsBxW3STmaore2mcDZXgELrQ%3D",
                    "pp_timestamp": "1715082260270",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
    ]


if __name__ == "__main__":
    TestCaseBettingZihaoTest().test_start()
