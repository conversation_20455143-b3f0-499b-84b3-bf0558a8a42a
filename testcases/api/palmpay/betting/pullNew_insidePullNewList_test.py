
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\betting\Betting_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestPullnewInsidepullnewlistTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("betting： 可领券列表").setup_hook('${setup_hooks_request($request)}')
            .get(
                "/api/scene-activity-product/bill/pullNew/insidePullNewList"
            )
            .with_params(**{"showLocation": "BettingHomepage"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=BettingHomepage",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "MfJq3rxcQ34tBlCYU7raDe6tWr15IjVMg8PBej23xnH068ipxHA6oUeOmVbKIZN6eecy%2F6BQW9wNzU1qaUTat3Ajy0RD4zMdySCCyxog%2FWExqrAErzlyBvjJ1Z0G4AX2ok%2FtBS%2BFzcS3xHXLessaP7z20q%2BC5513U1WVJExNrh8%3D",
                    "pp_timestamp": "1715082257534",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            # 活动结束
            .assert_equal("status_code", 200, "assert response status code")
            # .assert_equal("body.respCode", "10000013", "assert response body respCode")
            # .assert_equal("body.respMsg", "Activity ended", "assert response body respMsg")
            # .assert_equal("body.status", False, "assert response body status")
        )
    ]

if __name__ == "__main__":
    TestPullnewInsidepullnewlistTest().test_start()
