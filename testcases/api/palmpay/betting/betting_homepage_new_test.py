# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\betting\Betting_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest


# 新版本的betting主页接口
class TestBettingHomepageTest(HttpRunner):
    config = Config("C端:新接口首页").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("betting: ").setup_hook('${setup_hooks_request($request)}')
            .get("/api/betting-product/biller/v2/getHomeInfo")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/cfront/betting/homepage",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.11.0&604181603",  # 注意使用的新版本
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "o%2BqvP49EO0i0eQJuqJRWoahh91PphfYfmqypjnGmGZDyLFfX3kAq70sAtePnlvBH6xFkXt%2BBK%2FimC9JoloDPEzLbB5nD1iW4rYbhIcC3%2F%2Bk2aB59mr7qgrpxQ55FyLAA3yRZ1oBhWTtMW84dQXzZ%2FCajxUL2W5B9KzGNei%2FVzqo%3D",
                    "pp_timestamp": "1715082257480",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]


if __name__ == "__main__":
    TestBettingHomepageTest().test_start()
