
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\betting\Betting_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestCouponStatisticsTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("betting： 已领券列表").setup_hook('${setup_hooks_request($request)}')
            .post("/api/cfront/coupon/statistics")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/coupon/statistics",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "45",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "VQcWjaDMYb5g71gh12GRztzdZrWbaTFJbvto7dSBMyJpAFq7OAM4er9iloSy%2F%2F%2FDbGfyBTY%2FJnJ1JeZaOvbNQZIE9WYbuiQTZbB5HW4gahhuSOWMGwmZiuZxkhdb6z%2Bgek%2BKR9MNupvmx30bf5pwNdvm52ltFYJadoLYlLdsOQA%3D",
                    "pp_req_sign_2": "eoLycmKopxcL4nKaRyo%2FarlNMQt%2FseMPiFWpfio%2FoH5aRFK52xZC2bKtLWMJc3xn4lrsHjZiMwGbYk%2Fqiy6I7p4mCcbvHZ5ZeKh2ixprE5i1vtFMkWJci34%2B3zRaCVp7lYq09ST4IGeJz3ITbDNI8QHZVHRv4joJFjvq2Z9aI0o%3D",
                    "pp_timestamp": "1715082257547",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_json({"pageNum": 1, "pageSize": 100, "transType": "96"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        )
    ]

if __name__ == "__main__":
    TestCouponStatisticsTest().test_start()
