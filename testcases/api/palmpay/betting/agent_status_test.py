
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\betting\Betting_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestAgentStatusTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("betting:代理商状态").setup_hook('${setup_hooks_request($request)}')
            .get(
                "/api/online-agent-product/agent/status"
            )
            .with_params(**{"categoryId": "8"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/online-agent-product/agent/status?categoryId=8",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "IeD0UGAF%2B%2FVj1s2Tq%2Fm%2BmDNdZWhHZT2GJLiYWidD%2BYFun87Hrxnr6bVLDP359X1FPp6CFc8%2BLyofyDvss4J%2B4BLOXT79bcJ2NY6piH618RAcGgi5hLwiUQbkIsQt%2BvbbHA7XX0gOLB6dbQEAm2PVYt98wd4d10NoWr4nYmL0FAk%3D",
                    "pp_timestamp": "1715082257351",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        )
    ]

if __name__ == "__main__":
    TestAgentStatusTest().test_start()
