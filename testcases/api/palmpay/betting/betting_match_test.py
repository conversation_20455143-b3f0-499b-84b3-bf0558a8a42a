
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\betting\Betting_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestBettingMatchTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("betting： betting match").setup_hook('${setup_hooks_request($request)}')
            .get("/api/gateway/betting/match")
            .with_params(**{"timeZone": "GMT+08:00"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/gateway/betting/match?timeZone=GMT%2B08%3A00",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "KFaf8TQF8vgMwCzBo5JeRLUFVrKLJlqUm15a0YPeNClk%2B9HZ26Ra8W0LcuR9ajEfWSdvtWzd6VESc9RLo48KnNMz2AAmyeXDH9HqbqSg0uKB23hlWagxTrXEz9e6qK4VBllma3SLAjSaWFu9E%2FSbsBxW3STmaore2mcDZXgELrQ%3D",
                    "pp_timestamp": "1715082260270",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]

if __name__ == "__main__":
    TestBettingMatchTest().test_start()
