{"log": {"version": "1.2", "creator": {"name": "<PERSON>", "version": "4.6.4"}, "entries": [{"startedDateTime": "2024-05-20T17:56:17.163+08:00", "time": 228, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/airtime/v2/query/auto-onoff-sale-info", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/airtime/v2/query/auto-onoff-sale-info"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976452"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "NGAOfV2486HePMNyk1fRrNvFDmwevS3IcNkpCk14dFxL%2B03w0nZbLNk3MsdNWrl83vjI8YJjt1Pc2DHG%2F4n8EPPA9Y39QbEd%2F4NlDJLHLCFSDcG88End8D90k%2B91ugUaf9yFgRyp75HmitjcIWK5cP4siRy6dMUTYi6j52ncRwg%3D"}, {"name": "pp_req_sign_2", "value": "pK%2BygiaU70qHER%2BTiR<PERSON><PERSON>%2B8472y%2FHu%2BT2%2FeggGuYVh9QKFssEUcENh%2BDKiyCCK2DfnuIpBOf4UNL%2BTktGD21h0cgekE06jseX2OkiIcFVkFgQbCnGP933qBBVbkTofq4SUpP9J0j3bNO8Tc4udDq%2BjFu5xw98zNxHX70zs6kuV4%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "20"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"categoryId\":[\"2\"]}"}, "headersSize": 393, "bodySize": 20}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "67"}, {"name": "eagleeye-traceid", "value": "eaac162bc217161989773512365d0001"}, {"name": "x-envoy-upstream-service-time", "value": "25"}], "content": {"size": 67, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbXSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 104, "bodySize": 67}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 228, "receive": 0}}, {"startedDateTime": "2024-05-20T17:56:17.218+08:00", "time": 218, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/online-agent-product/agent/status?categoryId=2", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/online-agent-product/agent/status?categoryId=2"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976454"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "oyYiuVZwQqIP%2Btnrj4S1FWY7PA1XkPP35Z78e2T9IW39TRsVi%2BhOT0yLn0M0NLWeyn2s01NIWmC8A3p50oe1jxvD7yXrBYkaWT6adcG%2B%2FeKePnHfJByDP0PrA7mTJAYL4sIE84rk6CGgHjU%2FOjjhmvp%2BC6sjtAZ9u77Ad%2FJBy8U%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Mon, 20 May 2024 09:56:01 GMT"}], "queryString": [{"name": "categoryId", "value": "2"}], "headersSize": 216, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "559"}, {"name": "eagleeye-traceid", "value": "eaac16282817161989774052027d0001"}, {"name": "x-envoy-upstream-service-time", "value": "20"}], "content": {"size": 559, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7Im1lbWJlcklkIjoiQTEyRDc5RjgxNkMxNDRCNTkzNzAzRUNGRkU1M0YzMTkiLCJpc1doaXRlTGlzdE1lbWJlciI6dHJ1ZSwiYWdlbnRMZXZlbCI6MSwibGV2ZWxEZXNjIjoiWW91ciBhZ2VudCBsZXZlbCB3aWxsIGJlIG9uIEZpcnN0IExldmVsIHdoZW4geW91ciB0cmFuc2FjdGlvbiBhbW91bnQgb2YgQWlydGltZeOAgURhdGHjgIFCZXR0aW5n44CBRWxlY3RyaWNpdHnjgIFUViBpcyBtb3JlIHRoYW4gTjEyMDAuXG5UaGUgY29tbWlzc2lvbiByYXRlIG9mIEZpcnN0IExldmVsIGFzIGZvbGxvd++8mlxuQWlydGltZSB1cCB0byAxJe+8m1xuRGF0YSB1cCB0byAxJe+8m1xuQmV0dGluZyB1cCB0byAxJe+8m1xuRWxlY3RyaWNpdHkgdXAgdG8gMSXvvJtcblRWIHVwIHRvIDElIiwiYWdlbnRTdGF0dXMiOjAsImFnZW50VHlwZSI6MSwibG9nbyI6bnVsbCwidGlwcyI6IkJlY29tZSBQYWxtUGF5IFBsdXMsIGVhcm4gY29tbWlzc2lvbi4iLCJhZ2VudFN0YXRlIjoiTGFnb3MifSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 105, "bodySize": 559}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 217, "receive": 0}}, {"startedDateTime": "2024-05-20T17:56:17.218+08:00", "time": 948, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/live/biller?categoryId=2", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/live/biller?categoryId=2"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976465"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "Yh3yuhfAb2wHrKJTJClzM5E%2FqVBlG0%2BA6pQZEGOeis1vmtjK4OofQg6vIcR5xVr12jvtVm3COqHJ3wmm1XH8sQmWlepBObMcN7Endi2lS4FwBp6GGWUvZxFP8fKkymz4NINdwABZKXVlNvxmq5ESd4tInNIdRdXMDqYlXg3ZpMk%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Mon, 20 May 2024 09:56:02 GMT"}], "queryString": [{"name": "categoryId", "value": "2"}], "headersSize": 237, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:18 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162bc217161989774102367d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "740"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 25023, "compression": 22845, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbeyJyZWNvbW1lbmQiOmZhbHNlLCJpZCI6bnVsbCwiY2F0ZWdvcnlJZCI6IjIiLCJjYXRlZ29yeU5hbWUiOm51bGwsImNhdGVnb3J5RGVzY3JpcHRpb24iOm51bGwsImJpbGxlcklkIjoiMTEyIiwiYmlsbGVyTmFtZSI6Ik15VFYgU21hcnQgUGF5bWVudCIsImJpbGxlckxhYmVsTGlzdCI6bnVsbCwiY3VzdG9tZXJGaWVsZDEiOm51bGwsImN1c3RvbWVyRmllbGQyIjpudWxsLCJwYXlEaXJlY3RQcm9kdWN0SWQiOm51bGwsInBheURpcmVjdEluc3RpdHV0aW9uSWQiOm51bGwsIm5hcnJhdGlvbiI6bnVsbCwic2hvcnROYW1lIjoiTXlUViIsInN1cmNoYXJnZSI6bnVsbCwic2l0ZVVybE5hbWUiOm51bGwsImlzU2hvdyI6bnVsbCwicmVtYXJrIjpudWxsLCJiaWxsZXJMYWJsZSI6bnVsbCwiYmlsbGVyVGlwcyI6bnVsbCwiaGFzQ291cG9uIjpmYWxzZSwic3VzcGVuZCI6ZmFsc2UsInN0YXR1cyI6MSwiY3JlYXRlVGltZSI6bnVsbCwidXBkYXRlVGltZSI6bnVsbCwiaW5zdGl0dXRpb25Mb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFwLXNvdXRoZWFzdC0xLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTYwNTE0NTY2NzgzNDFNeVRWLnBuZyIsInRpcHMiOiI0NTY0NTExMjEzNDg2Nzg0MTIxMjMxNjQ2NTQ2NTEyMTM2MzEiLCJ1c2VySWRUaXBzIjoieHh4eHh4eHh4eCIsImlucHV0VGl0bGUiOiJTbWFydGNhcmQgTnVtYmVyIiwiaW5wdXRUaXBzIjoiRW50ZXIgeHh4eHh4eHh4eCIsImRpc2NvdW50VGV4dCI6bnVsbCwibWluQW1vdW50IjowLCJtYXhBbW91bnQiOjAsImFtb3VudFRpcCI6bnVsbCwiY2hhbm5lbENvZGUiOm51bGx9LHsicmVjb21tZW5kIjpmYWxzZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6IjE4MDMiLCJiaWxsZXJOYW1lIjoidGVzdCBmb3IgdHYyIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOiJBQ1RWIiwic3VyY2hhcmdlIjpudWxsLCJzaXRlVXJsTmFtZSI6bnVsbCwiaXNTaG93IjpudWxsLCJyZW1hcmsiOm51bGwsImJpbGxlckxhYmxlIjpudWxsLCJiaWxsZXJUaXBzIjpudWxsLCJoYXNDb3Vwb24iOmZhbHNlLCJzdXNwZW5kIjpmYWxzZSwic3RhdHVzIjoxLCJjcmVhdGVUaW1lIjpudWxsLCJ1cGRhdGVUaW1lIjpudWxsLCJpbnN0aXR1dGlvbkxvZ28iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNjQwMzM0NzA2NDUzNS1TY3JlZW5zaG90XzIwMjExMjI0LTExMjEzNi5wbmciLCJ0aXBzIjpudWxsLCJ1c2VySWRUaXBzIjpudWxsLCJpbnB1dFRpdGxlIjoiU21hcnRjYXJkIE51bWJlciIsImlucHV0VGlwcyI6bnVsbCwiZGlzY291bnRUZXh0IjpudWxsLCJtaW5BbW91bnQiOjAsIm1heEFtb3VudCI6MCwiYW1vdW50VGlwIjpudWxsLCJjaGFubmVsQ29kZSI6bnVsbH0seyJyZWNvbW1lbmQiOmZhbHNlLCJpZCI6bnVsbCwiY2F0ZWdvcnlJZCI6IjIiLCJjYXRlZ29yeU5hbWUiOm51bGwsImNhdGVnb3J5RGVzY3JpcHRpb24iOm51bGwsImJpbGxlcklkIjoiMTg2MiIsImJpbGxlck5hbWUiOiJNb250YWdlIERlY29kZXIgU3RhcnRlciBQYWNrIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOiJNb250YWdlVFYiLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6bnVsbCwidGlwcyI6bnVsbCwidXNlcklkVGlwcyI6bnVsbCwiaW5wdXRUaXRsZSI6IlNtYXJ0Y2FyZCBOdW1iZXIiLCJpbnB1dFRpcHMiOm51bGwsImRpc2NvdW50VGV4dCI6bnVsbCwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsImFtb3VudFRpcCI6bnVsbCwiY2hhbm5lbENvZGUiOm51bGx9LHsicmVjb21tZW5kIjpmYWxzZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6IjI0MCIsImJpbGxlck5hbWUiOiJTdGFydGltZXMgUGF5bWVudHMiLCJiaWxsZXJMYWJlbExpc3QiOm51bGwsImN1c3RvbWVyRmllbGQxIjpudWxsLCJjdXN0b21lckZpZWxkMiI6bnVsbCwicGF5RGlyZWN0UHJvZHVjdElkIjpudWxsLCJwYXlEaXJlY3RJbnN0aXR1dGlvbklkIjpudWxsLCJuYXJyYXRpb24iOm51bGwsInNob3J0TmFtZSI6IlN0YXJTYXQiLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hcC1zb3V0aGVhc3QtMS5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2MDUxNDU1OTMzMjIxN1N0YXJUaW1lcy5wbmciLCJ0aXBzIjpudWxsLCJ1c2VySWRUaXBzIjpudWxsLCJpbnB1dFRpdGxlIjoiU21hcnRjYXJkIE51bWJlciIsImlucHV0VGlwcyI6bnVsbCwiZGlzY291bnRUZXh0IjpudWxsLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwiYW1vdW50VGlwIjpudWxsLCJjaGFubmVsQ29kZSI6bnVsbH0seyJyZWNvbW1lbmQiOmZhbHNlLCJpZCI6bnVsbCwiY2F0ZWdvcnlJZCI6IjIiLCJjYXRlZ29yeU5hbWUiOm51bGwsImNhdGVnb3J5RGVzY3JpcHRpb24iOm51bGwsImJpbGxlcklkIjoiMzA4IiwiYmlsbGVyTmFtZSI6IkluZmluaXR5IFRWIFBheW1lbnRzIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOiJOL0EiLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hcC1zb3V0aGVhc3QtMS5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2MDUxNDU0OTUyMjI2SW5maW5pdHklMjBUVi5wbmciLCJ0aXBzIjpudWxsLCJ1c2VySWRUaXBzIjpudWxsLCJpbnB1dFRpdGxlIjoiU21hcnRjYXJkIE51bWJlciIsImlucHV0VGlwcyI6bnVsbCwiZGlzY291bnRUZXh0IjpudWxsLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwiYW1vdW50VGlwIjpudWxsLCJjaGFubmVsQ29kZSI6bnVsbH0seyJyZWNvbW1lbmQiOmZhbHNlLCJpZCI6bnVsbCwiY2F0ZWdvcnlJZCI6IjIiLCJjYXRlZ29yeU5hbWUiOm51bGwsImNhdGVnb3J5RGVzY3JpcHRpb24iOm51bGwsImJpbGxlcklkIjoiMzEyNiIsImJpbGxlck5hbWUiOiJLd2VzZSBUViIsImJpbGxlckxhYmVsTGlzdCI6bnVsbCwiY3VzdG9tZXJGaWVsZDEiOm51bGwsImN1c3RvbWVyRmllbGQyIjpudWxsLCJwYXlEaXJlY3RQcm9kdWN0SWQiOm51bGwsInBheURpcmVjdEluc3RpdHV0aW9uSWQiOm51bGwsIm5hcnJhdGlvbiI6bnVsbCwic2hvcnROYW1lIjoiS3dlc2UgVFYiLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hcC1zb3V0aGVhc3QtMS5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2MDUxNDU0NDA4OTExS3dlc2UlMjBUVi5wbmciLCJ0aXBzIjpudWxsLCJ1c2VySWRUaXBzIjpudWxsLCJpbnB1dFRpdGxlIjoiU21hcnRjYXJkIE51bWJlciIsImlucHV0VGlwcyI6bnVsbCwiZGlzY291bnRUZXh0IjpudWxsLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwiYW1vdW50VGlwIjpudWxsLCJjaGFubmVsQ29kZSI6bnVsbH0seyJyZWNvbW1lbmQiOmZhbHNlLCJpZCI6bnVsbCwiY2F0ZWdvcnlJZCI6IjIiLCJjYXRlZ29yeU5hbWUiOm51bGwsImNhdGVnb3J5RGVzY3JpcHRpb24iOm51bGwsImJpbGxlcklkIjoiMzE0NSIsImJpbGxlck5hbWUiOiJUU1RWIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOiJUU1RWIiwic3VyY2hhcmdlIjpudWxsLCJzaXRlVXJsTmFtZSI6bnVsbCwiaXNTaG93IjpudWxsLCJyZW1hcmsiOm51bGwsImJpbGxlckxhYmxlIjpudWxsLCJiaWxsZXJUaXBzIjpudWxsLCJoYXNDb3Vwb24iOmZhbHNlLCJzdXNwZW5kIjpmYWxzZSwic3RhdHVzIjoxLCJjcmVhdGVUaW1lIjpudWxsLCJ1cGRhdGVUaW1lIjpudWxsLCJpbnN0aXR1dGlvbkxvZ28iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYXAtc291dGhlYXN0LTEuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNjA1MTQ1MTcwMjQ0M1RTVFYucG5nIiwidGlwcyI6bnVsbCwidXNlcklkVGlwcyI6bnVsbCwiaW5wdXRUaXRsZSI6IlNtYXJ0Y2FyZCBOdW1iZXIiLCJpbnB1dFRpcHMiOm51bGwsImRpc2NvdW50VGV4dCI6bnVsbCwibWluQW1vdW50Ijo4ODgwMCwibWF4QW1vdW50IjowLCJhbW91bnRUaXAiOm51bGwsImNoYW5uZWxDb2RlIjpudWxsfSx7InJlY29tbWVuZCI6ZmFsc2UsImlkIjpudWxsLCJjYXRlZ29yeUlkIjoiMiIsImNhdGVnb3J5TmFtZSI6bnVsbCwiY2F0ZWdvcnlEZXNjcmlwdGlvbiI6bnVsbCwiYmlsbGVySWQiOiIzMjM0IiwiYmlsbGVyTmFtZSI6IlBsYXRpbnVtIFBsdXMgVFYiLCJiaWxsZXJMYWJlbExpc3QiOm51bGwsImN1c3RvbWVyRmllbGQxIjpudWxsLCJjdXN0b21lckZpZWxkMiI6bnVsbCwicGF5RGlyZWN0UHJvZHVjdElkIjpudWxsLCJwYXlEaXJlY3RJbnN0aXR1dGlvbklkIjpudWxsLCJuYXJyYXRpb24iOm51bGwsInNob3J0TmFtZSI6IlBQVFYiLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6bnVsbCwidGlwcyI6bnVsbCwidXNlcklkVGlwcyI6bnVsbCwiaW5wdXRUaXRsZSI6IlNtYXJ0Y2FyZCBOdW1iZXIiLCJpbnB1dFRpcHMiOm51bGwsImRpc2NvdW50VGV4dCI6bnVsbCwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsImFtb3VudFRpcCI6bnVsbCwiY2hhbm5lbENvZGUiOm51bGx9LHsicmVjb21tZW5kIjpmYWxzZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6IjM0MzEiLCJiaWxsZXJOYW1lIjoiTGluZGEgSWtlamkgVFYiLCJiaWxsZXJMYWJlbExpc3QiOm51bGwsImN1c3RvbWVyRmllbGQxIjpudWxsLCJjdXN0b21lckZpZWxkMiI6bnVsbCwicGF5RGlyZWN0UHJvZHVjdElkIjpudWxsLCJwYXlEaXJlY3RJbnN0aXR1dGlvbklkIjpudWxsLCJuYXJyYXRpb24iOm51bGwsInNob3J0TmFtZSI6Ik4vQSIsInN1cmNoYXJnZSI6bnVsbCwic2l0ZVVybE5hbWUiOm51bGwsImlzU2hvdyI6bnVsbCwicmVtYXJrIjpudWxsLCJiaWxsZXJMYWJsZSI6bnVsbCwiYmlsbGVyVGlwcyI6bnVsbCwiaGFzQ291cG9uIjpmYWxzZSwic3VzcGVuZCI6ZmFsc2UsInN0YXR1cyI6MSwiY3JlYXRlVGltZSI6bnVsbCwidXBkYXRlVGltZSI6bnVsbCwiaW5zdGl0dXRpb25Mb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFwLXNvdXRoZWFzdC0xLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTYwNTE0NTQyNDcwNjZMaW5kYSUyMGlrZWppJTIwVFYucG5nIiwidGlwcyI6bnVsbCwidXNlcklkVGlwcyI6bnVsbCwiaW5wdXRUaXRsZSI6IlNtYXJ0Y2FyZCBOdW1iZXIiLCJpbnB1dFRpcHMiOm51bGwsImRpc2NvdW50VGV4dCI6bnVsbCwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsImFtb3VudFRpcCI6bnVsbCwiY2hhbm5lbENvZGUiOm51bGx9LHsicmVjb21tZW5kIjpmYWxzZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6IjQwMiIsImJpbGxlck5hbWUiOiJUcmVuZFRWIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOiJOL0EiLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hcC1zb3V0aGVhc3QtMS5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2MDUxNDUzMjA2NjIxOHRyZW5kVFYucG5nIiwidGlwcyI6bnVsbCwidXNlcklkVGlwcyI6bnVsbCwiaW5wdXRUaXRsZSI6IlNtYXJ0Y2FyZCBOdW1iZXIiLCJpbnB1dFRpcHMiOm51bGwsImRpc2NvdW50VGV4dCI6bnVsbCwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsImFtb3VudFRpcCI6bnVsbCwiY2hhbm5lbENvZGUiOm51bGx9LHsicmVjb21tZW5kIjpmYWxzZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6IjQ1ODU5NTIxIiwiYmlsbGVyTmFtZSI6IlRlblh1blRWIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOiJUZW5YdW5UViIsInN1cmNoYXJnZSI6bnVsbCwic2l0ZVVybE5hbWUiOm51bGwsImlzU2hvdyI6bnVsbCwicmVtYXJrIjpudWxsLCJiaWxsZXJMYWJsZSI6bnVsbCwiYmlsbGVyVGlwcyI6bnVsbCwiaGFzQ291cG9uIjpmYWxzZSwic3VzcGVuZCI6ZmFsc2UsInN0YXR1cyI6MSwiY3JlYXRlVGltZSI6bnVsbCwidXBkYXRlVGltZSI6bnVsbCwiaW5zdGl0dXRpb25Mb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTYzNTc1NDIwNjUxNTE2LWU5MDg0MjUwYjRjMDM4NWVjNDlhYzNhYzBkZjEzNzgyOTY5OGJjYWQ4MTFjNjA0NzlhODZiOTBhNDg0YjI0OGNRenBjVlhObGNuTmNRV1J0YVc1cGMzUnlZWFJ2Y2x4QmNIQkVZWFJoWEZKdllXMXBibWRjUkdsdVoxUmhiR3RjTWpjeE5qazFNelV3WDNZeVhFbHRZV2RsUm1sc1pYTmNNVFl6TlRjMU5ERTRPRGc0TTE5Q05qazFRVGN4TkMxRk5qUXhMVFEyWTJFdFFUWkJPQzB4UWtVM016TTBOakV4UVVFdWNHNW4ucG5nIiwidGlwcyI6bnVsbCwidXNlcklkVGlwcyI6bnVsbCwiaW5wdXRUaXRsZSI6IlNtYXJ0Y2FyZCBOdW1iZXIiLCJpbnB1dFRpcHMiOm51bGwsImRpc2NvdW50VGV4dCI6bnVsbCwibWluQW1vdW50IjowLCJtYXhBbW91bnQiOjEwMDAwMCwiYW1vdW50VGlwIjpudWxsLCJjaGFubmVsQ29kZSI6bnVsbH0seyJyZWNvbW1lbmQiOmZhbHNlLCJpZCI6bnVsbCwiY2F0ZWdvcnlJZCI6IjIiLCJjYXRlZ29yeU5hbWUiOm51bGwsImNhdGVnb3J5RGVzY3JpcHRpb24iOm51bGwsImJpbGxlcklkIjoiNDU4NjI3MjMiLCJiaWxsZXJOYW1lIjoiQWlRaVlpIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOiJBaVFpWWkiLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2MzU3NTczOTY4MzgxLTE2MzU3NTczNzY2MzlfQ0YyNENFRTctMDJEQS00ZDFiLTkwQzUtRDgxQ0RBQzkzRDg3LnBuZyIsInRpcHMiOm51bGwsInVzZXJJZFRpcHMiOm51bGwsImlucHV0VGl0bGUiOiJTbWFydGNhcmQgTnVtYmVyIiwiaW5wdXRUaXBzIjpudWxsLCJkaXNjb3VudFRleHQiOm51bGwsIm1pbkFtb3VudCI6bnVsbCwibWF4QW1vdW50IjpudWxsLCJhbW91bnRUaXAiOm51bGwsImNoYW5uZWxDb2RlIjpudWxsfSx7InJlY29tbWVuZCI6ZmFsc2UsImlkIjpudWxsLCJjYXRlZ29yeUlkIjoiMiIsImNhdGVnb3J5TmFtZSI6bnVsbCwiY2F0ZWdvcnlEZXNjcmlwdGlvbiI6bnVsbCwiYmlsbGVySWQiOiI0NTg3NDgxMyIsImJpbGxlck5hbWUiOiJEU1RWIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOm51bGwsInN1cmNoYXJnZSI6bnVsbCwic2l0ZVVybE5hbWUiOm51bGwsImlzU2hvdyI6bnVsbCwicmVtYXJrIjpudWxsLCJiaWxsZXJMYWJsZSI6bnVsbCwiYmlsbGVyVGlwcyI6bnVsbCwiaGFzQ291cG9uIjpmYWxzZSwic3VzcGVuZCI6ZmFsc2UsInN0YXR1cyI6MSwiY3JlYXRlVGltZSI6bnVsbCwidXBkYXRlVGltZSI6bnVsbCwiaW5zdGl0dXRpb25Mb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTYzNTc2OTQ5OTQ2OTEyLTE2MDUxNDU1NTEzMjcxMURTVFYucG5nIiwidGlwcyI6IiIsInVzZXJJZFRpcHMiOiIxMTExMTEiLCJpbnB1dFRpdGxlIjoiU21hcnRjYXJkIE51bWJlciIsImlucHV0VGlwcyI6IkVudGVyIDExMTExMSIsImRpc2NvdW50VGV4dCI6bnVsbCwibWluQW1vdW50IjowLCJtYXhBbW91bnQiOjAsImFtb3VudFRpcCI6bnVsbCwiY2hhbm5lbENvZGUiOm51bGx9LHsicmVjb21tZW5kIjpmYWxzZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6IjQ5NiIsImJpbGxlck5hbWUiOiJDYWJsZSBBZnJpY2EgIE5ldHdvcmsgVFYoQ2FuVFYpIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOiJDQU50diIsInN1cmNoYXJnZSI6bnVsbCwic2l0ZVVybE5hbWUiOm51bGwsImlzU2hvdyI6bnVsbCwicmVtYXJrIjpudWxsLCJiaWxsZXJMYWJsZSI6bnVsbCwiYmlsbGVyVGlwcyI6bnVsbCwiaGFzQ291cG9uIjpmYWxzZSwic3VzcGVuZCI6ZmFsc2UsInN0YXR1cyI6MSwiY3JlYXRlVGltZSI6bnVsbCwidXBkYXRlVGltZSI6bnVsbCwiaW5zdGl0dXRpb25Mb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFwLXNvdXRoZWFzdC0xLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTYwNTE0NTYxMjkxNTE5Q2FuVFYucG5nIiwidGlwcyI6bnVsbCwidXNlcklkVGlwcyI6bnVsbCwiaW5wdXRUaXRsZSI6IlNtYXJ0Y2FyZCBOdW1iZXIiLCJpbnB1dFRpcHMiOm51bGwsImRpc2NvdW50VGV4dCI6bnVsbCwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsImFtb3VudFRpcCI6bnVsbCwiY2hhbm5lbENvZGUiOm51bGx9LHsicmVjb21tZW5kIjpmYWxzZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6IjUwNDM5OTgwIiwiYmlsbGVyTmFtZSI6InRlc3QgZm9yIHR2IiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOm51bGwsInN1cmNoYXJnZSI6bnVsbCwic2l0ZVVybE5hbWUiOm51bGwsImlzU2hvdyI6bnVsbCwicmVtYXJrIjpudWxsLCJiaWxsZXJMYWJsZSI6bnVsbCwiYmlsbGVyVGlwcyI6bnVsbCwiaGFzQ291cG9uIjpmYWxzZSwic3VzcGVuZCI6ZmFsc2UsInN0YXR1cyI6MSwiY3JlYXRlVGltZSI6bnVsbCwidXBkYXRlVGltZSI6bnVsbCwiaW5zdGl0dXRpb25Mb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTY0MDMzNDY2MDY2MTUtU2NyZWVuc2hvdF8yMDIxMTIyNC0xMTIxMDkucG5nIiwidGlwcyI6bnVsbCwidXNlcklkVGlwcyI6bnVsbCwiaW5wdXRUaXRsZSI6IlNtYXJ0Y2FyZCBOdW1iZXIiLCJpbnB1dFRpcHMiOm51bGwsImRpc2NvdW50VGV4dCI6bnVsbCwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsImFtb3VudFRpcCI6bnVsbCwiY2hhbm5lbENvZGUiOm51bGx9LHsicmVjb21tZW5kIjpmYWxzZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6IjUwNDQwMDI0IiwiYmlsbGVyTmFtZSI6InRlc3QgZm9yIHR2MiIsImJpbGxlckxhYmVsTGlzdCI6bnVsbCwiY3VzdG9tZXJGaWVsZDEiOm51bGwsImN1c3RvbWVyRmllbGQyIjpudWxsLCJwYXlEaXJlY3RQcm9kdWN0SWQiOm51bGwsInBheURpcmVjdEluc3RpdHV0aW9uSWQiOm51bGwsIm5hcnJhdGlvbiI6bnVsbCwic2hvcnROYW1lIjpudWxsLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2NDAzMzQ3MDY0NTM1LVNjcmVlbnNob3RfMjAyMTEyMjQtMTEyMTM2LnBuZyIsInRpcHMiOm51bGwsInVzZXJJZFRpcHMiOm51bGwsImlucHV0VGl0bGUiOiJTbWFydGNhcmQgTnVtYmVyIiwiaW5wdXRUaXBzIjpudWxsLCJkaXNjb3VudFRleHQiOm51bGwsIm1pbkFtb3VudCI6MTAwMCwibWF4QW1vdW50IjoyMDAwMCwiYW1vdW50VGlwIjpudWxsLCJjaGFubmVsQ29kZSI6bnVsbH0seyJyZWNvbW1lbmQiOmZhbHNlLCJpZCI6bnVsbCwiY2F0ZWdvcnlJZCI6IjIiLCJjYXRlZ29yeU5hbWUiOm51bGwsImNhdGVnb3J5RGVzY3JpcHRpb24iOm51bGwsImJpbGxlcklkIjoiNTM2IiwiYmlsbGVyTmFtZSI6IkRTVFYgQm94IE9mZmljZSBXYWxsZXQgVG9wdXAiLCJiaWxsZXJMYWJlbExpc3QiOm51bGwsImN1c3RvbWVyRmllbGQxIjpudWxsLCJjdXN0b21lckZpZWxkMiI6bnVsbCwicGF5RGlyZWN0UHJvZHVjdElkIjpudWxsLCJwYXlEaXJlY3RJbnN0aXR1dGlvbklkIjpudWxsLCJuYXJyYXRpb24iOm51bGwsInNob3J0TmFtZSI6IkJveCBPZmZpY2UiLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hcC1zb3V0aGVhc3QtMS5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2MDUxNDU1MzQwMzc3RFNUViUyMEJveCUyMG9mZmljZS5wbmciLCJ0aXBzIjpudWxsLCJ1c2VySWRUaXBzIjpudWxsLCJpbnB1dFRpdGxlIjoiU21hcnRjYXJkIE51bWJlciIsImlucHV0VGlwcyI6bnVsbCwiZGlzY291bnRUZXh0IjpudWxsLCJtaW5BbW91bnQiOjEwMDAwLCJtYXhBbW91bnQiOjAsImFtb3VudFRpcCI6bnVsbCwiY2hhbm5lbENvZGUiOm51bGx9LHsicmVjb21tZW5kIjpmYWxzZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6IjcyMCIsImJpbGxlck5hbWUiOiJNb250YWdlIENhYmxlIFRWIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOiJNb250YWdlVFYiLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hcC1zb3V0aGVhc3QtMS5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2MDUxNDU2ODgxNzQxMU1vbnRhZ2UlMjBUVi5wbmciLCJ0aXBzIjpudWxsLCJ1c2VySWRUaXBzIjpudWxsLCJpbnB1dFRpdGxlIjoiU21hcnRjYXJkIE51bWJlciIsImlucHV0VGlwcyI6bnVsbCwiZGlzY291bnRUZXh0IjpudWxsLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwiYW1vdW50VGlwIjpudWxsLCJjaGFubmVsQ29kZSI6bnVsbH0seyJyZWNvbW1lbmQiOmZhbHNlLCJpZCI6bnVsbCwiY2F0ZWdvcnlJZCI6IjIiLCJjYXRlZ29yeU5hbWUiOm51bGwsImNhdGVnb3J5RGVzY3JpcHRpb24iOm51bGwsImJpbGxlcklkIjoiNzQwMzkwMzQ4MjI2MDAiLCJiaWxsZXJOYW1lIjoibWFubGluZ3Rlc3QiLCJiaWxsZXJMYWJlbExpc3QiOm51bGwsImN1c3RvbWVyRmllbGQxIjpudWxsLCJjdXN0b21lckZpZWxkMiI6bnVsbCwicGF5RGlyZWN0UHJvZHVjdElkIjpudWxsLCJwYXlEaXJlY3RJbnN0aXR1dGlvbklkIjpudWxsLCJuYXJyYXRpb24iOm51bGwsInNob3J0TmFtZSI6Im1hbmxpbmd0ZXN0Iiwic3VyY2hhcmdlIjpudWxsLCJzaXRlVXJsTmFtZSI6bnVsbCwiaXNTaG93IjpudWxsLCJyZW1hcmsiOm51bGwsImJpbGxlckxhYmxlIjpudWxsLCJiaWxsZXJUaXBzIjpudWxsLCJoYXNDb3Vwb24iOmZhbHNlLCJzdXNwZW5kIjpmYWxzZSwic3RhdHVzIjoxLCJjcmVhdGVUaW1lIjpudWxsLCJ1cGRhdGVUaW1lIjpudWxsLCJpbnN0aXR1dGlvbkxvZ28iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNjYzOTMzNzA3MTIxOS1uaW5pMi5qcGVnIiwidGlwcyI6bnVsbCwidXNlcklkVGlwcyI6bnVsbCwiaW5wdXRUaXRsZSI6IlNtYXJ0Y2FyZCBOdW1iZXIiLCJpbnB1dFRpcHMiOm51bGwsImRpc2NvdW50VGV4dCI6bnVsbCwibWluQW1vdW50IjowLCJtYXhBbW91bnQiOjAsImFtb3VudFRpcCI6bnVsbCwiY2hhbm5lbENvZGUiOm51bGx9LHsicmVjb21tZW5kIjpmYWxzZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6Ijk1MDI0MDk4OTM1ODUwIiwiYmlsbGVyTmFtZSI6IlN0YXJ0aW1lcyBPbiIsImJpbGxlckxhYmVsTGlzdCI6bnVsbCwiY3VzdG9tZXJGaWVsZDEiOm51bGwsImN1c3RvbWVyRmllbGQyIjpudWxsLCJwYXlEaXJlY3RQcm9kdWN0SWQiOm51bGwsInBheURpcmVjdEluc3RpdHV0aW9uSWQiOm51bGwsIm5hcnJhdGlvbiI6bnVsbCwic2hvcnROYW1lIjpudWxsLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2ODQ5MTg3ODA4MDMxMi1zdGFydGltZXNPbi5wbmciLCJ0aXBzIjpudWxsLCJ1c2VySWRUaXBzIjpudWxsLCJpbnB1dFRpdGxlIjoiU21hcnRjYXJkIE51bWJlciIsImlucHV0VGlwcyI6bnVsbCwiZGlzY291bnRUZXh0IjpudWxsLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwiYW1vdW50VGlwIjpudWxsLCJjaGFubmVsQ29kZSI6bnVsbH0seyJyZWNvbW1lbmQiOmZhbHNlLCJpZCI6bnVsbCwiY2F0ZWdvcnlJZCI6IjIiLCJjYXRlZ29yeU5hbWUiOm51bGwsImNhdGVnb3J5RGVzY3JpcHRpb24iOm51bGwsImJpbGxlcklkIjoiOTc3IiwiYmlsbGVyTmFtZSI6IklST0tPdHYiLCJiaWxsZXJMYWJlbExpc3QiOm51bGwsImN1c3RvbWVyRmllbGQxIjpudWxsLCJjdXN0b21lckZpZWxkMiI6bnVsbCwicGF5RGlyZWN0UHJvZHVjdElkIjpudWxsLCJwYXlEaXJlY3RJbnN0aXR1dGlvbklkIjpudWxsLCJuYXJyYXRpb24iOm51bGwsInNob3J0TmFtZSI6ImlST0tPdHYiLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hcC1zb3V0aGVhc3QtMS5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2MDUxNDU0MDg5MDkzaVJPS09UVi5wbmciLCJ0aXBzIjpudWxsLCJ1c2VySWRUaXBzIjpudWxsLCJpbnB1dFRpdGxlIjoiU21hcnRjYXJkIE51bWJlciIsImlucHV0VGlwcyI6bnVsbCwiZGlzY291bnRUZXh0IjpudWxsLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwiYW1vdW50VGlwIjpudWxsLCJjaGFubmVsQ29kZSI6bnVsbH0seyJyZWNvbW1lbmQiOmZhbHNlLCJpZCI6bnVsbCwiY2F0ZWdvcnlJZCI6IjIiLCJjYXRlZ29yeU5hbWUiOm51bGwsImNhdGVnb3J5RGVzY3JpcHRpb24iOm51bGwsImJpbGxlcklkIjoiOTgxMjcxNjc1ODYxMzgwIiwiYmlsbGVyTmFtZSI6InNob3dtYXgiLCJiaWxsZXJMYWJlbExpc3QiOm51bGwsImN1c3RvbWVyRmllbGQxIjpudWxsLCJjdXN0b21lckZpZWxkMiI6bnVsbCwicGF5RGlyZWN0UHJvZHVjdElkIjpudWxsLCJwYXlEaXJlY3RJbnN0aXR1dGlvbklkIjpudWxsLCJuYXJyYXRpb24iOm51bGwsInNob3J0TmFtZSI6bnVsbCwic3VyY2hhcmdlIjpudWxsLCJzaXRlVXJsTmFtZSI6bnVsbCwiaXNTaG93IjpudWxsLCJyZW1hcmsiOm51bGwsImJpbGxlckxhYmxlIjpudWxsLCJiaWxsZXJUaXBzIjpudWxsLCJoYXNDb3Vwb24iOmZhbHNlLCJzdXNwZW5kIjpmYWxzZSwic3RhdHVzIjoxLCJjcmVhdGVUaW1lIjpudWxsLCJ1cGRhdGVUaW1lIjpudWxsLCJpbnN0aXR1dGlvbkxvZ28iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNjg4MDIxODQ3NjM3MTYtY2RlOGViZTIzOGI5MWRiMjkxMDMwZTI1Y2FlNDgzNWMucG5nIiwidGlwcyI6bnVsbCwidXNlcklkVGlwcyI6bnVsbCwiaW5wdXRUaXRsZSI6IlNtYXJ0Y2FyZCBOdW1iZXIiLCJpbnB1dFRpcHMiOm51bGwsImRpc2NvdW50VGV4dCI6bnVsbCwibWluQW1vdW50IjowLCJtYXhBbW91bnQiOjAsImFtb3VudFRpcCI6bnVsbCwiY2hhbm5lbENvZGUiOm51bGx9LHsicmVjb21tZW5kIjp0cnVlLCJpZCI6bnVsbCwiY2F0ZWdvcnlJZCI6IjIiLCJjYXRlZ29yeU5hbWUiOm51bGwsImNhdGVnb3J5RGVzY3JpcHRpb24iOm51bGwsImJpbGxlcklkIjoiNDI2ODkxMjAiLCJiaWxsZXJOYW1lIjoiMDkyNVRWIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOiIwOC1UZXN0Iiwic3VyY2hhcmdlIjpudWxsLCJzaXRlVXJsTmFtZSI6bnVsbCwiaXNTaG93IjpudWxsLCJyZW1hcmsiOm51bGwsImJpbGxlckxhYmxlIjpudWxsLCJiaWxsZXJUaXBzIjpudWxsLCJoYXNDb3Vwb24iOmZhbHNlLCJzdXNwZW5kIjpmYWxzZSwic3RhdHVzIjoxLCJjcmVhdGVUaW1lIjpudWxsLCJ1cGRhdGVUaW1lIjpudWxsLCJpbnN0aXR1dGlvbkxvZ28iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNjMyNTgzODAzNTMxMS1QbGF0Zm9ybSUyMFByb2R1Y3RzLnBuZyIsInRpcHMiOm51bGwsInVzZXJJZFRpcHMiOm51bGwsImlucHV0VGl0bGUiOiJTbWFydGNhcmQgTnVtYmVyIiwiaW5wdXRUaXBzIjpudWxsLCJkaXNjb3VudFRleHQiOm51bGwsIm1pbkFtb3VudCI6MCwibWF4QW1vdW50IjowLCJhbW91bnRUaXAiOm51bGwsImNoYW5uZWxDb2RlIjpudWxsfSx7InJlY29tbWVuZCI6dHJ1ZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6IjQ1OSIsImJpbGxlck5hbWUiOiJHb1RWIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOm51bGwsInN1cmNoYXJnZSI6bnVsbCwic2l0ZVVybE5hbWUiOm51bGwsImlzU2hvdyI6bnVsbCwicmVtYXJrIjpudWxsLCJiaWxsZXJMYWJsZSI6bnVsbCwiYmlsbGVyVGlwcyI6bnVsbCwiaGFzQ291cG9uIjpmYWxzZSwic3VzcGVuZCI6ZmFsc2UsInN0YXR1cyI6MSwiY3JlYXRlVGltZSI6bnVsbCwidXBkYXRlVGltZSI6bnVsbCwiaW5zdGl0dXRpb25Mb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFwLXNvdXRoZWFzdC0xLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTYwNTE0NTUwNTU0MTZHb1RWLnBuZyIsInRpcHMiOm51bGwsInVzZXJJZFRpcHMiOm51bGwsImlucHV0VGl0bGUiOiJTbWFydGNhcmQgTnVtYmVyIiwiaW5wdXRUaXBzIjpudWxsLCJkaXNjb3VudFRleHQiOm51bGwsIm1pbkFtb3VudCI6MCwibWF4QW1vdW50IjowLCJhbW91bnRUaXAiOm51bGwsImNoYW5uZWxDb2RlIjpudWxsfSx7InJlY29tbWVuZCI6dHJ1ZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6IjE2NDkxNDMwIiwiYmlsbGVyTmFtZSI6IkdvVFYiLCJiaWxsZXJMYWJlbExpc3QiOm51bGwsImN1c3RvbWVyRmllbGQxIjpudWxsLCJjdXN0b21lckZpZWxkMiI6bnVsbCwicGF5RGlyZWN0UHJvZHVjdElkIjpudWxsLCJwYXlEaXJlY3RJbnN0aXR1dGlvbklkIjpudWxsLCJuYXJyYXRpb24iOm51bGwsInNob3J0TmFtZSI6IkdvVFYiLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hcC1zb3V0aGVhc3QtMS5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2MDYzODYxMDg1MDMwR29UVi5wbmciLCJ0aXBzIjoiVGhpcyBpcyBHb1R2IEJJTExFUiBUaXBzIiwidXNlcklkVGlwcyI6Inh4eHh4eHh4eHgiLCJpbnB1dFRpdGxlIjoiU21hcnRjYXJkIE51bWJlciIsImlucHV0VGlwcyI6IkVudGVyIHh4eHh4eHh4eHgiLCJkaXNjb3VudFRleHQiOm51bGwsIm1pbkFtb3VudCI6MTAwMDAsIm1heEFtb3VudCI6MTUwMDAwMDAwMDAwMCwiYW1vdW50VGlwIjpudWxsLCJjaGFubmVsQ29kZSI6bnVsbH0seyJyZWNvbW1lbmQiOnRydWUsImlkIjpudWxsLCJjYXRlZ29yeUlkIjoiMiIsImNhdGVnb3J5TmFtZSI6bnVsbCwiY2F0ZWdvcnlEZXNjcmlwdGlvbiI6bnVsbCwiYmlsbGVySWQiOiIzNDEiLCJiaWxsZXJOYW1lIjoiUGxheSBTdWJzY3JpcHRpb24iLCJiaWxsZXJMYWJlbExpc3QiOm51bGwsImN1c3RvbWVyRmllbGQxIjpudWxsLCJjdXN0b21lckZpZWxkMiI6bnVsbCwicGF5RGlyZWN0UHJvZHVjdElkIjpudWxsLCJwYXlEaXJlY3RJbnN0aXR1dGlvbklkIjpudWxsLCJuYXJyYXRpb24iOm51bGwsInNob3J0TmFtZSI6IlBsYXlUViIsInN1cmNoYXJnZSI6bnVsbCwic2l0ZVVybE5hbWUiOm51bGwsImlzU2hvdyI6bnVsbCwicmVtYXJrIjpudWxsLCJiaWxsZXJMYWJsZSI6bnVsbCwiYmlsbGVyVGlwcyI6bnVsbCwiaGFzQ291cG9uIjpmYWxzZSwic3VzcGVuZCI6ZmFsc2UsInN0YXR1cyI6MSwiY3JlYXRlVGltZSI6bnVsbCwidXBkYXRlVGltZSI6bnVsbCwiaW5zdGl0dXRpb25Mb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFwLXNvdXRoZWFzdC0xLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTYwNTE0NTMwOTYyMzIwUGxheSUyMHN1YnNjcmlwdGlvbi5wbmciLCJ0aXBzIjpudWxsLCJ1c2VySWRUaXBzIjpudWxsLCJpbnB1dFRpdGxlIjoiU21hcnRjYXJkIE51bWJlciIsImlucHV0VGlwcyI6bnVsbCwiZGlzY291bnRUZXh0IjpudWxsLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwiYW1vdW50VGlwIjpudWxsLCJjaGFubmVsQ29kZSI6bnVsbH0seyJyZWNvbW1lbmQiOnRydWUsImlkIjpudWxsLCJjYXRlZ29yeUlkIjoiMiIsImNhdGVnb3J5TmFtZSI6bnVsbCwiY2F0ZWdvcnlEZXNjcmlwdGlvbiI6bnVsbCwiYmlsbGVySWQiOiI1MDQzNTc3NyIsImJpbGxlck5hbWUiOiJsaXV3ZW50ZXN0IiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOm51bGwsInN1cmNoYXJnZSI6bnVsbCwic2l0ZVVybE5hbWUiOm51bGwsImlzU2hvdyI6bnVsbCwicmVtYXJrIjpudWxsLCJiaWxsZXJMYWJsZSI6bnVsbCwiYmlsbGVyVGlwcyI6bnVsbCwiaGFzQ291cG9uIjpmYWxzZSwic3VzcGVuZCI6ZmFsc2UsInN0YXR1cyI6MSwiY3JlYXRlVGltZSI6bnVsbCwidXBkYXRlVGltZSI6bnVsbCwiaW5zdGl0dXRpb25Mb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTY0MDMzMDQ2NTE5OTItJUU1JTg4JTk4JUU2JTk2JTg3LmpwZyIsInRpcHMiOm51bGwsInVzZXJJZFRpcHMiOm51bGwsImlucHV0VGl0bGUiOiJTbWFydGNhcmQgTnVtYmVyIiwiaW5wdXRUaXBzIjpudWxsLCJkaXNjb3VudFRleHQiOm51bGwsIm1pbkFtb3VudCI6bnVsbCwibWF4QW1vdW50IjpudWxsLCJhbW91bnRUaXAiOm51bGwsImNoYW5uZWxDb2RlIjpudWxsfSx7InJlY29tbWVuZCI6dHJ1ZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6IjUwNDQwNzY3IiwiYmlsbGVyTmFtZSI6IkRTVFYxMjMiLCJiaWxsZXJMYWJlbExpc3QiOm51bGwsImN1c3RvbWVyRmllbGQxIjpudWxsLCJjdXN0b21lckZpZWxkMiI6bnVsbCwicGF5RGlyZWN0UHJvZHVjdElkIjpudWxsLCJwYXlEaXJlY3RJbnN0aXR1dGlvbklkIjpudWxsLCJuYXJyYXRpb24iOm51bGwsInNob3J0TmFtZSI6bnVsbCwic3VyY2hhcmdlIjpudWxsLCJzaXRlVXJsTmFtZSI6bnVsbCwiaXNTaG93IjpudWxsLCJyZW1hcmsiOm51bGwsImJpbGxlckxhYmxlIjpudWxsLCJiaWxsZXJUaXBzIjpudWxsLCJoYXNDb3Vwb24iOmZhbHNlLCJzdXNwZW5kIjpmYWxzZSwic3RhdHVzIjoxLCJjcmVhdGVUaW1lIjpudWxsLCJ1cGRhdGVUaW1lIjpudWxsLCJpbnN0aXR1dGlvbkxvZ28iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNjQwMzM1NDQyMjU3MTItJUU5JTlDJUI4JUU1JTg4JTgwLmpwZyIsInRpcHMiOm51bGwsInVzZXJJZFRpcHMiOm51bGwsImlucHV0VGl0bGUiOiJTbWFydGNhcmQgTnVtYmVyIiwiaW5wdXRUaXBzIjpudWxsLCJkaXNjb3VudFRleHQiOm51bGwsIm1pbkFtb3VudCI6bnVsbCwibWF4QW1vdW50IjpudWxsLCJhbW91bnRUaXAiOm51bGwsImNoYW5uZWxDb2RlIjpudWxsfSx7InJlY29tbWVuZCI6dHJ1ZSwiaWQiOm51bGwsImNhdGVnb3J5SWQiOiIyIiwiY2F0ZWdvcnlOYW1lIjpudWxsLCJjYXRlZ29yeURlc2NyaXB0aW9uIjpudWxsLCJiaWxsZXJJZCI6IjUwNDQwNDg1IiwiYmlsbGVyTmFtZSI6InRlc3QxIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOm51bGwsInN1cmNoYXJnZSI6bnVsbCwic2l0ZVVybE5hbWUiOm51bGwsImlzU2hvdyI6bnVsbCwicmVtYXJrIjpudWxsLCJiaWxsZXJMYWJsZSI6bnVsbCwiYmlsbGVyVGlwcyI6bnVsbCwiaGFzQ291cG9uIjpmYWxzZSwic3VzcGVuZCI6ZmFsc2UsInN0YXR1cyI6MSwiY3JlYXRlVGltZSI6bnVsbCwidXBkYXRlVGltZSI6bnVsbCwiaW5zdGl0dXRpb25Mb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTY0MDMzNTE2ODY4MTE4LTEyMy5wbmciLCJ0aXBzIjpudWxsLCJ1c2VySWRUaXBzIjpudWxsLCJpbnB1dFRpdGxlIjoiU21hcnRjYXJkIE51bWJlciIsImlucHV0VGlwcyI6bnVsbCwiZGlzY291bnRUZXh0IjpudWxsLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwiYW1vdW50VGlwIjpudWxsLCJjaGFubmVsQ29kZSI6bnVsbH0seyJyZWNvbW1lbmQiOnRydWUsImlkIjpudWxsLCJjYXRlZ29yeUlkIjoiMiIsImNhdGVnb3J5TmFtZSI6bnVsbCwiY2F0ZWdvcnlEZXNjcmlwdGlvbiI6bnVsbCwiYmlsbGVySWQiOiI1MDQzNTgxMyIsImJpbGxlck5hbWUiOiJsaXdlbnRlczIyMiIsImJpbGxlckxhYmVsTGlzdCI6bnVsbCwiY3VzdG9tZXJGaWVsZDEiOm51bGwsImN1c3RvbWVyRmllbGQyIjpudWxsLCJwYXlEaXJlY3RQcm9kdWN0SWQiOm51bGwsInBheURpcmVjdEluc3RpdHV0aW9uSWQiOm51bGwsIm5hcnJhdGlvbiI6bnVsbCwic2hvcnROYW1lIjpudWxsLCJzdXJjaGFyZ2UiOm51bGwsInNpdGVVcmxOYW1lIjpudWxsLCJpc1Nob3ciOm51bGwsInJlbWFyayI6bnVsbCwiYmlsbGVyTGFibGUiOm51bGwsImJpbGxlclRpcHMiOm51bGwsImhhc0NvdXBvbiI6ZmFsc2UsInN1c3BlbmQiOmZhbHNlLCJzdGF0dXMiOjEsImNyZWF0ZVRpbWUiOm51bGwsInVwZGF0ZVRpbWUiOm51bGwsImluc3RpdHV0aW9uTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2NDAzMzA0OTQ4MDE5LSVFNSU4OCU5OCVFNiU5NiU4Ny5qcGciLCJ0aXBzIjpudWxsLCJ1c2VySWRUaXBzIjpudWxsLCJpbnB1dFRpdGxlIjoiU21hcnRjYXJkIE51bWJlciIsImlucHV0VGlwcyI6bnVsbCwiZGlzY291bnRUZXh0IjpudWxsLCJtaW5BbW91bnQiOm51bGwsIm1heEFtb3VudCI6bnVsbCwiYW1vdW50VGlwIjpudWxsLCJjaGFubmVsQ29kZSI6bnVsbH0seyJyZWNvbW1lbmQiOnRydWUsImlkIjpudWxsLCJjYXRlZ29yeUlkIjoiMiIsImNhdGVnb3J5TmFtZSI6bnVsbCwiY2F0ZWdvcnlEZXNjcmlwdGlvbiI6bnVsbCwiYmlsbGVySWQiOiI1MDQzNTg2NCIsImJpbGxlck5hbWUiOiIxMzQzNDM0MzQiLCJiaWxsZXJMYWJlbExpc3QiOm51bGwsImN1c3RvbWVyRmllbGQxIjpudWxsLCJjdXN0b21lckZpZWxkMiI6bnVsbCwicGF5RGlyZWN0UHJvZHVjdElkIjpudWxsLCJwYXlEaXJlY3RJbnN0aXR1dGlvbklkIjpudWxsLCJuYXJyYXRpb24iOm51bGwsInNob3J0TmFtZSI6bnVsbCwic3VyY2hhcmdlIjpudWxsLCJzaXRlVXJsTmFtZSI6bnVsbCwiaXNTaG93IjpudWxsLCJyZW1hcmsiOm51bGwsImJpbGxlckxhYmxlIjpudWxsLCJiaWxsZXJUaXBzIjpudWxsLCJoYXNDb3Vwb24iOmZhbHNlLCJzdXNwZW5kIjpmYWxzZSwic3RhdHVzIjoxLCJjcmVhdGVUaW1lIjpudWxsLCJ1cGRhdGVUaW1lIjpudWxsLCJpbnN0aXR1dGlvbkxvZ28iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNjQwMzMwNTQ0NjIxOS0lRTUlODglOTglRTYlOTYlODcuanBnIiwidGlwcyI6bnVsbCwidXNlcklkVGlwcyI6bnVsbCwiaW5wdXRUaXRsZSI6IlNtYXJ0Y2FyZCBOdW1iZXIiLCJpbnB1dFRpcHMiOm51bGwsImRpc2NvdW50VGV4dCI6bnVsbCwibWluQW1vdW50IjpudWxsLCJtYXhBbW91bnQiOm51bGwsImFtb3VudFRpcCI6bnVsbCwiY2hhbm5lbENvZGUiOm51bGx9LHsicmVjb21tZW5kIjp0cnVlLCJpZCI6bnVsbCwiY2F0ZWdvcnlJZCI6IjIiLCJjYXRlZ29yeU5hbWUiOm51bGwsImNhdGVnb3J5RGVzY3JpcHRpb24iOm51bGwsImJpbGxlcklkIjoiMTA0IiwiYmlsbGVyTmFtZSI6IkRTVFYgU3Vic2NyaXB0aW9uIiwiYmlsbGVyTGFiZWxMaXN0IjpudWxsLCJjdXN0b21lckZpZWxkMSI6bnVsbCwiY3VzdG9tZXJGaWVsZDIiOm51bGwsInBheURpcmVjdFByb2R1Y3RJZCI6bnVsbCwicGF5RGlyZWN0SW5zdGl0dXRpb25JZCI6bnVsbCwibmFycmF0aW9uIjpudWxsLCJzaG9ydE5hbWUiOiJEU1RWIiwic3VyY2hhcmdlIjpudWxsLCJzaXRlVXJsTmFtZSI6bnVsbCwiaXNTaG93IjpudWxsLCJyZW1hcmsiOm51bGwsImJpbGxlckxhYmxlIjpudWxsLCJiaWxsZXJUaXBzIjpudWxsLCJoYXNDb3Vwb24iOmZhbHNlLCJzdXNwZW5kIjpmYWxzZSwic3RhdHVzIjoxLCJjcmVhdGVUaW1lIjpudWxsLCJ1cGRhdGVUaW1lIjpudWxsLCJpbnN0aXR1dGlvbkxvZ28iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYXAtc291dGhlYXN0LTEuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNjA1MTQ1NTUxMzI3MTFEU1RWLnBuZyIsInRpcHMiOm51bGwsInVzZXJJZFRpcHMiOm51bGwsImlucHV0VGl0bGUiOiJTbWFydGNhcmQgTnVtYmVyIiwiaW5wdXRUaXBzIjpudWxsLCJkaXNjb3VudFRleHQiOm51bGwsIm1pbkFtb3VudCI6MCwibWF4QW1vdW50IjowLCJhbW91bnRUaXAiOm51bGwsImNoYW5uZWxDb2RlIjpudWxsfV19", "encoding": "base64"}, "redirectURL": null, "headersSize": 172, "bodySize": 2178}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 937, "receive": 10}}, {"startedDateTime": "2024-05-20T17:56:17.218+08:00", "time": 229, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/pay/queryLimitAmtForOrder", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/pay/queryLimitAmtForOrder"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976462"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "eQWjTxKR428EsxjUbLFyJD8yEoiDWN8UE4M9ynrxrp%2FsseAChU3K9zqBsOfZUFzPaw7ZOw8Um6gTkDDWE%2B8Wwep%2BGNb5qS%2Bsflv2%2B1lBbEoI5Wy86tJUYtcZmX2hgqEcJSRmGmiQsN9eN2A4O02d2isAV5DV8XmJP6u5XhLgeHY%3D"}, {"name": "pp_req_sign_2", "value": "D%2BYYdVaKc7HmH8nCMCaHmffWfJ33nmJSuxWov7m9FQH%2BIWkkhNfnkJXaqtBENF1x5Zs9sKaoXzg0d0how1vHt9Zw8VNjABbX4jNlqxET0nwdeQClkWwXzpiHoaeL1eJMqHM8jbEA%2F5tn9PpvzGEnuiNq0pVa0ePIgwes49vumIg%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "18"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"transType\":\"17\"}"}, "headersSize": 354, "bodySize": 18}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "114"}, {"name": "eagleeye-traceid", "value": "eaac16282817161989774062028d0001"}, {"name": "x-envoy-upstream-service-time", "value": "30"}], "content": {"size": 114, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7Im1pbkFtb3VudCI6MTAwLCJtYXhBbW91bnQiOjkyMjMzNzIwMzY4NTQ3NzU4MDd9LCJzdGF0dXMiOnRydWV9", "encoding": "base64"}, "redirectURL": null, "headersSize": 105, "bodySize": 114}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 228, "receive": 0}}, {"startedDateTime": "2024-05-20T17:56:17.218+08:00", "time": 231, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/live/tv/customerId/history", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/live/tv/customerId/history"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976464"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "JJMZZTp8I8L8VL0mxWfnUSll3%2BQrSEu3oxpqpxaSOk8E8XoSW%2FcURR%2BFvUbWjSulTpav4wCNLbkFK2x9Y%2FW1kc3eGGzqpnxRFUI3zwRe5RJ4DB9ox5KpZEd5upPv30%2F48gwMUQ6kA33E10BinYzI%2Fwxw5rphCMW9S5LCjeKSRuE%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Mon, 20 May 2024 09:56:01 GMT"}], "queryString": [], "headersSize": 209, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "content-length", "value": "53"}, {"name": "eagleeye-traceid", "value": "eaac162bc217161989774052366d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "31"}], "content": {"size": 53, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbXX0=", "encoding": "base64"}, "redirectURL": null, "headersSize": 145, "bodySize": 53}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 231, "receive": 0}}, {"startedDateTime": "2024-05-20T17:56:17.245+08:00", "time": 205, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/dataBurialPoint/adBehavior", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/dataBurialPoint/adBehavior"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json"}, {"name": "countrycode", "value": "NG"}, {"name": "signature", "value": "dYJw8HAxSihcjGpPc97OTVS8tk3IjSTVcWXyfEL/eMb4SEU+eiwdyknvamU71iI+B3Mqhss1JbT75IUlvrh7aa1kBIU65cSOdnF+vQrOEU/WwR21KSvXnxqjfiDgSboMBezoWnP3Yt8qAN0kApPbGraCROWf48hTmCRYD8zgvXw="}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "1695"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json", "text": "{\"bizInfo\":\"[{\\\"body\\\":{\\\"adId\\\":\\\"4D873EF03C7245CC91B7BBACBFBD5919\\\",\\\"adSlotId\\\":\\\"3FC9C7B4C09241F8A1BF9AB1A68AF753\\\",\\\"adTime\\\":1716198976524,\\\"countryCode\\\":\\\"NG\\\",\\\"eventSubType\\\":\\\"\\\",\\\"eventType\\\":\\\"autoBegin\\\",\\\"num\\\":1,\\\"operator\\\":\\\"\\\",\\\"pointWindowX\\\":\\\"\\\",\\\"pointWindowY\\\":\\\"\\\",\\\"relatedId\\\":\\\"\\\",\\\"userId\\\":\\\"\\\",\\\"wifi\\\":true},\\\"head\\\":{\\\"SDKVersion\\\":\\\"1.0.36-SNAPSHOT\\\",\\\"appIdentifier\\\":\\\"com.transsnet.palmpay\\\",\\\"appVersion\\\":\\\"5.8.0\\\",\\\"appVersionCode\\\":605151059,\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"behaviorId\\\":\\\"B795C65A0EA3476F985A4029A85669161716198976524\\\",\\\"createTime\\\":1716198976528,\\\"eventTime\\\":1716198976528,\\\"gaid\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"imei\\\":\\\"\\\",\\\"ip\\\":\\\"*************\\\",\\\"macAddress\\\":\\\"please open wifi\\\",\\\"operSystem\\\":\\\"Android\\\"}},{\\\"body\\\":{\\\"adId\\\":\\\"6C6A0A5AF92F4BE0B8A1F0DE158F9664\\\",\\\"adSlotId\\\":\\\"5F7CAA79D3F247AD986B94D3807E6B97\\\",\\\"adTime\\\":1716198961957,\\\"countryCode\\\":\\\"NG\\\",\\\"eventSubType\\\":\\\"\\\",\\\"eventType\\\":\\\"autoBegin\\\",\\\"num\\\":1,\\\"operator\\\":\\\"\\\",\\\"pointWindowX\\\":\\\"\\\",\\\"pointWindowY\\\":\\\"\\\",\\\"relatedId\\\":\\\"\\\",\\\"userId\\\":\\\"\\\",\\\"wifi\\\":true},\\\"head\\\":{\\\"SDKVersion\\\":\\\"1.0.36-SNAPSHOT\\\",\\\"appIdentifier\\\":\\\"com.transsnet.palmpay\\\",\\\"appVersion\\\":\\\"5.8.0\\\",\\\"appVersionCode\\\":605151059,\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"behaviorId\\\":\\\"B795C65A0EA3476F985A4029A85669161716198961957\\\",\\\"createTime\\\":1716198961960,\\\"eventTime\\\":1716198961960,\\\"gaid\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"imei\\\":\\\"\\\",\\\"ip\\\":\\\"*************\\\",\\\"macAddress\\\":\\\"please open wifi\\\",\\\"operSystem\\\":\\\"Android\\\"}}]\",\"nonceStr\":\"LtS07EkJ\",\"requestTime\":1716198976533,\"version\":\"1.0.36-SNAPSHOT\"}"}, "headersSize": 194, "bodySize": 1695}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "analysis:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "1"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 55, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpudWxsfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 439, "bodySize": 70}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 199, "receive": 5}}, {"startedDateTime": "2024-05-20T17:56:17.274+08:00", "time": 224, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/airtime/v2/query/auto-onoff-sale-info", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/airtime/v2/query/auto-onoff-sale-info"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976522"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "NXSG5zZb4Cw9g6wWt%2BnvB%2B%2BiHkHIgKabOFBGEUHQHqcFbdbEwA40dX0RiYQKFeAArP6N53bQESNWcdrKvF8afDjvAhA9qKAOEjZiXFasjbLxAETdwW9sGviC0Z8H6svvgxEq3mg4KRb8V8TnRDDB5GnRGOrFhnWsAwmdJsrqpuw%3D"}, {"name": "pp_req_sign_2", "value": "pK%2BygiaU70qHER%2BTiR<PERSON><PERSON>%2B8472y%2FHu%2BT2%2FeggGuYVh9QKFssEUcENh%2BDKiyCCK2DfnuIpBOf4UNL%2BTktGD21h0cgekE06jseX2OkiIcFVkFgQbCnGP933qBBVbkTofq4SUpP9J0j3bNO8Tc4udDq%2BjFu5xw98zNxHX70zs6kuV4%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "20"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"categoryId\":[\"2\"]}"}, "headersSize": 210, "bodySize": 20}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "67"}, {"name": "eagleeye-traceid", "value": "eaac16282817161989774612029d0001"}, {"name": "x-envoy-upstream-service-time", "value": "26"}], "content": {"size": 67, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbXSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 104, "bodySize": 67}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 224, "receive": 0}}, {"startedDateTime": "2024-05-20T17:56:17.274+08:00", "time": 512, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/quickteller/getLastBill", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/quickteller/getLastBill"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976556"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "ZtMNp0q54eHhhRxBZhR%2Bh4A%2BvrLzuJZYpm3tQMhI3ALu9F6FEz8tKXBBCBXogspg4PTb01jYtrPz2GoF39Efvkvbs0k91vTkuvU2nG9iUUq8x52MKLJvS6mOVncfcTmHEkgVSNqXBWBOscvEJpFlex%2BOv5q8k4kBRcVvR9GDOmU%3D"}, {"name": "pp_req_sign_2", "value": "Dmvlro9jE2kyu5LR4U%2Bd9NDMvoRb%2F4I1HgeS5CvlTCSTe30O7R5iey6lHgngU5iNpejEbVov37LDNVoxJQsT3iyLOmhJgNKtCFSBKfB87yI9nT0GWNlom7hlOrqg%2BkfljoLaTjQLH8ArZNvnxFAcYG5%2FCCRwDUfCkZ4Izn3ZPQI%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "18"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"categoryId\":\"2\"}"}, "headersSize": 441, "bodySize": 18}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162bc217161989774612369d0001"}, {"name": "x-envoy-upstream-service-time", "value": "309"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 2744, "compression": 1524, "mimeType": "application/json", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 131, "bodySize": 1220}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 507, "receive": 4}}, {"startedDateTime": "2024-05-20T17:56:17.29+08:00", "time": 209, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "U7zpyJyqDboQcDBmcRR3O10H5hr1uHl0KLjq0ugE8kW+zk0/qNRadXtEJZvtWTv98e8XUqFWC8sdREsYJf2O7PF7aBCk8rWkiZNDAgPYVt6KJ+U3TUl/Q8jQ05gIQuEwaAYwXGmSDbhdocE2UmSO+WDvr0po+nOyZdmdIzpnCSQ="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.8.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"FB6AE414D5D94D4FA2A2EA53D936EB4F\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"iyYG0loA\",\"requestTime\":1716198976574,\"version\":\"1.0\"}"}, "headersSize": 160, "bodySize": 512}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "ad-center:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "7"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 84, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImV4cGlyZWRUaW1lIjo2MDAwMCwibGlzdCI6bnVsbH19", "encoding": "base64"}, "redirectURL": null, "headersSize": 439, "bodySize": 96}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 205, "receive": 4}}, {"startedDateTime": "2024-05-20T17:56:17.313+08:00", "time": 320, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/airtime/cashback/getBizConfig", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/airtime/cashback/getBizConfig"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976572"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "NRuC%2Bky6aLSccyC7eKyT0gPvSN3k6flDA4zth1HKD7pd9UFqwnqB%2B4KFXtVVOWd73pQiIRUiUsN%2BC9fgOVfHv%2BT%2FeuoMgDRdEX3ILI56rQuFFfLoUIOR4tGNldKTWZZJpaPCTeAriTFQfGZgXBJ5OwWOhzODLqUJsutY0CtIEeM%3D"}, {"name": "pp_req_sign_2", "value": "D%2BYYdVaKc7HmH8nCMCaHmffWfJ33nmJSuxWov7m9FQH%2BIWkkhNfnkJXaqtBENF1x5Zs9sKaoXzg0d0how1vHt9Zw8VNjABbX4jNlqxET0nwdeQClkWwXzpiHoaeL1eJMqHM8jbEA%2F5tn9PpvzGEnuiNq0pVa0ePIgwes49vumIg%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "18"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"transType\":\"17\"}"}, "headersSize": 229, "bodySize": 18}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "content-length", "value": "485"}, {"name": "eagleeye-traceid", "value": "eaac162bc217161989775012370d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "120"}], "content": {"size": 485, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7InRyYW5zVHlwZSI6IjE3IiwiaWNvbiI6Imh0dHBzOi8vdHJhbnNzbmV0LWFwcC1pbWFnZXMtcHJvZC5zMy5ldS13ZXN0LTEuYW1hem9uYXdzLmNvbS8yMDIzMDcxNS82NGIyMTcwN2JhNTNjMjEwMjEwNDM5ZmEucG5nIiwiaGludCI6bnVsbCwiY29udGVudCI6IjxiPjxmb250IGNvbG9yPVwiI0ZGQUEwQ1wiPjUwMDQgUGFsbVBvaW50czwvZm9udD48L2I+IGF2YWlsYWJsZSIsImRhcmtDb250ZW50IjoiXCI8Yj48Zm9udCBjb2xvcj1cIiNFNjk1MDBcIj41MDA0IFBhbG1Qb2ludHM8L2ZvbnQ+PGZvbnQgY29sb3I9XCIjRkZGRkZGXCI+IGF2YWlsYWJsZTwvZm9udD48L2I+XCIiLCJ1cmwiOiIvbWFpbi9wb2ludC9saXN0IiwiYW5kcmlvZFVybCI6Ii9tYWluL3BvaW50L2xpc3QiLCJpb3NVcmwiOiJwYWxtcGF5Oi8vbWFpbi9wb2ludHNzdGF0ZW1lbnQ/dGFnPTIifX0=", "encoding": "base64"}, "redirectURL": null, "headersSize": 146, "bodySize": 485}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 320, "receive": 0}}, {"startedDateTime": "2024-05-20T17:56:17.313+08:00", "time": 243, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=17", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=17"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976573"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "YOOWFyyO1th91XaVpMdTVUTy9vJWgeEv9JN7ZoDw%2Fup2KHGNrD%2Bw5AoUwxZOPpCQg3kgqMhDF8ZkdLBd1jp8qYdFmDjJ19XnogTUFFhwnLOCdfAjTzyLm4EAGLht5afvCgX%2FV3%2FsEBL%2BCbuF7B0BkawgYEZEjIsLSzL7HfM4s7c%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Mon, 20 May 2024 09:56:10 GMT"}], "queryString": [{"name": "showLocation", "value": "17"}], "headersSize": 256, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "67"}, {"name": "eagleeye-traceid", "value": "eaac16282817161989775002031d0001"}, {"name": "x-envoy-upstream-service-time", "value": "43"}], "content": {"size": 67, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbXSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 104, "bodySize": 67}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 242, "receive": 1}}, {"startedDateTime": "2024-05-20T17:56:17.313+08:00", "time": 254, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/payBills?location=17", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/payBills?location=17"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976559"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "wlDr3vjwc4HvderSbeYJx%2FVq%2BIJ4BxVUxjSQZjflPnNHS4dcC6%2FbdNSt4OaaID8wCbBxmvqh7RpvUQi2zYZ1UEFnrGvsXzbHhyJmXPq4UZN60V%2B2wTCcCbCJVI%2BFu2k0eCEj5FLInSVns6TmAw%2FSV%2FdA%2FXoh3hmMaL6e3O8lfVI%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Mon, 20 May 2024 09:56:12 GMT"}], "queryString": [{"name": "location", "value": "17"}], "headersSize": 278, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac16282817161989775002030d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "52"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 2476, "compression": 1864, "mimeType": "application/json;charset=UTF-8", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 171, "bodySize": 612}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 250, "receive": 4}}, {"startedDateTime": "2024-05-20T17:56:17.313+08:00", "time": 1194, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/live/tv/item?billerId=459", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/live/tv/item?billerId=459"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976594"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "ZzMCtXmDXHbmGc%2B1RzT2lqjsAfomYOb1RO11aqGkFdzpczmRvAAxdIt5lcOLh%2Fy6FKVcfDXNP6tEfERM5bkQSpLwV0ACwrNzunbtIFnf4Kh03y0AqUj3dQkl%2Br3O4A2neUFEP5dTsvDr4nDsrdpVW6QTce0o3UOgdZZsfWdy%2BLo%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Mon, 20 May 2024 09:56:12 GMT"}], "queryString": [{"name": "billerId", "value": "459"}], "headersSize": 203, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:18 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162bc217161989775012371d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "990"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 2226, "compression": 1809, "mimeType": "application/json;charset=UTF-8", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 172, "bodySize": 417}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 1190, "receive": 4}}, {"startedDateTime": "2024-05-20T17:56:17.313+08:00", "time": 1464, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/live/tv/item/tips?billerId=459", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/live/tv/item/tips?billerId=459"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976594"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "ZzMCtXmDXHbmGc%2B1RzT2lqjsAfomYOb1RO11aqGkFdzpczmRvAAxdIt5lcOLh%2Fy6FKVcfDXNP6tEfERM5bkQSpLwV0ACwrNzunbtIFnf4Kh03y0AqUj3dQkl%2Br3O4A2neUFEP5dTsvDr4nDsrdpVW6QTce0o3UOgdZZsfWdy%2BLo%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Mon, 20 May 2024 09:56:12 GMT"}], "queryString": [{"name": "billerId", "value": "459"}], "headersSize": 47, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:18 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "content-length", "value": "782"}, {"name": "eagleeye-traceid", "value": "eaac16282817161989775062032d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "1257"}], "content": {"size": 782, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbeyJiaWxsZXJOYW1lIjoiNDU5IiwiYmlsbGVySWQiOiI0NTkiLCJwYXltZW50SXRlbUlkIjoiMjAxOTA2MDUxNDExMDc3NTAyODkwODg2NiIsIml0ZW1JZCI6IjIwMTkwNjA1MTQxMTA3NzUwMjg5MDg4NjYiLCJyZW1hcmsiOm51bGx9LHsiYmlsbGVyTmFtZSI6IjQ1OSIsImJpbGxlcklkIjoiNDU5IiwicGF5bWVudEl0ZW1JZCI6IjIwMTkwNjA1MTQxMTA3NzUwMzExNjg3MjIiLCJpdGVtSWQiOiIyMDE5MDYwNTE0MTEwNzc1MDMxMTY4NzIyIiwicmVtYXJrIjpudWxsfSx7ImJpbGxlck5hbWUiOiI0NTkiLCJiaWxsZXJJZCI6IjQ1OSIsInBheW1lbnRJdGVtSWQiOiIyMDE5MDYwNTE0MTEwNzc1MDI4NTg2NTU1IiwiaXRlbUlkIjoiMjAxOTA2MDUxNDExMDc3NTAyODU4NjU1NSIsInJlbWFyayI6bnVsbH0seyJiaWxsZXJOYW1lIjoiNDU5IiwiYmlsbGVySWQiOiI0NTkiLCJwYXltZW50SXRlbUlkIjoiMjAxOTA2MDUxNDExMDc3NTA2Mzc5MzA0OCIsIml0ZW1JZCI6IjIwMTkwNjA1MTQxMTA3NzUwNjM3OTMwNDgiLCJyZW1hcmsiOm51bGx9LHsiYmlsbGVyTmFtZSI6IjQ1OSIsImJpbGxlcklkIjoiNDU5IiwicGF5bWVudEl0ZW1JZCI6IjE2NDkwOTU0IiwiaXRlbUlkIjoiMTY0OTA5NTQiLCJyZW1hcmsiOm51bGx9LHsiYmlsbGVyTmFtZSI6IjQ1OSIsImJpbGxlcklkIjoiNDU5IiwicGF5bWVudEl0ZW1JZCI6IjQyNjg5MDY4IiwiaXRlbUlkIjoiNDI2ODkwNjgiLCJyZW1hcmsiOm51bGx9XX0=", "encoding": "base64"}, "redirectURL": null, "headersSize": 147, "bodySize": 782}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 1464, "receive": 0}}, {"startedDateTime": "2024-05-20T17:56:17.318+08:00", "time": 235, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/coupon/statistics", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/coupon/statistics"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976603"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "hPwxFUfGD6aOhyVeGy9EgWQJxRR0wA53T6YY%2B8d80iZhcSQU7GUa%2BFTdeyJZYrPMBzZfjEmuarDQb3e4hGjVYuIgcb0pp%2BuLNVBRbR%2F1Zo%2BxvYJkgtGk8ERoTnYGSV5ffBfS4OzpNH0nfC%2FEa%2FDFTrCtSRKcYsWB02bLb%2F0AdGo%3D"}, {"name": "pp_req_sign_2", "value": "otfWUehjYoVpIMgL%2BLEeIgGtQzK%2FIGj0ejfJap9wLg9nkuBPgWzWUFhucdceCbHzik8JQQ9OoscjOs3pfDb5ptntEd1PafsCLptl44KXYUSpstYpqoW1RAvi7tRCiZSiDTU%2ByYFMO%2FEXic4aksUflVDKj6gLVqZ4ayBkVYdeddg%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "45"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"pageNum\":1,\"pageSize\":100,\"transType\":\"17\"}"}, "headersSize": 353, "bodySize": 45}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "157"}, {"name": "eagleeye-traceid", "value": "eaac162bc217161989775062372d0001"}, {"name": "x-envoy-upstream-service-time", "value": "35"}], "content": {"size": 157, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7InRvdGFsIjowLCJ0b3RhbFBhZ2UiOjAsImN1clBhZ2UiOjEsInBhZ2VTaXplIjoxMDAsImRhdGFNYXAiOm51bGwsImV4dERhdGEiOm51bGwsImxpc3QiOltdfSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 105, "bodySize": 157}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 235, "receive": 0}}, {"startedDateTime": "2024-05-20T17:56:17.343+08:00", "time": 208, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "fgBvHst+LDBqKDf4VIRsxs2M6iEe2GWdON9LdWE8olhcZ9pwFz9vteyHGvLqXEFBTEPG535dZIsr7iHEYRgYSwv1VTHlObC23M7ebZo9homgti+5BqYybwf5aJ2QKGpgMIlXgJ8EQzMVJ5G+FdBqodwbaWt043P0UfpJF+kyftk="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.8.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"E02DFD3BD7C9461B8CBEADBDEF9B0DCA\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"2Ivy2wQQ\",\"requestTime\":1716198976574,\"version\":\"1.0\"}"}, "headersSize": 158, "bodySize": 512}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "ad-center:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "7"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 84, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImV4cGlyZWRUaW1lIjo2MDAwMCwibGlzdCI6bnVsbH19", "encoding": "base64"}, "redirectURL": null, "headersSize": 439, "bodySize": 96}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 207, "receive": 1}}, {"startedDateTime": "2024-05-20T17:56:17.414+08:00", "time": 218, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/payBills?location=17", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/payBills?location=17"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&605151059"}, {"name": "pp_timestamp", "value": "1716198976701"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&605151059 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "bXRkPPv7S4Jot635Eh4fBlkPokNFE%2B03Nxxg%2BdR4KFQmlP6lOlwbmKZSGd1nMKUQFryDilB%2Be1cxnlab7%2Ff67ToCVRg8uW1W%2BDyQvH7mok7asTdwEzISo4RVikPgjGY20FYD9d3GDtHVBcTzB6RfsvnIXzxffe%2FVIFSuzehCdRA%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Mon, 20 May 2024 09:56:12 GMT"}], "queryString": [{"name": "location", "value": "17"}], "headersSize": 190, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:56:17 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac16282817161989776022033d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "17"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 2476, "compression": 1828, "mimeType": "application/json;charset=UTF-8", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 171, "bodySize": 648}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 214, "receive": 4}}]}}