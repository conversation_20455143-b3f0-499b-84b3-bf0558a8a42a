# NOTE: Generated By HttpRunner v4.3.5
# FROM: .\testcases\ref_testcase.yml
from httprunner import Http<PERSON>un<PERSON>, Config, Step, RunRequest
from httprunner import RunTestCase
from loguru import logger

import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from demo.requests_test import TestCaseRequests as Requests


class TestCaseRefTestcase(HttpRunner):
    # 类级别前后置
    def setup_class(self):
        logger.warning("------类级别前置")

    def teardown_class(self):
        logger.warning("------类级别后置")

    # 用例级别前后置
    def setup(self):
        logger.warning("------用例级别前置")

    def teardown(self):
        logger.warning("------用例级别后置")

    config = (
        Config("request methods testcase: reference testcase")
        .variables(
            **{
                "foo1": "testsuite_config_bar1",
                "expect_foo1": "testsuite_config_bar1",
                "expect_foo2": "config_bar2",
            }
        )
        .base_url("https://postman-echo.com")
        .verify(False)
    )

    teststeps = [
        Step(
            RunTestCase("request with functions")
            .with_variables(
                **{"foo1": "testcase_ref_bar1", "expect_foo1": "testcase_ref_bar1"}
            )
            .call(Requests)
            .export(*["foo3"])
        ),
        Step(
            RunRequest("post form data")
            .with_variables(**{"foo1": "bar1"})
            .setup_hook('${setup_hooks_request($request)}')
            .post("/post")
            .with_headers(
                **{
                    "User-Agent": "${get_user_agent()}",
                    "Content-Type": "application/x-www-form-urlencoded",
                }
            )
            .with_data("foo1=$foo1&foo2=$foo3")
            .validate()
            .assert_equal("status_code", 200)
            .assert_equal("body.form.foo1", "bar1")
            .assert_equal("body.form.foo2", "bar21")
        ),
    ]


if __name__ == "__main__":
    TestCaseRefTestcase().test_start()
