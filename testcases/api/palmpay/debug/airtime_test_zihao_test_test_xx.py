# NOTE: Generated By HttpRunner v4.3.5
# FROM: testcases\palmpay\debug\airtime_test_zihao_test.json
from httprunner import HttpRunner, Config, Step, RunRequest


class TestCaseAirtimeTestZihaoTest(HttpRunner):

    config = Config("testcase description")

    teststeps = [
        Step(
            RunRequest("")
            .post("https://ng-apptest.transspay.net/api/cfront/home/<USER>")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/home/<USER>",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "11",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "hhkr8RfcZyMcGTqrhBADFzpcL%2BbUICNsszOd8dJq%2F3fy%2Bxv26ozcZ%2Beb7LTF6DSQy1jEh3GCvnxlOrGycfe4tIslw3kdYdUoIi9IyKpyzaBr2eSIdNCNuelLEI%2FNLRdY%2BYy9EgI2SKjFdIRvXjIBtoTZ5WawCQ5bqX5FIEq3T8M%3D",
                    "pp_req_sign_2": "V9sAo5i3J3NHc5DRr617mYAS3CA346K%2Bgkk4LfyOawzMC3yiVtblecWdSFmIc9WxOP7e8gvwLdyfdwDH17zE3YXCKlqdflnqq2H7DutSHkTxOtNp6qUKKdIsKO3M%2FktksC0vU6Zy8ZRjk7e38wVg5iwHQZ8VYa07PKsw3t8KbGI%3D",
                    "pp_timestamp": "1715081340689",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_data({"scene": 0})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .get(
                "https://ng-apptest.transspay.net/api/online-agent-product/agent/status"
            )
            .with_params(**{"categoryId": "10"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/online-agent-product/agent/status?categoryId=10",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "snBVpaCfrWbqHDdddcZbxQIoHP6P7mX93ycKSPexbcyMX9FT2L%2FWngJ1IIp2qWLwQDknNbFY0CfTcsZRWp6dioQuayvqbcadeOOqL7W6%2BZlg3G%2BRox8TAMtNSdk3vZTA6%2F6FWQqo1uAguflSp%2B0vWcogurYFx0z0IlhZNYWDZRo%3D",
                    "pp_timestamp": "1715081342893",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("")
            .post("https://ng-apptest.transspay.net/api/cfront/airtime/menu/v4")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/airtime/menu/v4",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "0",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "QAcCbkrsxTgQOgCAYuSE5KZVFXlUoLwCGz0M9HE%2BmimNJGSpGJvoEimJWC%2Baeyo53xXy8yzPlMArx00B%2FkWky%2BEy07O8hr2OBt7zFBjIvgwGsTm6iHPRMI8uBZplMWqRwmZtwgdhrQEGNxEv%2BnKorlwekxkjCtHr986DQurLFyo%3D",
                    "pp_timestamp": "1715081342988",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .get(
                "https://ng-apptest.transspay.net/api/cfront/airtime/recharge/records/latest"
            )
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/cfront/airtime/recharge/records/latest",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "hBh0T6wTDx2OQEnjCoWcfglQW5ieaP9SFZvqwjix3cw2qalX%2BcdemCv2Z14GyzxPVHVFMYt%2FjZ2s1YWjgOz%2FmofXJTwYhYZZvlQcKOyISo6h%2FI8MbkIJFOs11v9wuUTI2D5eW3IS1%2BjwhYQbyhFzAg8M6cMc%2FBmw6s3EyCPQngM%3D",
                    "pp_timestamp": "1715081342997",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .get("https://ng-apptest.transspay.net/api/airtime/techShareLink")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/techShareLink",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "hBh0T6wTDx2OQEnjCoWcfglQW5ieaP9SFZvqwjix3cw2qalX%2BcdemCv2Z14GyzxPVHVFMYt%2FjZ2s1YWjgOz%2FmofXJTwYhYZZvlQcKOyISo6h%2FI8MbkIJFOs11v9wuUTI2D5eW3IS1%2BjwhYQbyhFzAg8M6cMc%2FBmw6s3EyCPQngM%3D",
                    "pp_timestamp": "1715081342997",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.data", None, "assert response body data")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .post("https://ng-apptest.transspay.net/api/cfront/airtime/item/tips")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/airtime/item/tips",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "0",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "PMGt%2FcYzyasuhl92nbJj6B5zg8Tfozd7i1ztrrcA6hqjimvwPGVQNWvl%2FkaRzKerkeHNdB%2BQJRCBDlhXAf%2B5HaorDhq44dQjHHKm71VFwE2aDY1heGWFVkgEYLjd8McPxlAeg57B6oQFDV86PybuW4fp7kJaJXggNuO358ZlSSw%3D",
                    "pp_timestamp": "1715081343009",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .get("https://ng-apptest.transspay.net/api/airtime/preOrders/page")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/preOrders/page",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:27:21 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "hzw5Wb3kYUmKisIf0MKvKrdpQVW5Jk7eKm1f3c95fEx%2BcgviRW9RegJu8Di3n3HFTCX30W%2BUzwtrLTFPtVMUWrKAknJy4xm9wymojjU4kw4xCwNdrB5aFlDBtPwfMhbgy%2BFWXgA5zaZ9C44npnyi6B0HVUMJbkdpvbUbk%2Bxh5TQ%3D",
                    "pp_timestamp": "1715081343012",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .get(
                "https://ng-apptest.transspay.net/api/cfront/quickteller/resolve/network/query"
            )
            .with_params(**{"mobileNo": "09551234911"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/cfront/quickteller/resolve/network/query?mobileNo=09551234911",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:36 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "wC02FlTsbHusBVyhqEz5h%2BR5GacwugzY691716TduaLwc0xO7TJATRFV47kQFmguo47GmP2VDHk2qXgAvEkt3xX9HmVnibaWdO4PgqU%2BlHOxvK1DaLG8Ir8isFupjFTpe9bSUp4Ar%2FB4qH8ruGMJGKhfPRYJ%2BvK2rGQ8SlgxqVQ%3D",
                    "pp_timestamp": "1715081343076",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.data", "GLO", "assert response body data")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .get("https://ng-apptest.transspay.net/api/airtime/services")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/services",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "Bek5dF2xFCdtaV3yIpv3Vultt819glOYGX7W4i9up1z3r%2Bq0%2Fr9pJXhtRZ4ef%2FNrWmKPcgiC1e0RKQde4xkpCQH%2FYGIhRhwwgfurbKgOXmxoLyQxHh9dj562R%2Ftl4Zt8U1wkNrJzFwzzTsTH4jupZL1LmJ3%2Fq1X%2FNoTzeHKzwlU%3D",
                    "pp_timestamp": "1715081343084",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .post("https://test-ad.transspay.net/api/ad/pullAd")
            .with_headers(
                **{
                    ":authority": "test-ad.transspay.net",
                    ":method": "POST",
                    ":path": "/api/ad/pullAd",
                    ":scheme": "https",
                    "accept": "application/json",
                    "accept-encoding": "gzip",
                    "accept-language": "en",
                    "appchannel": "googleplay",
                    "authorization": "Bearer MC3253537245",
                    "content-length": "512",
                    "content-type": "application/json; charset=UTF-8",
                    "countrycode": "NG",
                    "imgresolution": "2400*1080",
                    "pp_client_ver": "5.7.0",
                    "signature": "f6Zup9dDUqcw4i0qzaUL6sHjeYrewe/hyJCX9rIX681OsrPingsgEn0QOW6f53hPpQJJeTa1m+L2t8mqATKgtnHu787MbVsyrFp0L1yj8CD3/f6c91HflY3rTSpJYrSaWEKmL9scmWcliqMALygFdTzsVhnnV8g5Ioj/JMRjUsM=",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .with_data(
                {
                    "bizInfo": '{"adSlotId":"01FFAE0D0B2447B68A767616A137F063","applicationId":"B795C65A0EA3476F985A4029A8566916","extendParam":{"deviceModel":"Infinix X671B","deviceVersion":"Android13","brand":"Infinix","deviceInfo":"bee648d4b0a5ab70"},"gaId":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","infoId":"MDIzNDA5NTUxMjM0OTEx","lat":9999.0,"lon":9999.0,"notShowAdIds":[],"userId":"A12D79F816C144B593703ECFFE53F319"}',
                    "nonceStr": "KeYSIwtC",
                    "requestTime": 1715081343114,
                    "version": "1.0",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .get(
                "https://ng-apptest.transspay.net/api/scene-activity-product/bill/pullNew/insidePullNewList"
            )
            .with_params(**{"showLocation": "AirtimeHomepage"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=AirtimeHomepage",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:27:21 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "MDdUpiZcKLrNKt7XhI4YLQDL%2FPCehGfgbPInxaQZh1myALu%2FW1RC1rJtdmXQ1F90cR7ZRsq2V2OtPCTtCY8JYfDwFg0JcMS6Vzh8b6NeeErqVpVHLY0PNkZi%2B97fjPomIl0rgvRmVpYtpkiMKwfW64E2oD4Fv66ZxQ4Iq81vAVs%3D",
                    "pp_timestamp": "1715081343117",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json",
                "assert response header Content-Type",
            )
            .assert_equal("body.data", None, "assert response body data")
            .assert_equal("body.respCode", "10000013", "assert response body respCode")
            .assert_equal(
                "body.respMsg", "Activity ended", "assert response body respMsg"
            )
            .assert_equal("body.status", False, "assert response body status")
        ),
        Step(
            RunRequest("")
            .post("https://ng-apptest.transspay.net/api/airtime/cashback/getBizConfig")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/airtime/cashback/getBizConfig",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "18",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "I%2BUEQfGWcnUQMCHUs5X1zzCXdsupvkLr8duRsJh%2Bi3aOPbLsATjqRPqGUcFk1g0C8VlT3MNXfP04d4RwXoq%2Bos7hCGhR17Jgx67z0mlD1ZMCWLtHVQqyRqzslJBSi4RZ7Iw%2Bxd4XYexrFWw68CFIQB%2BobZzTFD6h%2BpFvrBdQwrc%3D",
                    "pp_req_sign_2": "H8kwISn%2BUeHFLdPNucdGhq%2FBgJwgs88rAXKttm1GcTrHoqw8NXhJ0WhKp2EmF5JZ%2Bf4%2BJT9kOEjvrZDE6lzODTOYaLzZZ7E5pxvXQN08wqZ8x4wyRMYmB0mhU3wvijEqAI1dtkNHdCrpKIu6XcF6FAXpUbkGMyn3uSHS%2BaFmZIw%3D",
                    "pp_timestamp": "1715081343111",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_data({"transType": "04"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .post("https://test-ad.transspay.net/api/ad/pullAd")
            .with_headers(
                **{
                    ":authority": "test-ad.transspay.net",
                    ":method": "POST",
                    ":path": "/api/ad/pullAd",
                    ":scheme": "https",
                    "accept": "application/json",
                    "accept-encoding": "gzip",
                    "accept-language": "en",
                    "appchannel": "googleplay",
                    "authorization": "Bearer MC3253537245",
                    "content-length": "512",
                    "content-type": "application/json; charset=UTF-8",
                    "countrycode": "NG",
                    "imgresolution": "2400*1080",
                    "pp_client_ver": "5.7.0",
                    "signature": "I3L99jh+6yOdHzZUfSXZJIpZx0+mqBKdOusb1meZMLzcoFAHwUx2sZ5lfP6MDs2DsHJpnikb74g0iwm+KxBtFWIyqnCWb2xw+4r5Fl2Gb3kljG16n7+fVlcict6nDCdOVRio8q9E1vHGRJ55qh4RWtz685vkZc/jT+NEG+i2eLU=",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .with_data(
                {
                    "bizInfo": '{"adSlotId":"A4558A5E2CFD41C28EEBB831CE3E90B3","applicationId":"B795C65A0EA3476F985A4029A8566916","extendParam":{"deviceModel":"Infinix X671B","deviceVersion":"Android13","brand":"Infinix","deviceInfo":"bee648d4b0a5ab70"},"gaId":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","infoId":"MDIzNDA5NTUxMjM0OTEx","lat":9999.0,"lon":9999.0,"notShowAdIds":[],"userId":"A12D79F816C144B593703ECFFE53F319"}',
                    "nonceStr": "tBQPCWyh",
                    "requestTime": 1715081343114,
                    "version": "1.0",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .post(
                "https://ng-apptest.transspay.net/api/cfront/airtime/v2/query/auto-onoff-sale-info"
            )
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/airtime/v2/query/auto-onoff-sale-info",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "21",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "BTYL5WgQ8USARSyjmPgVyirxyM0DGfFGWpui5rGdqqUZ%2BSNA6T3ldDcjDP1sBKqdLq4FMOahiZJZcI8Q%2BB1nDx8iUzwT6KcaPSSOQFDT1x5iq34Rl2XqTp4qL5TbiP%2FM3cWheYorvU9eDmirx%2BvMj6%2Bzi8u5fiCdvECf14E9jDg%3D",
                    "pp_req_sign_2": "tANoTqmqPg5V0fdrTvgZ69XgSLWJQdWW%2BTdcCXO3wSeB7ImJtFhtbGSempI5t3Yrxpfq8T9TH4dnuo%2FfE6wWQTvXmHuHj4bEmdJ4dZ2uK3cKyy%2F39EidXB8U0VI9fRFQC5JfNiP7GouPwYBrqBtEbZjK1ISkoCZykBnLMj604og%3D",
                    "pp_timestamp": "1715081343174",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_data({"categoryId": ["10"]})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("")
            .post("https://test-ad.transspay.net/api/ad/pullAd")
            .with_headers(
                **{
                    ":authority": "test-ad.transspay.net",
                    ":method": "POST",
                    ":path": "/api/ad/pullAd",
                    ":scheme": "https",
                    "accept": "application/json",
                    "accept-encoding": "gzip",
                    "accept-language": "en",
                    "appchannel": "googleplay",
                    "authorization": "Bearer MC3253537245",
                    "content-length": "512",
                    "content-type": "application/json; charset=UTF-8",
                    "countrycode": "NG",
                    "imgresolution": "2400*1080",
                    "pp_client_ver": "5.7.0",
                    "signature": "T3Q15Ny181G5EGO5gw1a8cPOSq6KJO8xqXLZ9mcAwvNZ9RXOx87foezReNzrqvAs9Ams0pgWexWjBnvjIQkt021EIy3F++3R70jGjPbWZR4sj47JFmdShL3VlIRYM8FWwZqvtIY9gsiGsTYlq9dIWGzm0uhcbX4UAShKWqNwZfA=",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .with_data(
                {
                    "bizInfo": '{"adSlotId":"F6BC5D9086AB4D88B18FDCCAE76DDA8C","applicationId":"B795C65A0EA3476F985A4029A8566916","extendParam":{"deviceModel":"Infinix X671B","deviceVersion":"Android13","brand":"Infinix","deviceInfo":"bee648d4b0a5ab70"},"gaId":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","infoId":"MDIzNDA5NTUxMjM0OTEx","lat":9999.0,"lon":9999.0,"notShowAdIds":[],"userId":"A12D79F816C144B593703ECFFE53F319"}',
                    "nonceStr": "qi0vdGR6",
                    "requestTime": 1715081343174,
                    "version": "1.0",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .get("https://ng-apptest.transspay.net/api/cfront/airtime/buyProducts/v2")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/cfront/airtime/buyProducts/v2",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:36 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "u2uqOaGnM9q1D5%2B%2BuCzy3Mr4jYBj563HfI3geZ4d7HwhWAzn%2BEXdiB6jz%2BhPqVk6jckQGUoq%2B%2F0LVQ8eUjdVrxR8f3FZxo1TgOk4QDWJbwvqxOhzt7CtlP31O8xmN4CJo0Ps%2FbivqPZYYj6f9VCGO9tlQpp2ieyNMu%2B%2BmihqgdE%3D",
                    "pp_timestamp": "1715081343177",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .get("https://ng-apptest.transspay.net/api/airtime/payBills")
            .with_params(**{"location": "04"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/payBills?location=04",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "GlsfoYX9ZXTqWhlA25EdJkhbHavBj1isfctqrx8Dg%2FDmjOBIXzN7ikLHbT6lG%2BSbJeFI%2Bwblklbgt8Intmks4Kx5wy5QdE8v3LL05m8LZFwEAT1fKKMa1uO8r1QWIMBHccHS%2BVxVCk2xYm%2Byd6nXRXauWU%2ByT3R7clJkqID%2Fpzo%3D",
                    "pp_timestamp": "1715081343179",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .post("https://ng-apptest.transspay.net/api/cfront/coupon/statistics")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/coupon/statistics",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "45",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "R3VqElKmCa%2FjZyP7J0H00YA%2Bv%2FWWZOf2UVyCqqG5i%2FwMvJIMV%2FOEVfzdkK1OuNf%2FbKoZdF1zBNk6OVxYTMayAJEslQWGhwban4FmnWyCnAXp6OpG4dH1XIWKMWl%2By1uz33CTOLr10l7qRlI3pHDpUC5nGn44AWzTHiKW2cVGgd8%3D",
                    "pp_req_sign_2": "A7UD%2FB8lmJIl194doPKpSskslld6JCBt4TZP%2BaNBVAHDovzxTg34%2BcG6IE9SYDG32YVjbJicNUD29Gc8%2FbxQZb64doWvoJiU4qTafvDPzig4AiEv2URKvqzGsIewT5KlefTXyK0FZvPdrOaMYZhl9whea9USjtZDmvH5Kr0Ne8A%3D",
                    "pp_timestamp": "1715081343185",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_data({"pageNum": 1, "pageSize": 100, "transType": "04"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("")
            .post("https://test-ad.transspay.net/api/ad/pullAd")
            .with_headers(
                **{
                    ":authority": "test-ad.transspay.net",
                    ":method": "POST",
                    ":path": "/api/ad/pullAd",
                    ":scheme": "https",
                    "accept": "application/json",
                    "accept-encoding": "gzip",
                    "accept-language": "en",
                    "appchannel": "googleplay",
                    "authorization": "Bearer MC3253537245",
                    "content-length": "512",
                    "content-type": "application/json; charset=UTF-8",
                    "countrycode": "NG",
                    "imgresolution": "2400*1080",
                    "pp_client_ver": "5.7.0",
                    "signature": "A9uoQsHyNPnLNy8b6ZgRDl6PIq+uOEGVHxUfVyETg9zqCzBbGaY4vuApaN/eNuat65X1AjkRT+hShpUmwYjrqso0RVh7QXpFM/pthOih5v/vwMQvG8PGVK4i8Kcf5xX5zg7O4ypXbixhawTJCzbPsc+n4qK283T40BBDw2a2F8s=",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .with_data(
                {
                    "bizInfo": '{"adSlotId":"98E7CAF944774A5AB456DD0CFABAD73C","applicationId":"B795C65A0EA3476F985A4029A8566916","extendParam":{"deviceModel":"Infinix X671B","deviceVersion":"Android13","brand":"Infinix","deviceInfo":"bee648d4b0a5ab70"},"gaId":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","infoId":"MDIzNDA5NTUxMjM0OTEx","lat":9999.0,"lon":9999.0,"notShowAdIds":[],"userId":"A12D79F816C144B593703ECFFE53F319"}',
                    "nonceStr": "Zgmj2Zo5",
                    "requestTime": 1715081343248,
                    "version": "1.0",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .get("https://ng-apptest.transspay.net/api/airtime/recommendedItems")
            .with_params(**{"billerName": "GLO"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/recommendedItems?billerName=GLO",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:37 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "J%2BjA3sIoRld0qj6dzOxyKR9HomyNdcY%2B3WbodDA2B5vz5vBEZcz%2Fnncgxx4ojlqpMw0cRlMGyDzt%2FhLYRpXP%2FWc6807T%2FSh169ldnWeGOWeaBjUgfiejN5HhM9EcxK72HAXPKAXX9wkzi%2Fazj%2FzJwm%2F12vlIhBjPmehBB%2FQsevg%3D",
                    "pp_timestamp": "1715081343254",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .post("https://test-ad.transspay.net/api/dataBurialPoint/adBehavior")
            .with_headers(
                **{
                    ":authority": "test-ad.transspay.net",
                    ":method": "POST",
                    ":path": "/api/dataBurialPoint/adBehavior",
                    ":scheme": "https",
                    "accept": "application/json",
                    "accept-encoding": "gzip",
                    "authorization": "Bearer MC3253537245",
                    "content-length": "4882",
                    "content-type": "application/json",
                    "countrycode": "NG",
                    "signature": "W3bSvQ52EK06vYRsO3TGTXSdkiqPuiGZWKsXIddZN2GzdUOx+9DbzWOqHfxTmFxd639RJwHi3aSK1UKy8McYS9iVs5y+Yhksmy1iA1F9Qx8bQNowIarbBnsRAp8d++Fa8HQXPGZFysT9bqzm/I1HMINPjZ9ApH2yWavLhpPfa9s=",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .with_data(
                {
                    "bizInfo": '[{"body":{"adId":"3DAFC601B3A3439A9DBEB9C721F9288D","adSlotId":"01FFAE0D0B2447B68A767616A137F063","adTime":1715081343418,"countryCode":"NG","eventSubType":"","eventType":"autoBegin","num":1,"operator":"","pointWindowX":"","pointWindowY":"","relatedId":"","userId":"","wifi":true},"head":{"SDKVersion":"1.0.36-SNAPSHOT","appIdentifier":"com.transsnet.palmpay","appVersion":"5.7.0","appVersionCode":604181603,"applicationId":"B795C65A0EA3476F985A4029A8566916","behaviorId":"B795C65A0EA3476F985A4029A85669161715081343418","createTime":1715081343423,"eventTime":1715081343423,"gaid":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","imei":"","ip":"*************","macAddress":"please open wifi","operSystem":"Android"}},{"body":{"adId":"474DDD975A5C4F61B8A8B13A16DB45F4","adSlotId":"0CDF659D244E4A65B86D0BD9EB2D6879","adTime":1715081335148,"countryCode":"NG","eventSubType":"","eventType":"autoBegin","num":1,"operator":"","pointWindowX":"","pointWindowY":"","relatedId":"","userId":"","wifi":true},"head":{"SDKVersion":"1.0.36-SNAPSHOT","appIdentifier":"com.transsnet.palmpay","appVersion":"5.7.0","appVersionCode":604181603,"applicationId":"B795C65A0EA3476F985A4029A8566916","behaviorId":"B795C65A0EA3476F985A4029A85669161715081335148","createTime":1715081335152,"eventTime":1715081335152,"gaid":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","imei":"","ip":"*************","macAddress":"please open wifi","operSystem":"Android"}},{"body":{"adId":"10DAD3F8FA4948418C6F88527FF8AE86","adSlotId":"74E199B793574A2BA35FAA1C82210C84","adTime":1715081331509,"countryCode":"NG","eventSubType":"","eventType":"autoBegin","num":1,"operator":"","pointWindowX":"","pointWindowY":"","relatedId":"","userId":"","wifi":true},"head":{"SDKVersion":"1.0.36-SNAPSHOT","appIdentifier":"com.transsnet.palmpay","appVersion":"5.7.0","appVersionCode":604181603,"applicationId":"B795C65A0EA3476F985A4029A8566916","behaviorId":"B795C65A0EA3476F985A4029A85669161715081331508","createTime":1715081331514,"eventTime":1715081331514,"gaid":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","imei":"","ip":"*************","macAddress":"please open wifi","operSystem":"Android"}},{"body":{"adId":"474DDD975A5C4F61B8A8B13A16DB45F4","adSlotId":"0CDF659D244E4A65B86D0BD9EB2D6879","adTime":1715081330943,"countryCode":"NG","eventSubType":"","eventType":"autoBegin","num":1,"operator":"","pointWindowX":"","pointWindowY":"","relatedId":"","userId":"","wifi":true},"head":{"SDKVersion":"1.0.36-SNAPSHOT","appIdentifier":"com.transsnet.palmpay","appVersion":"5.7.0","appVersionCode":604181603,"applicationId":"B795C65A0EA3476F985A4029A8566916","behaviorId":"B795C65A0EA3476F985A4029A85669161715081330943","createTime":1715081330947,"eventTime":1715081330947,"gaid":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","imei":"","ip":"*************","macAddress":"please open wifi","operSystem":"Android"}},{"body":{"adId":"6C6A0A5AF92F4BE0B8A1F0DE158F9664","adSlotId":"5F7CAA79D3F247AD986B94D3807E6B97","adTime":1715081330932,"countryCode":"NG","eventSubType":"","eventType":"autoBegin","num":1,"operator":"","pointWindowX":"","pointWindowY":"","relatedId":"","userId":"","wifi":true},"head":{"SDKVersion":"1.0.36-SNAPSHOT","appIdentifier":"com.transsnet.palmpay","appVersion":"5.7.0","appVersionCode":604181603,"applicationId":"B795C65A0EA3476F985A4029A8566916","behaviorId":"B795C65A0EA3476F985A4029A85669161715081330932","createTime":1715081330937,"eventTime":1715081330937,"gaid":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","imei":"","ip":"*************","macAddress":"please open wifi","operSystem":"Android"}},{"body":{"adId":"3665C02D8F9549E788C0AF8AD20B99FB","adSlotId":"E1F2570CEEFD47C1A3B8ACDC0DDB9C79","adTime":1715081293017,"countryCode":"NG","eventSubType":"","eventType":"autoBegin","num":1,"operator":"","pointWindowX":"0","pointWindowY":"0","userId":"","wifi":true},"head":{"SDKVersion":"1.0.36-SNAPSHOT","appIdentifier":"com.transsnet.palmpay","appVersion":"5.7.0","appVersionCode":604181603,"applicationId":"B795C65A0EA3476F985A4029A8566916","behaviorId":"B795C65A0EA3476F985A4029A85669161715081293017","createTime":1715081330398,"eventTime":1715081330398,"gaid":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","imei":"","ip":"*************","macAddress":"please open wifi","operSystem":"Android"}}]',
                    "nonceStr": "Ee17LCUY",
                    "requestTime": 1715081343428,
                    "version": "1.0.36-SNAPSHOT",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.data", None, "assert response body data")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .post("https://test-ad.transspay.net/api/dataBurialPoint/adBehavior")
            .with_headers(
                **{
                    ":authority": "test-ad.transspay.net",
                    ":method": "POST",
                    ":path": "/api/dataBurialPoint/adBehavior",
                    ":scheme": "https",
                    "accept": "application/json",
                    "accept-encoding": "gzip",
                    "authorization": "Bearer MC3253537245",
                    "content-length": "3387",
                    "content-type": "application/json",
                    "countrycode": "NG",
                    "signature": "M/Oi0iDKNkmFeNJLcV9yfIi58M386FVtoT75Fdr68AGhoIu24o3ARjGEKBi0sLP463sGcKaEtIrhpZimnkM7jR7zmXQwoWtPCz0PUdqeJRwnwX+ydnEY6IIc2XcTGd+cjVXGDtssoYxqMsxPXg/Jom4jPtyKjaLgcnTeCqTml8g=",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .with_data(
                {
                    "bizInfo": '[{"body":{"adId":"3D26FD2CB1324E31A2FD4A20B7332AF1","adSlotId":"98E7CAF944774A5AB456DD0CFABAD73C","adTime":1715081343553,"countryCode":"NG","eventSubType":"","eventType":"autoBegin","num":1,"operator":"","pointWindowX":"","pointWindowY":"","relatedId":"B795C65A0EA3476F985A4029A85669161715081343537","userId":"","wifi":true},"head":{"SDKVersion":"1.0.36-SNAPSHOT","appIdentifier":"com.transsnet.palmpay","appVersion":"5.7.0","appVersionCode":604181603,"applicationId":"B795C65A0EA3476F985A4029A8566916","behaviorId":"B795C65A0EA3476F985A4029A85669161715081343553","createTime":1715081343558,"eventTime":1715081343558,"gaid":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","imei":"","ip":"*************","macAddress":"please open wifi","operSystem":"Android"}},{"body":{"adId":"EEB0E85648464F5AA55723D0A438BABE","adSlotId":"F6BC5D9086AB4D88B18FDCCAE76DDA8C","adTime":1715081343537,"countryCode":"NG","eventSubType":"","eventType":"autoBegin","num":1,"operator":"","pointWindowX":"","pointWindowY":"","relatedId":"","userId":"","wifi":true},"head":{"SDKVersion":"1.0.36-SNAPSHOT","appIdentifier":"com.transsnet.palmpay","appVersion":"5.7.0","appVersionCode":604181603,"applicationId":"B795C65A0EA3476F985A4029A8566916","behaviorId":"B795C65A0EA3476F985A4029A85669161715081343537","createTime":1715081343543,"eventTime":1715081343543,"gaid":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","imei":"","ip":"*************","macAddress":"please open wifi","operSystem":"Android"}},{"body":{"adId":"3D26FD2CB1324E31A2FD4A20B7332AF1","adSlotId":"98E7CAF944774A5AB456DD0CFABAD73C","adTime":1715081343537,"countryCode":"NG","eventSubType":"","eventType":"autoBegin","num":1,"operator":"","pointWindowX":"","pointWindowY":"","relatedId":"","userId":"","wifi":true},"head":{"SDKVersion":"1.0.36-SNAPSHOT","appIdentifier":"com.transsnet.palmpay","appVersion":"5.7.0","appVersionCode":604181603,"applicationId":"B795C65A0EA3476F985A4029A8566916","behaviorId":"B795C65A0EA3476F985A4029A85669161715081343537","createTime":1715081343541,"eventTime":1715081343541,"gaid":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","imei":"","ip":"*************","macAddress":"please open wifi","operSystem":"Android"}},{"body":{"adId":"3DAFC601B3A3439A9DBEB9C721F9288D","adSlotId":"01FFAE0D0B2447B68A767616A137F063","adTime":1715081343437,"countryCode":"NG","eventSubType":"","eventType":"autoBegin","num":1,"operator":"","pointWindowX":"","pointWindowY":"","relatedId":"B795C65A0EA3476F985A4029A85669161715081343418","userId":"","wifi":true},"head":{"SDKVersion":"1.0.36-SNAPSHOT","appIdentifier":"com.transsnet.palmpay","appVersion":"5.7.0","appVersionCode":604181603,"applicationId":"B795C65A0EA3476F985A4029A8566916","behaviorId":"B795C65A0EA3476F985A4029A85669161715081343437","createTime":1715081343446,"eventTime":1715081343446,"gaid":"715131e3-1810-4c6d-8dc3-c6776a8b63cc","imei":"","ip":"*************","macAddress":"please open wifi","operSystem":"Android"}}]',
                    "nonceStr": "nedEEh7A",
                    "requestTime": 1715081343689,
                    "version": "1.0.36-SNAPSHOT",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json;charset=UTF-8",
                "assert response header Content-Type",
            )
            .assert_equal("body.data", None, "assert response body data")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("")
            .get(
                "https://ng-apptest.transspay.net/api/scene-activity-product/bill/pullNew/insidePullNewList"
            )
            .with_params(**{"showLocation": "AirtimeHomepage"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=AirtimeHomepage",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:29:04 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "OCph0JLi91Uad1L7fvKx2Pwery6pnaZJ9DGayob%2Be%2F1Ll%2BmZlOYM8B9N3pWsPgtk0t6zDpEVnGyzOjsYj%2FPdCt9eis3rEb7tiJM9Ql2QRvTyG62UfsUVhMD0B4HdAhws4iajqMuab6dUTlW2LYl8Jey%2Fk1MXD3C%2BdOkMEKoEi4E%3D",
                    "pp_timestamp": "1715081344583",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json",
                "assert response header Content-Type",
            )
            .assert_equal("body.data", None, "assert response body data")
            .assert_equal("body.respCode", "10000013", "assert response body respCode")
            .assert_equal(
                "body.respMsg", "Activity ended", "assert response body respMsg"
            )
            .assert_equal("body.status", False, "assert response body status")
        ),
    ]


if __name__ == "__main__":
    TestCaseAirtimeTestZihaoTest().test_start()
