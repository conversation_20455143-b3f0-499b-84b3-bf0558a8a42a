{"log": {"version": "1.2", "creator": {"name": "<PERSON>", "version": "4.6.4"}, "entries": [{"startedDateTime": "2024-05-20T17:58:40.106+08:00", "time": 241, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/online-agent-product/agent/status?categoryId=1", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/online-agent-product/agent/status?categoryId=1"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&*********"}, {"name": "pp_timestamp", "value": "1716199119397"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "XeenMey4KyFq2heWxIQuB0wf4UEDt1wZFIjkTjgBQjHS%2F7CI4NnnmZna%2FTk0wHCEHiDqFCyakykCPw4OTGkM3qSoSomAC54DzmeI8b%2FP0m5riLXZltNmfye4dKmhLezoTs2Td3m9HltITHDYgIw8m5uD3qdC3KOnotiNkWNoJFM%3D"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [{"name": "categoryId", "value": "1"}], "headersSize": 210, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:58:40 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "559"}, {"name": "eagleeye-traceid", "value": "eaac16282817161991202932183d0001"}, {"name": "x-envoy-upstream-service-time", "value": "18"}], "content": {"size": 559, "mimeType": "application/json", "text": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "encoding": "base64"}, "redirectURL": null, "headersSize": 105, "bodySize": 559}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 239, "receive": 1}}, {"startedDateTime": "2024-05-20T17:58:40.226+08:00", "time": 230, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/pay/queryLimitAmtForOrder", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/pay/queryLimitAmtForOrder"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&*********"}, {"name": "pp_timestamp", "value": "1716199119400"}, {"name": "user-agent", "value": "PalmPay/5.8.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "hEX5sJSmwbjCfYY2GcncwpR3m3WSsY5JdKBCsW7xeamcneFsZu0q1sCseUFQtgaKm123cFOSvZS420TAm41uy4BXIjYmW%2FIuOfV1xpKFClIT0GDsmvIfG6fh4rG4m7%2BpvkVzVbAtu%2FlQjn9MwU6aGkvj3ISve3xdS5jlguxydZ8%3D"}, {"name": "pp_req_sign_2", "value": "n2RspmtD9HeUU5FB5F9b9z1UpvgZ3A%2BwRkk6yQd15yb1blqeQIec9QiwO9T%2BSBPGVYYaZhOsGKvv1W2RxdLAtfz5E69jPZ4iQeDP7aEEjoNwBA%2F5gkwAH9GRo68GH4MKZrd2BNvg%2FokgwXy5uI2N3%2BZM4r%2F%2B75pekrAKfr8gnnM%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "18"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"transType\":\"16\"}"}, "headersSize": 356, "bodySize": 18}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:58:40 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "116"}, {"name": "eagleeye-traceid", "value": "eaac162bc217161991204172525d0001"}, {"name": "x-envoy-upstream-service-time", "value": "29"}], "content": {"size": 116, "mimeType": "application/json", "text": "************************************************************************************************************************************************************", "encoding": "base64"}, "redirectURL": null, "headersSize": 105, "bodySize": 116}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 230, "receive": 0}}, {"startedDateTime": "2024-05-20T17:58:40.226+08:00", "time": 29973, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "kRQ6UPV2mE5gEh/7QYKCijNnPMVXAZIFrXT21GWycVWpXjkNAW0R/y1S35XXOf2f3VoqTyUy7BQen1ttg2d3k/BStzhC2BcsSOd6q4YJQ+LftiAqPWYsuyizc+5xXVizj0+w+NEu1nro+4/pf+zWGRh6j+KjX0KQTDClxr1eVsY="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.8.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"27DE84107F634ABF8B789620D4BD44F5\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"jLSXogCw\",\"requestTime\":1716199119485,\"version\":\"1.0\"}"}, "headersSize": 160, "bodySize": 512}, "response": {"_charlesStatus": "EXCEPTION", "status": 0, "statusText": null, "httpVersion": "", "cookies": [], "headers": [], "content": {"size": 0, "mimeType": null}, "redirectURL": null, "headersSize": 0, "bodySize": 0, "_error": "IO: Stream cancelled by CLIENT"}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": -1, "receive": -1}}, {"startedDateTime": "2024-05-20T17:58:40.226+08:00", "time": 225, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/airtime/v2/query/auto-onoff-sale-info", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/airtime/v2/query/auto-onoff-sale-info"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&*********"}, {"name": "pp_timestamp", "value": "1716199119486"}, {"name": "user-agent", "value": "PalmPay/5.8.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "QJ%2BlQICj5Oe5WhlVS7nez8FZo26ajbBXaknKpRsZh96FOjRPUaSGWNWZe4NKid6Nf2DyK2oF%2ByYmz8JPoIeWdvhQOMFgBYzOY931gCoO8HwaV3eahcJwojT4FLkLp6C5871Y37Nsj8RpZg4%2B1BJ3A885VK7MCK%2Fwkw0vtEwBqew%3D"}, {"name": "pp_req_sign_2", "value": "W%2FlJ8gDZebCWafjHL5o%2B1HTqQ7it%2BJ0l1kPMmwwYRESRPMxQY%2FAy83neHbLSqtlxoqSy7a5qJfJUWj%2FsfIUvesijIYP7IX1tmKd6DmVb%2FJCp%2FTQc7CIeldWuwylGIiM%2BHN5PuCoQE3MTfRhiwZwj%2FcZc7YwNRJVv5Z9XVhXM3gM%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "20"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"categoryId\":[\"1\"]}"}, "headersSize": 377, "bodySize": 20}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:58:40 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "67"}, {"name": "eagleeye-traceid", "value": "eaac162bc217161991204202526d0001"}, {"name": "x-envoy-upstream-service-time", "value": "20"}], "content": {"size": 67, "mimeType": "application/json", "text": "********************************************************************************************", "encoding": "base64"}, "redirectURL": null, "headersSize": 104, "bodySize": 67}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 225, "receive": 0}}, {"startedDateTime": "2024-05-20T17:58:40.226+08:00", "time": 29996, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "MMVSMIWhhigk61la5oaYnPjTlyznB7uKkWVxcEDQfEYeMFueBa4WABoNpCXcxxIeKt1B1gGgNAN2v40cgCXDhEhAbZudVyFaIWUc1a5beZmFg/nmTRl7PLKItgya8+p4OvdeufeeuNF6RH5AV5feaugb/qbB7fymrxnmKbTsy/U="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.8.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"EC5415611A0748659D85A0931BBFF0DD\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"1GzOn1Rh\",\"requestTime\":1716199119487,\"version\":\"1.0\"}"}, "headersSize": 314, "bodySize": 512}, "response": {"_charlesStatus": "EXCEPTION", "status": 0, "statusText": null, "httpVersion": "", "cookies": [], "headers": [], "content": {"size": 0, "mimeType": null}, "redirectURL": null, "headersSize": 0, "bodySize": 0, "_error": "Remote server closed the connection before sending response header"}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": -1, "receive": -1}}, {"startedDateTime": "2024-05-20T17:58:40.226+08:00", "time": 3158, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/live/biller?categoryId=1", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/live/biller?categoryId=1"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&*********"}, {"name": "pp_timestamp", "value": "1716199119405"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "t3hxAvaA8saAmg6nuCpBPDBg%2FcjoA%2BOhibMM6J61MPxjjUTarNk1LmTkNjU%2BoNmOhWDNIyJuAWu9m98USrDpwIIcTr23QRnPNxW86p7X0as3I%2Fm4YfCbP6yjuMFgXpdYa7Hm2wLAt1v3pCdIoJiEoIazcG65uBXIqfIhjJgunSY%3D"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [{"name": "categoryId", "value": "1"}], "headersSize": 332, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:58:43 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac16282817161991204172184d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "2948"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 27066, "compression": 24741, "mimeType": "application/json;charset=UTF-8", "text": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "encoding": "base64"}, "redirectURL": null, "headersSize": 172, "bodySize": 2325}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 3148, "receive": 10}}, {"startedDateTime": "2024-05-20T17:58:40.226+08:00", "time": 29996, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "E+g9X7Gv4F9l6pXy46cmASwLzLBHH7MapnDoTUFmlrt0FySydg2WCr/hhJvg5yoIXqTtM2DpkjYbAXHPesYeR8QrrSNgAnUeE/b5/pVCMSWmZKJkemKA8uTUhDS6WU8ldVWd4y1NuaDKKStilPmJBNXtUt6T2aBYBH/Nz7IIaQk="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.8.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"4867591293064EEC8CED10C309A999BF\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"y8Rpm3Zs\",\"requestTime\":1716199119485,\"version\":\"1.0\"}"}, "headersSize": 157, "bodySize": 512}, "response": {"_charlesStatus": "EXCEPTION", "status": 0, "statusText": null, "httpVersion": "", "cookies": [], "headers": [], "content": {"size": 0, "mimeType": null}, "redirectURL": null, "headersSize": 0, "bodySize": 0, "_error": "Remote server closed the connection before sending response header"}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": -1, "receive": -1}}, {"startedDateTime": "2024-05-20T17:58:40.236+08:00", "time": 209, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/c-bff-product/page/template/query", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/c-bff-product/page/template/query"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&*********"}, {"name": "pp_timestamp", "value": "1716199119501"}, {"name": "user-agent", "value": "PalmPay/5.8.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "EPJI1LxRjZ9yA%2BNpP0Tdx1kr%2BmUKRwusk8eb5kfOMkY9tsmN5ltKaPziC4W1wLWll%2BU7n5i7ZAweh0RzglgKawANH%2BLWQqS1wQmr4EYGoLf73wBowYDL8Qe7G3LmbpmcnqA6XTTZJX3cHO%2FzPnQ8kv%2B%2B8%2BOlZUDcMRJ5zVAti0U%3D"}, {"name": "pp_req_sign_2", "value": "aQKb3hG7Mw62tTTiobaUNcGkVZqPFFt4YGRvo72nD%2BIeDb4EAFkWQ2uXYv0hrF0nqmuCyj5WIzScYHOb1F5IycZiTzmWPxyOFHrXt%2FK1xpUJubu1oWtY8J2mYxH7poTmCtOZbOXGacQf20fRbP2ERCntLsO3AmghKLH%2FwsTN9fg%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "2"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{}"}, "headersSize": 429, "bodySize": 2}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:58:40 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "content-length", "value": "425"}, {"name": "eagleeye-traceid", "value": "eaac16282817161991204242185d0001"}, {"name": "x-application-context", "value": "c-bff-product:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "8"}], "content": {"size": 425, "mimeType": "application/json;charset=UTF-8", "text": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "encoding": "base64"}, "redirectURL": null, "headersSize": 149, "bodySize": 425}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 209, "receive": 0}}, {"startedDateTime": "2024-05-20T17:58:40.236+08:00", "time": 244, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=16", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=16"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&*********"}, {"name": "pp_timestamp", "value": "1716199119514"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "Lx9r5wdco%2FCLZZa8JimS9N1cminsG0tE1F8lLyYhR6kSNoyyx8ZvVcG4GDRpTWp4IhcjLQCnuY5r0%2FZt88EN40%2BgBlCaEa%2BGggM0h%2FPKSI57BK8QoBb%2BOzSc%2BJ%2BWUvYtwDGvHh2xDCss%2F4c0k8VKyn2oKP0wK7wcI0%2BtxPX0B8c%3D"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [{"name": "showLocation", "value": "16"}], "headersSize": 235, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:58:40 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "67"}, {"name": "eagleeye-traceid", "value": "eaac162bc217161991204252527d0001"}, {"name": "x-envoy-upstream-service-time", "value": "36"}], "content": {"size": 67, "mimeType": "application/json", "text": "********************************************************************************************", "encoding": "base64"}, "redirectURL": null, "headersSize": 104, "bodySize": 67}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 244, "receive": 0}}, {"startedDateTime": "2024-05-20T17:58:40.236+08:00", "time": 281, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/airtime/cashback/getBizConfig", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/airtime/cashback/getBizConfig"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&*********"}, {"name": "pp_timestamp", "value": "1716199119513"}, {"name": "user-agent", "value": "PalmPay/5.8.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "O%2B6g7FdBQqZQYOn7q1rlt6ezob6cg85twlM2nb4CBeMXI5BvkfJAsfOn6sEa8GoH02EsCytgnDQnTZTYyL%2BEuVPFBr30Hh6dsf4vyCXxxAWqIga5RDz9Uac4xR8edT6relKQ6%2BVJZMM3sptshzeY5z1QE0VWCR8xZN437iF6wXc%3D"}, {"name": "pp_req_sign_2", "value": "n2RspmtD9HeUU5FB5F9b9z1UpvgZ3A%2BwRkk6yQd15yb1blqeQIec9QiwO9T%2BSBPGVYYaZhOsGKvv1W2RxdLAtfz5E69jPZ4iQeDP7aEEjoNwBA%2F5gkwAH9GRo68GH4MKZrd2BNvg%2FokgwXy5uI2N3%2BZM4r%2F%2B75pekrAKfr8gnnM%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "18"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"transType\":\"16\"}"}, "headersSize": 198, "bodySize": 18}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:58:40 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "content-length", "value": "485"}, {"name": "eagleeye-traceid", "value": "eaac16282817161991204252186d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "81"}], "content": {"size": 485, "mimeType": "application/json;charset=UTF-8", "text": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "encoding": "base64"}, "redirectURL": null, "headersSize": 146, "bodySize": 485}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 281, "receive": 0}}, {"startedDateTime": "2024-05-20T17:58:40.239+08:00", "time": 29983, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "ID9AqPqT2miDINxeMVCwEcjxhTG2y8fZFtznoCEMcTybELrPP/3JmjzHeXclmVvA7yTSP5wPkTitJYEPovW5jk30ohwEBc3lpg5UrWtDC+TfX8ZGyzMl5T1js1tXUg3C9IYDUdWs5k6G8h3gZAmOjdg1vkx9n/mD/VGC/9VvCB4="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.8.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"DA7525AFA09D4916A5959A085FB7727B\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"5FKMzi0W\",\"requestTime\":1716199119485,\"version\":\"1.0\"}"}, "headersSize": 159, "bodySize": 512}, "response": {"_charlesStatus": "EXCEPTION", "status": 0, "statusText": null, "httpVersion": "", "cookies": [], "headers": [], "content": {"size": 0, "mimeType": null}, "redirectURL": null, "headersSize": 0, "bodySize": 0, "_error": "Remote server closed the connection before sending response header"}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": -1, "receive": -1}}, {"startedDateTime": "2024-05-20T17:58:40.246+08:00", "time": 225, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/coupon/statistics", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/coupon/statistics"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&*********"}, {"name": "pp_timestamp", "value": "1716199119529"}, {"name": "user-agent", "value": "PalmPay/5.8.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "XPy940OBv17oFZk3PgBdWZzTs%2F%2BjBHfNQLVIYuZZIJqtgWBV75ZAIXPupBnXLdLUBL895%2FaDMJXo5dJsWQfsRXnuD%2BRh0mm30LVjZu4fKfphc60Ph9jM4SEtl6EjZYJiHRZH9%2F%2FeDEaKyT4FyPmmG1do8elMEZuGj6%2Ftf0xwmnI%3D"}, {"name": "pp_req_sign_2", "value": "Em%2BIMG%2BANTsXh2lhdsUa6%2F7rLeOIBxTOfOklKNToEMnGT4pSiPMg%2BBoiUefNiuh1x0aIbAOr5LDBvkx1RxbEu%2Ff36O6PZVheqRhwVCGWQzNSGpeXqVFOqZrR732Te9kNf7eW4mBvTlFMQ3JNOzhZFso%2B8dzu8li%2Bl9HjyRpmj%2B0%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "45"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"pageNum\":1,\"pageSize\":100,\"transType\":\"16\"}"}, "headersSize": 361, "bodySize": 45}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:58:40 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "157"}, {"name": "eagleeye-traceid", "value": "eaac162bc217161991204342528d0001"}, {"name": "x-envoy-upstream-service-time", "value": "26"}], "content": {"size": 157, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7InRvdGFsIjowLCJ0b3RhbFBhZ2UiOjAsImN1clBhZ2UiOjEsInBhZ2VTaXplIjoxMDAsImRhdGFNYXAiOm51bGwsImV4dERhdGEiOm51bGwsImxpc3QiOltdfSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 105, "bodySize": 157}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 225, "receive": 0}}, {"startedDateTime": "2024-05-20T17:58:43.003+08:00", "time": 288, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/payBills?location=16", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/payBills?location=16"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&*********"}, {"name": "pp_timestamp", "value": "1716199122294"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "inQju50gldr%2FYtE33mnDNit5uA0trOIfiffEeG6DMLzCJ7Z0IVxmmtIj8gjk9lw%2FhCK6LqpVCdGQPGQQXKICYbDro9dZ%2FdllNo%2BgKA7L3ELlOF6hMsjl%2BNAmn8qrsn6rUfYV0nWNvXUHSJB%2BRAtK23QckDrhg8dx%2BUw38CL%2BrcE%3D"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [{"name": "location", "value": "16"}], "headersSize": 204, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:58:43 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac16282817161991231912187d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "85"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 2467, "compression": 1852, "mimeType": "application/json;charset=UTF-8", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 171, "bodySize": 615}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 284, "receive": 4}}, {"startedDateTime": "2024-05-20T17:58:43.559+08:00", "time": 511, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/quickteller/getLastBill", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/quickteller/getLastBill"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&*********"}, {"name": "pp_timestamp", "value": "1716199122848"}, {"name": "user-agent", "value": "PalmPay/5.8.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "pkurH4MRoxLb%2F4nE99D6r1VzwY5dza01xUHY2GU8UGbuEjAGNhUec%2FsN0TJRnxhHjl8UbLgIi5b5W%2Brs7ikbpOPm%2BN0%2BKrV%2FHkv0FCC%2BjzUrnzR%2FqMgJ2uq%2FkvxcTDs9yN15L3M5iplwUoOmHmvt%2FkGqfnB95GsLUUeeINtdZIg%3D"}, {"name": "pp_req_sign_2", "value": "uhuZz221pJXbjrRtRtowWk8CuvzNYqaHEQcp5eR4GsAhM4zgtfNMrMDte7lKsnPzuytePYX5tW0E62mVu7RQ%2BkEyxR%2BaDMR7fr9Bop9QmF5e6GmvVzxiEvLPMuPk8l%2FPeo1X5vDi2ZymaKYB90ojF4omJL26GwTO9pSCRw9ktVk%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "18"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"categoryId\":\"1\"}"}, "headersSize": 358, "bodySize": 18}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:58:44 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162bc217161991237462529d0001"}, {"name": "x-envoy-upstream-service-time", "value": "305"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 2911, "compression": 1636, "mimeType": "application/json", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 131, "bodySize": 1275}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 510, "receive": 1}}, {"startedDateTime": "2024-05-20T17:58:44.111+08:00", "time": 567, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/live/biller/item?categoryId=1&billerId=1753", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/live/biller/item?categoryId=1&billerId=1753"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&*********"}, {"name": "pp_timestamp", "value": "1716199123401"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "RMBUhS%2BKMZB7YXclHp9kRVh7r4Msmwq2Ou2L4zM7g%2BJsWa3PAM0XsLJVZtNMMmiWBJkPl%2FwgSQk8HHR5LaOXlZ6uJrJsuMiS%2B03qPq1w1J1xkBLd8Zex9AWY10TXhYZludBXuBTFFNdiN6sd25hBh5ZsUDYCC0FY1xEXyIbXFnU%3D"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [{"name": "categoryId", "value": "1"}, {"name": "billerId", "value": "1753"}], "headersSize": 349, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:58:44 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac16282817161991242992189d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "364"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 4670, "compression": 4050, "mimeType": "application/json;charset=UTF-8", "text": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "encoding": "base64"}, "redirectURL": null, "headersSize": 172, "bodySize": 620}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 563, "receive": 4}}, {"startedDateTime": "2024-05-20T17:58:44.164+08:00", "time": 1240, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/live/biller/item?categoryId=1&billerId=1753", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/live/biller/item?categoryId=1&billerId=1753"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&*********"}, {"name": "pp_timestamp", "value": "1716199123403"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.8.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "EibnzvXSPPl6jrdT22c42Mo3DmmKMpphifh7f02jH8%2Bh%2FSp8tJl4FFvifvhnDv2OEXZQIm3tZbmiXlgmXiO5hg0ReiSV%2Fd8MIIwofiOOetFbby3UvSLuVF9vX5khrCo%2BQVOtLiZPGaau76VFa0AhGg%2BZmLn4SreGeOyFz%2Bvd0HM%3D"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [{"name": "categoryId", "value": "1"}, {"name": "billerId", "value": "1753"}], "headersSize": 179, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:58:45 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162bc217161991243512530d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "1038"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 4670, "compression": 4050, "mimeType": "application/json;charset=UTF-8", "text": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "encoding": "base64"}, "redirectURL": null, "headersSize": 172, "bodySize": 620}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 1236, "receive": 4}}, {"startedDateTime": "2024-05-20T17:58:46.524+08:00", "time": 234, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/airtime/live/electricity/queryAccount", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/airtime/live/electricity/queryAccount"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.8.0&*********"}, {"name": "pp_timestamp", "value": "*************"}, {"name": "user-agent", "value": "PalmPay/5.8.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "0072e8dd-8ebb-42a9-88fc-4a14f5e2605f"}, {"name": "pp_req_sign", "value": "gC0MGZIUclin2Ri4wR3znBLaoQzpRjVyDJwpG7Pr4xcQa75MGLxVRyYL%2F5d435gnXyU2YT%2F9ovV7MreQH4btibKdRZ%2BudOVdGo9Ir6DwTW7W4bAAEOTVRs7QbZZ4OtbI9QUTvyzcvHzdlONVd37lUEyodVGrIrYlxq7adwdhkgU%3D"}, {"name": "pp_req_sign_2", "value": "j3NphOA2NxQIADV95jn2IBufodzxx6H2w8u1ssiDna6v6S2dK0Gnq4nHdtEyC5oQfO5Ska197w%2FM08tKJQS6FvXeoV%2BDLQoid3xPpLWfwO%2FsPGKj39PeDsV8ADmpgCR99PnZwxdm5GThjmG4RTfB5v2pYYrAn8odqu4xra0RsI4%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "163"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"billerId\":\"1753\",\"billerName\":\"Yola Electricity Distribution Company\",\"categoryId\":\"\",\"customerId\":\"2222222222\",\"itemId\":\"1068559040961740\",\"paymentItemType\":\"\"}"}, "headersSize": 427, "bodySize": 163}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Mon, 20 May 2024 09:58:46 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "content-length", "value": "228"}, {"name": "eagleeye-traceid", "value": "eaac16282817161991267122191d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "35"}], "content": {"size": 228, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImN1c3RvbWVySWQiOm51bGwsImN1c3RvbWVyTmFtZSI6bnVsbCwidmFsaWQiOnRydWUsInBheW1lbnRJdGVtVHlwZSI6bnVsbCwiYWN0dWFsUGF5bWVudEl0ZW1UeXBlIjpudWxsLCJmYW1pbHlBY2NvdW50RXhpc3QiOnRydWUsImVycm9ySW5mbyI6bnVsbCwidGlwcyI6bnVsbCwiZGVidEFtb3VudCI6bnVsbH19", "encoding": "base64"}, "redirectURL": null, "headersSize": 146, "bodySize": 228}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 234, "receive": 0}}]}}