{"config": {"name": "testcase description"}, "teststeps": [{"name": "", "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/online-agent-product/agent/status", "params": {"categoryId": "8"}, "headers": {":authority": "ng-apptest.transspay.net", ":method": "GET", ":path": "/api/online-agent-product/agent/status?categoryId=8", ":scheme": "https", "accept-encoding": "gzip", "content-type": "application/json", "pp_channel": "googleplay", "pp_client_ver": "5.7.0&604181603", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "IeD0UGAF%2B%2FVj1s2Tq%2Fm%2BmDNdZWhHZT2GJLiYWidD%2BYFun87Hrxnr6bVLDP359X1FPp6CFc8%2BLyofyDvss4J%2B4BLOXT79bcJ2NY6piH618RAcGgi5hLwiUQbkIsQt%2BvbbHA7XX0gOLB6dbQEAm2PVYt98wd4d10NoWr4nYmL0FAk%3D", "pp_timestamp": "1715082257351", "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e", "user-agent": "PalmPay/5.7.0&604181603 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}, {"check": "body.status", "assert": "equals", "expect": true, "msg": "assert response body status"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/cfront/betting/homepage", "headers": {":authority": "ng-apptest.transspay.net", ":method": "GET", ":path": "/api/cfront/betting/homepage", ":scheme": "https", "accept-encoding": "gzip", "content-type": "application/json", "pp_channel": "googleplay", "pp_client_ver": "5.7.0&604181603", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "o%2BqvP49EO0i0eQJuqJRWoahh91PphfYfmqypjnGmGZDyLFfX3kAq70sAtePnlvBH6xFkXt%2BBK%2FimC9JoloDPEzLbB5nD1iW4rYbhIcC3%2F%2Bk2aB59mr7qgrpxQ55FyLAA3yRZ1oBhWTtMW84dQXzZ%2FCajxUL2W5B9KzGNei%2FVzqo%3D", "pp_timestamp": "1715082257480", "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e", "user-agent": "PalmPay/5.7.0&604181603 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "headers": {":authority": "test-ad.transspay.net", ":method": "POST", ":path": "/api/ad/pullAd", ":scheme": "https", "accept": "application/json", "accept-encoding": "gzip", "accept-language": "en", "appchannel": "googleplay", "authorization": "Bearer MC3253537245", "content-length": "512", "content-type": "application/json; charset=UTF-8", "countrycode": "NG", "imgresolution": "2400*1080", "pp_client_ver": "5.7.0", "signature": "gSOQ6OiynoIcUs6NHjEaq+Gay0AiZ74GIwy/go+tOFipEqEO3VYEwmACPfhLsZBeWk0+ntpB2RMyGtZrVSiu96AFdncouTDyks0VdINSER8hEAzDZ4JD5MWaHmmgV6ymlPTVK/wZVOy7/t9ihl9I5Mt99FStGlfJ8u2uZFWEXzY=", "user-agent": "okhttp/4.9.3"}, "body": {"bizInfo": "{\"adSlotId\":\"8F807507C4FF42DEA3D464C69A2427B9\",\"applicationId\":\"B795C65A0EA3476F985A4029A8566916\",\"extendParam\":{\"deviceModel\":\"Infinix X671B\",\"deviceVersion\":\"Android13\",\"brand\":\"Infinix\",\"deviceInfo\":\"bee648d4b0a5ab70\"},\"gaId\":\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\",\"infoId\":\"MDIzNDA5NTUxMjM0OTEx\",\"lat\":9999.0,\"lon\":9999.0,\"notShowAdIds\":[],\"userId\":\"A12D79F816C144B593703ECFFE53F319\"}", "nonceStr": "1eBQq8ae", "requestTime": 1715082257512, "version": "1.0"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "headers": {":authority": "test-ad.transspay.net", ":method": "POST", ":path": "/api/ad/pullAd", ":scheme": "https", "accept": "application/json", "accept-encoding": "gzip", "accept-language": "en", "appchannel": "googleplay", "authorization": "Bearer MC3253537245", "content-length": "512", "content-type": "application/json; charset=UTF-8", "countrycode": "NG", "imgresolution": "2400*1080", "pp_client_ver": "5.7.0", "signature": "S/RaedBslIuLQ7USYshiQYGFJYitPXAdvCqYSh4oF0916fyOdwqfxvWeQXzHKp81qqUYZoNGdLrqo9c7ZExf3zcRTZ8JZsOBYrPl6fCm8AUyJVqxpMybFoP9eacRTIGvClYb+aPTXh7P5B0vsvVigtFnEKSmb7HGelOvrUp7SZg=", "user-agent": "okhttp/4.9.3"}, "body": {"bizInfo": "{\"adSlotId\":\"D78E194720F0418B9DB2F4EA5D8EBD03\",\"applicationId\":\"B795C65A0EA3476F985A4029A8566916\",\"extendParam\":{\"deviceModel\":\"Infinix X671B\",\"deviceVersion\":\"Android13\",\"brand\":\"Infinix\",\"deviceInfo\":\"bee648d4b0a5ab70\"},\"gaId\":\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\",\"infoId\":\"MDIzNDA5NTUxMjM0OTEx\",\"lat\":9999.0,\"lon\":9999.0,\"notShowAdIds\":[],\"userId\":\"A12D79F816C144B593703ECFFE53F319\"}", "nonceStr": "6UQD7W3f", "requestTime": 1715082257532, "version": "1.0"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "headers": {":authority": "test-ad.transspay.net", ":method": "POST", ":path": "/api/ad/pullAd", ":scheme": "https", "accept": "application/json", "accept-encoding": "gzip", "accept-language": "en", "appchannel": "googleplay", "authorization": "Bearer MC3253537245", "content-length": "512", "content-type": "application/json; charset=UTF-8", "countrycode": "NG", "imgresolution": "2400*1080", "pp_client_ver": "5.7.0", "signature": "Ivt6JPsALxgUYYmkF9Nsr1wMaMxK3lVYJFj3PbBIqyLbf53OCqjvoZ+cuyoUcrCjBSeX6Z4zKjVzUHJZSn+ktTJiS41JrZPe+QQx5q4dOG2Ah7nvP60MCE3kvV1R3dV70oMG3NwTrd6BVVC5EHeQRVSYt46SBT/S+bbSW0yDu6s=", "user-agent": "okhttp/4.9.3"}, "body": {"bizInfo": "{\"adSlotId\":\"A2514D207D424613B585431165DF038B\",\"applicationId\":\"B795C65A0EA3476F985A4029A8566916\",\"extendParam\":{\"deviceModel\":\"Infinix X671B\",\"deviceVersion\":\"Android13\",\"brand\":\"Infinix\",\"deviceInfo\":\"bee648d4b0a5ab70\"},\"gaId\":\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\",\"infoId\":\"MDIzNDA5NTUxMjM0OTEx\",\"lat\":9999.0,\"lon\":9999.0,\"notShowAdIds\":[],\"userId\":\"A12D79F816C144B593703ECFFE53F319\"}", "nonceStr": "jZqTDlxV", "requestTime": 1715082257533, "version": "1.0"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/airtime/v2/query/auto-onoff-sale-info", "headers": {":authority": "ng-apptest.transspay.net", ":method": "POST", ":path": "/api/cfront/airtime/v2/query/auto-onoff-sale-info", ":scheme": "https", "accept-encoding": "gzip", "content-length": "20", "content-type": "application/json; charset=UTF-8", "pp_channel": "googleplay", "pp_client_ver": "5.7.0&604181603", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "FtN%2FwgxWDllv5IYtKRgvSlx6%2F%2BFkeVcJmQAM%2B8c1S%2B1ethqVdj08qzJYTLImSPiKxyokKYxn2FWpi6%2BMm9JrwbKAhFOVIQ94Q9TOjPX6pK07EETGYI1grMWIIavny4AevO1M9Af%2FGMLYPJeipfaTLd57GLj0tG9L0UD63kDkOCw%3D", "pp_req_sign_2": "PyIv%2F%2Bpl89VujiP5JtSGtjZFa968Cw1k5R%2B2QbVJbdWp1BF4fPfB1POytz7jIhdkkZrR%2BgEe0GWdWXISpt8cKOQ39VNF%2F7gcdTv2y5ZRgXgEmj74qYUHUzrWHB7BqfKwPwnZuzhRUGo3V1L1q8iYIw%2B815TJSjJcRfvsqdUEYiw%3D", "pp_timestamp": "1715082257513", "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e", "user-agent": "PalmPay/5.7.0&604181603 (Android 13)"}, "body": {"categoryId": ["8"]}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}, {"check": "body.status", "assert": "equals", "expect": true, "msg": "assert response body status"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/scene-activity-product/bill/pullNew/insidePullNewList", "params": {"showLocation": "BettingHomepage"}, "headers": {":authority": "ng-apptest.transspay.net", ":method": "GET", ":path": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=BettingHomepage", ":scheme": "https", "accept-encoding": "gzip", "content-type": "application/json", "pp_channel": "googleplay", "pp_client_ver": "5.7.0&604181603", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "MfJq3rxcQ34tBlCYU7raDe6tWr15IjVMg8PBej23xnH068ipxHA6oUeOmVbKIZN6eecy%2F6BQW9wNzU1qaUTat3Ajy0RD4zMdySCCyxog%2FWExqrAErzlyBvjJ1Z0G4AX2ok%2FtBS%2BFzcS3xHXLessaP7z20q%2BC5513U1WVJExNrh8%3D", "pp_timestamp": "1715082257534", "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e", "user-agent": "PalmPay/5.7.0&604181603 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}, {"check": "body.status", "assert": "equals", "expect": true, "msg": "assert response body status"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/payBills", "params": {"location": "96"}, "headers": {":authority": "ng-apptest.transspay.net", ":method": "GET", ":path": "/api/airtime/payBills?location=96", ":scheme": "https", "accept-encoding": "gzip", "content-type": "application/json", "pp_channel": "googleplay", "pp_client_ver": "5.7.0&604181603", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "FaMWlGF1DJ8Eat7k8tvvtJsRoN06pT8awMmw5awi6FxyEQQxrPTFQeSnlhF6rUEmOKDk1WGGECyvqauoTgxfrsiiqWvU73Lsfx%2Fwx3gSSO5kxGle0DW00HekG46Ww8jJsrdSt5Sw5lbCT%2Ba25IkOaCSH%2BbdYi21iCAUjZyBWK5w%3D", "pp_timestamp": "1715082257533", "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e", "user-agent": "PalmPay/5.7.0&604181603 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "headers": {":authority": "test-ad.transspay.net", ":method": "POST", ":path": "/api/ad/pullAd", ":scheme": "https", "accept": "application/json", "accept-encoding": "gzip", "accept-language": "en", "appchannel": "googleplay", "authorization": "Bearer MC3253537245", "content-length": "512", "content-type": "application/json; charset=UTF-8", "countrycode": "NG", "imgresolution": "2400*1080", "pp_client_ver": "5.7.0", "signature": "J5wagTJtAuplYJ5UgJi0biihjCg69RrYlT805HFd37ZtKjYhgwcrfBNRMNWbZpC8wfZuszQdQIK4Iy5/SXfehvbTdr7FO9b46CjGYqQoMp+tYyuM3OwV7gziSlzPDAeHRTirDozWypqBg02Ii8riot/2g1BsyXuwgXEDYYDwUNA=", "user-agent": "okhttp/4.9.3"}, "body": {"bizInfo": "{\"adSlotId\":\"34B15F9BA85A4A138C32C0D030DE1528\",\"applicationId\":\"B795C65A0EA3476F985A4029A8566916\",\"extendParam\":{\"deviceModel\":\"Infinix X671B\",\"deviceVersion\":\"Android13\",\"brand\":\"Infinix\",\"deviceInfo\":\"bee648d4b0a5ab70\"},\"gaId\":\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\",\"infoId\":\"MDIzNDA5NTUxMjM0OTEx\",\"lat\":9999.0,\"lon\":9999.0,\"notShowAdIds\":[],\"userId\":\"A12D79F816C144B593703ECFFE53F319\"}", "nonceStr": "lnZo5P0r", "requestTime": 1715082257532, "version": "1.0"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/coupon/statistics", "headers": {":authority": "ng-apptest.transspay.net", ":method": "POST", ":path": "/api/cfront/coupon/statistics", ":scheme": "https", "accept-encoding": "gzip", "content-length": "45", "content-type": "application/json; charset=UTF-8", "pp_channel": "googleplay", "pp_client_ver": "5.7.0&604181603", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "VQcWjaDMYb5g71gh12GRztzdZrWbaTFJbvto7dSBMyJpAFq7OAM4er9iloSy%2F%2F%2FDbGfyBTY%2FJnJ1JeZaOvbNQZIE9WYbuiQTZbB5HW4gahhuSOWMGwmZiuZxkhdb6z%2Bgek%2BKR9MNupvmx30bf5pwNdvm52ltFYJadoLYlLdsOQA%3D", "pp_req_sign_2": "eoLycmKopxcL4nKaRyo%2FarlNMQt%2FseMPiFWpfio%2FoH5aRFK52xZC2bKtLWMJc3xn4lrsHjZiMwGbYk%2Fqiy6I7p4mCcbvHZ5ZeKh2ixprE5i1vtFMkWJci34%2B3zRaCVp7lYq09ST4IGeJz3ITbDNI8QHZVHRv4joJFjvq2Z9aI0o%3D", "pp_timestamp": "1715082257547", "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e", "user-agent": "PalmPay/5.7.0&604181603 (Android 13)"}, "body": {"pageNum": 1, "pageSize": 100, "transType": "96"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}, {"check": "body.status", "assert": "equals", "expect": true, "msg": "assert response body status"}]}, {"name": "", "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "headers": {":authority": "test-ad.transspay.net", ":method": "POST", ":path": "/api/ad/pullAd", ":scheme": "https", "accept": "application/json", "accept-encoding": "gzip", "accept-language": "en", "appchannel": "googleplay", "authorization": "Bearer MC3253537245", "content-length": "512", "content-type": "application/json; charset=UTF-8", "countrycode": "NG", "imgresolution": "2400*1080", "pp_client_ver": "5.7.0", "signature": "aM1AiAObgi9M0MX4y0OBFkmaurYFFTV0wtS82xjeP8qwvTPoBqcoB9kZB3MY/tMDS3olBcG3R4yas4hNFDV0R+eE/ZfKR/cyxn5pfmx3NVUY3gASfflAH2W95Cvet84mGem8DoB23ndQkYepvr48SKOpCzVEeMFBbLDWzYWyG6w=", "user-agent": "okhttp/4.9.3"}, "body": {"bizInfo": "{\"adSlotId\":\"15D71D47F4AA41328D4FA6792A9B8023\",\"applicationId\":\"B795C65A0EA3476F985A4029A8566916\",\"extendParam\":{\"deviceModel\":\"Infinix X671B\",\"deviceVersion\":\"Android13\",\"brand\":\"Infinix\",\"deviceInfo\":\"bee648d4b0a5ab70\"},\"gaId\":\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\",\"infoId\":\"MDIzNDA5NTUxMjM0OTEx\",\"lat\":9999.0,\"lon\":9999.0,\"notShowAdIds\":[],\"userId\":\"A12D79F816C144B593703ECFFE53F319\"}", "nonceStr": "GGDZIAV3", "requestTime": 1715082257543, "version": "1.0"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/dataBurialPoint/adBehavior", "headers": {":authority": "test-ad.transspay.net", ":method": "POST", ":path": "/api/dataBurialPoint/adBehavior", ":scheme": "https", "accept": "application/json", "accept-encoding": "gzip", "authorization": "Bearer MC3253537245", "content-length": "894", "content-type": "application/json", "countrycode": "NG", "signature": "Iwzb8yPkf2J9wKrGF1aXQkOvCFIKHoeMGGWP3EdpXXEv+kmo9Gi5dVPuvHt/1Crrkno7/WNojcqept3UZeMvQ4z/jwKYTfjrMZ1mgbvjzJ1obn4uiqFvsRGDCYfmk4FSX9pGtLPZ020b9jW8lzAYoih2hQ/LwDjiIA+8ZNmKv/A=", "user-agent": "okhttp/4.9.3"}, "body": {"bizInfo": "[{\"body\":{\"adId\":\"F4724209550E46A98AFDB2B631F37D06\",\"adSlotId\":\"34B15F9BA85A4A138C32C0D030DE1528\",\"adTime\":1715082257836,\"countryCode\":\"NG\",\"eventSubType\":\"\",\"eventType\":\"autoBegin\",\"num\":1,\"operator\":\"\",\"pointWindowX\":\"\",\"pointWindowY\":\"\",\"relatedId\":\"\",\"userId\":\"\",\"wifi\":true},\"head\":{\"SDKVersion\":\"1.0.36-SNAPSHOT\",\"appIdentifier\":\"com.transsnet.palmpay\",\"appVersion\":\"5.7.0\",\"appVersionCode\":604181603,\"applicationId\":\"B795C65A0EA3476F985A4029A8566916\",\"behaviorId\":\"B795C65A0EA3476F985A4029A85669161715082257836\",\"createTime\":1715082257840,\"eventTime\":1715082257840,\"gaid\":\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\",\"imei\":\"\",\"ip\":\"*************\",\"macAddress\":\"please open wifi\",\"operSystem\":\"Android\"}}]", "nonceStr": "7ChvYk1A", "requestTime": 1715082257844, "version": "1.0.36-SNAPSHOT"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.data", "assert": "equals", "expect": null, "msg": "assert response body data"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/cfront/betting/info", "params": {"billerId": "2269"}, "headers": {":authority": "ng-apptest.transspay.net", ":method": "GET", ":path": "/api/cfront/betting/info?billerId=2269", ":scheme": "https", "accept-encoding": "gzip", "content-type": "application/json", "pp_channel": "googleplay", "pp_client_ver": "5.7.0&604181603", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "jRKrhRZKveWA83u83v00jM%2FWqEzyD9eY85AqYqvKCettQwz5a%2BiYouatzRMRjXMo1hhwWklaq14Lib6rQ1K26GJmu9P645NW0TH%2BLXEwEbST5IyCqVYG7psEKrv2nzQd7jyCS8rsrn8f3G%2F88%2B1qJw9Hkt7e2L4rEO2OPpuhouk%3D", "pp_timestamp": "1715082259940", "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e", "user-agent": "PalmPay/5.7.0&604181603 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}, {"name": "", "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/pay/queryLimitAmtForOrder", "headers": {":authority": "ng-apptest.transspay.net", ":method": "POST", ":path": "/api/cfront/pay/queryLimitAmtForOrder", ":scheme": "https", "accept-encoding": "gzip", "content-length": "18", "content-type": "application/json; charset=UTF-8", "pp_channel": "googleplay", "pp_client_ver": "5.7.0&604181603", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "pZys%2FsuRLbotF4QL%2FBvDApmq%2B%2BAEs0SCk9rYm5UjYyqtY%2FDLtPbBStjsXogVOzfyKnHSDwySEUHoO%2FpMX0%2Fav%2BScvd9ew2ZFEFQguBO1i0rNVYnTrMjGEXUSydq9ZNQC4JUECi3oio7%2FuJ6gBGCi3%2FT00gc2qoIOugHEqoTaQ0I%3D", "pp_req_sign_2": "Yxscngo9VXa7FGc7hzQ73fr8G0LIi8vUCcD6vGj%2BTfIWLAbNVDF%2BOM%2F03k%2FZ%2B4tqoos5rxBNuxzxCEwChLOM2omJQxNTZmXZaopnE02DK1hHNxUJFm4EcS06sbxSTO3Wh6nN0IT%2B%2BzJMeVicn1NfvM1AcQvieI1AMbKpe7xB2E4%3D", "pp_timestamp": "1715082259942", "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e", "user-agent": "PalmPay/5.7.0&604181603 (Android 13)"}, "body": {"transType": "96"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}, {"check": "body.status", "assert": "equals", "expect": true, "msg": "assert response body status"}]}, {"name": "", "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/gateway/betting/match", "params": {"timeZone": "GMT+08:00"}, "headers": {":authority": "ng-apptest.transspay.net", ":method": "GET", ":path": "/api/gateway/betting/match?timeZone=GMT%2B08%3A00", ":scheme": "https", "accept-encoding": "gzip", "content-type": "application/json", "pp_channel": "googleplay", "pp_client_ver": "5.7.0&604181603", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "KFaf8TQF8vgMwCzBo5JeRLUFVrKLJlqUm15a0YPeNClk%2B9HZ26Ra8W0LcuR9ajEfWSdvtWzd6VESc9RLo48KnNMz2AAmyeXDH9HqbqSg0uKB23hlWagxTrXEz9e6qK4VBllma3SLAjSaWFu9E%2FSbsBxW3STmaore2mcDZXgELrQ%3D", "pp_timestamp": "1715082260270", "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e", "user-agent": "PalmPay/5.7.0&604181603 (Android 13)"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json;charset=UTF-8", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}]}]}