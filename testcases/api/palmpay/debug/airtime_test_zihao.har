{"log": {"version": "1.2", "creator": {"name": "<PERSON>", "version": "4.6.4"}, "entries": [{"startedDateTime": "2024-05-07T19:29:00.584+08:00", "time": 292, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/home/<USER>", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/home/<USER>"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081340689"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "hhkr8RfcZyMcGTqrhBADFzpcL%2BbUICNsszOd8dJq%2F3fy%2Bxv26ozcZ%2Beb7LTF6DSQy1jEh3GCvnxlOrGycfe4tIslw3kdYdUoIi9IyKpyzaBr2eSIdNCNuelLEI%2FNLRdY%2BYy9EgI2SKjFdIRvXjIBtoTZ5WawCQ5bqX5FIEq3T8M%3D"}, {"name": "pp_req_sign_2", "value": "V9sAo5i3J3NHc5DRr617mYAS3CA346K%2Bgkk4LfyOawzMC3yiVtblecWdSFmIc9WxOP7e8gvwLdyfdwDH17zE3YXCKlqdflnqq2H7DutSHkTxOtNp6qUKKdIsKO3M%2FktksC0vU6Zy8ZRjk7e38wVg5iwHQZ8VYa07PKsw3t8KbGI%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "11"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"scene\":0}"}, "headersSize": 353, "bodySize": 11}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:01 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150813415916668d0001"}, {"name": "x-application-context", "value": "c-bff-product:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "62"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 41731, "compression": 37618, "mimeType": "application/json;charset=UTF-8", "text": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "encoding": "base64"}, "redirectURL": null, "headersSize": 175, "bodySize": 4113}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 275, "receive": 16}}, {"startedDateTime": "2024-05-07T19:29:02.786+08:00", "time": 233, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/online-agent-product/agent/status?categoryId=10", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/online-agent-product/agent/status?categoryId=10"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081342893"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "snBVpaCfrWbqHDdddcZbxQIoHP6P7mX93ycKSPexbcyMX9FT2L%2FWngJ1IIp2qWLwQDknNbFY0CfTcsZRWp6dioQuayvqbcadeOOqL7W6%2BZlg3G%2BRox8TAMtNSdk3vZTA6%2F6FWQqo1uAguflSp%2B0vWcogurYFx0z0IlhZNYWDZRo%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Tu<PERSON>, 07 May 2024 11:26:34 GMT"}], "queryString": [{"name": "categoryId", "value": "10"}], "headersSize": 250, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:03 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "559"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150813437926671d0001"}, {"name": "x-envoy-upstream-service-time", "value": "18"}], "content": {"size": 559, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7Im1lbWJlcklkIjoiQTEyRDc5RjgxNkMxNDRCNTkzNzAzRUNGRkU1M0YzMTkiLCJpc1doaXRlTGlzdE1lbWJlciI6dHJ1ZSwiYWdlbnRMZXZlbCI6MSwibGV2ZWxEZXNjIjoiWW91ciBhZ2VudCBsZXZlbCB3aWxsIGJlIG9uIEZpcnN0IExldmVsIHdoZW4geW91ciB0cmFuc2FjdGlvbiBhbW91bnQgb2YgQWlydGltZeOAgURhdGHjgIFCZXR0aW5n44CBRWxlY3RyaWNpdHnjgIFUViBpcyBtb3JlIHRoYW4gTjEyMDAuXG5UaGUgY29tbWlzc2lvbiByYXRlIG9mIEZpcnN0IExldmVsIGFzIGZvbGxvd++8mlxuQWlydGltZSB1cCB0byAxJe+8m1xuRGF0YSB1cCB0byAxJe+8m1xuQmV0dGluZyB1cCB0byAxJe+8m1xuRWxlY3RyaWNpdHkgdXAgdG8gMSXvvJtcblRWIHVwIHRvIDElIiwiYWdlbnRTdGF0dXMiOjAsImFnZW50VHlwZSI6MSwibG9nbyI6bnVsbCwidGlwcyI6IkJlY29tZSBQYWxtUGF5IFBsdXMsIGVhcm4gY29tbWlzc2lvbi4iLCJhZ2VudFN0YXRlIjoiTGFnb3MifSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 105, "bodySize": 559}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 231, "receive": 1}}, {"startedDateTime": "2024-05-07T19:29:02.876+08:00", "time": 2012, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/airtime/menu/v4", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/airtime/menu/v4"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081342988"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "QAcCbkrsxTgQOgCAYuSE5KZVFXlUoLwCGz0M9HE%2BmimNJGSpGJvoEimJWC%2Baeyo53xXy8yzPlMArx00B%2FkWky%2BEy07O8hr2OBt7zFBjIvgwGsTm6iHPRMI8uBZplMWqRwmZtwgdhrQEGNxEv%2BnKorlwekxkjCtHr986DQurLFyo%3D"}, {"name": "content-length", "value": "0"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json", "text": ""}, "headersSize": 201, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:05 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac16306117150813438826644d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "1791"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 10600, "compression": 9089, "mimeType": "application/json;charset=UTF-8", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 172, "bodySize": 1511}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 2011, "receive": 0}}, {"startedDateTime": "2024-05-07T19:29:02.931+08:00", "time": 261, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/cfront/airtime/recharge/records/latest", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/cfront/airtime/recharge/records/latest"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081342997"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "hBh0T6wTDx2OQEnjCoWcfglQW5ieaP9SFZvqwjix3cw2qalX%2BcdemCv2Z14GyzxPVHVFMYt%2FjZ2s1YWjgOz%2FmofXJTwYhYZZvlQcKOyISo6h%2FI8MbkIJFOs11v9wuUTI2D5eW3IS1%2BjwhYQbyhFzAg8M6cMc%2FBmw6s3EyCPQngM%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Tu<PERSON>, 07 May 2024 11:26:34 GMT"}], "queryString": [], "headersSize": 210, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:03 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150813439376673d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "43"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 2709, "compression": 2085, "mimeType": "application/json;charset=UTF-8", "text": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "encoding": "base64"}, "redirectURL": null, "headersSize": 172, "bodySize": 624}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 257, "receive": 4}}, {"startedDateTime": "2024-05-07T19:29:02.931+08:00", "time": 234, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/techShareLink", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/techShareLink"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081342997"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "hBh0T6wTDx2OQEnjCoWcfglQW5ieaP9SFZvqwjix3cw2qalX%2BcdemCv2Z14GyzxPVHVFMYt%2FjZ2s1YWjgOz%2FmofXJTwYhYZZvlQcKOyISo6h%2FI8MbkIJFOs11v9wuUTI2D5eW3IS1%2BjwhYQbyhFzAg8M6cMc%2FBmw6s3EyCPQngM%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Tu<PERSON>, 07 May 2024 11:26:34 GMT"}], "queryString": [], "headersSize": 168, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:03 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "content-length", "value": "55"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150813439416674d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "19"}], "content": {"size": 55, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpudWxsfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 145, "bodySize": 55}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 232, "receive": 1}}, {"startedDateTime": "2024-05-07T19:29:02.931+08:00", "time": 1670, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/airtime/item/tips", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/airtime/item/tips"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081343009"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "PMGt%2FcYzyasuhl92nbJj6B5zg8Tfozd7i1ztrrcA6hqjimvwPGVQNWvl%2FkaRzKerkeHNdB%2BQJRCBDlhXAf%2B5HaorDhq44dQjHHKm71VFwE2aDY1heGWFVkgEYLjd8McPxlAeg57B6oQFDV86PybuW4fp7kJaJXggNuO358ZlSSw%3D"}, {"name": "content-length", "value": "0"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json", "text": ""}, "headersSize": 195, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:05 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac16306117150813439436645d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "1452"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 2681, "compression": 2106, "mimeType": "application/json;charset=UTF-8", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 173, "bodySize": 575}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 1665, "receive": 4}}, {"startedDateTime": "2024-05-07T19:29:02.931+08:00", "time": 230, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/preOrders/page", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/preOrders/page"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081343012"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "hzw5Wb3kYUmKisIf0MKvKrdpQVW5Jk7eKm1f3c95fEx%2BcgviRW9RegJu8Di3n3HFTCX30W%2BUzwtrLTFPtVMUWrKAknJy4xm9wymojjU4kw4xCwNdrB5aFlDBtPwfMhbgy%2BFWXgA5zaZ9C44npnyi6B0HVUMJbkdpvbUbk%2Bxh5TQ%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Tue, 07 May 2024 11:27:21 GMT"}], "queryString": [], "headersSize": 218, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:03 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "content-length", "value": "333"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150813439376672d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "15"}], "content": {"size": 333, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7InRvdGFsIjowLCJsaXN0IjpbXSwicGFnZU51bSI6MSwicGFnZVNpemUiOjEwMCwic2l6ZSI6MCwic3RhcnRSb3ciOjAsImVuZFJvdyI6MCwicGFnZXMiOjAsInByZVBhZ2UiOjAsIm5leHRQYWdlIjowLCJpc0ZpcnN0UGFnZSI6dHJ1ZSwiaXNMYXN0UGFnZSI6dHJ1ZSwiaGFzUHJldmlvdXNQYWdlIjpmYWxzZSwiaGFzTmV4dFBhZ2UiOmZhbHNlLCJuYXZpZ2F0ZVBhZ2VzIjowLCJuYXZpZ2F0ZXBhZ2VOdW1zIjpudWxsLCJuYXZpZ2F0ZUZpcnN0UGFnZSI6MCwibmF2aWdhdGVMYXN0UGFnZSI6MH19", "encoding": "base64"}, "redirectURL": null, "headersSize": 146, "bodySize": 333}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 229, "receive": 0}}, {"startedDateTime": "2024-05-07T19:29:02.977+08:00", "time": 1547, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/cfront/quickteller/resolve/network/query?mobileNo=09551234911", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/cfront/quickteller/resolve/network/query?mobileNo=09551234911"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081343076"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "wC02FlTsbHusBVyhqEz5h%2BR5GacwugzY691716TduaLwc0xO7TJATRFV47kQFmguo47GmP2VDHk2qXgAvEkt3xX9HmVnibaWdO4PgqU%2BlHOxvK1DaLG8Ir8isFupjFTpe9bSUp4Ar%2FB4qH8ruGMJGKhfPRYJ%2BvK2rGQ8SlgxqVQ%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Tu<PERSON>, 07 May 2024 11:26:36 GMT"}], "queryString": [{"name": "mobileNo", "value": "09551234911"}], "headersSize": 251, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:05 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "content-length", "value": "56"}, {"name": "eagleeye-traceid", "value": "eaac16306117150813439836646d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "1333"}], "content": {"size": 56, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjoiR0xPIn0=", "encoding": "base64"}, "redirectURL": null, "headersSize": 147, "bodySize": 56}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 1546, "receive": 1}}, {"startedDateTime": "2024-05-07T19:29:02.977+08:00", "time": 226, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/services", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/services"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081343084"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "Bek5dF2xFCdtaV3yIpv3Vultt819glOYGX7W4i9up1z3r%2Bq0%2Fr9pJXhtRZ4ef%2FNrWmKPcgiC1e0RKQde4xkpCQH%2FYGIhRhwwgfurbKgOXmxoLyQxHh9dj562R%2Ftl4Zt8U1wkNrJzFwzzTsTH4jupZL1LmJ3%2Fq1X%2FNoTzeHKzwlU%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Tu<PERSON>, 07 May 2024 11:26:34 GMT"}], "queryString": [], "headersSize": 230, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:03 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "content-length", "value": "1020"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150813439836675d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "11"}], "content": {"size": 1020, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbeyJ0aXRsZSI6IlJlY2hhcmdlMmNhc2giLCJzdWJoZWFkIjoiU2VsbCBhaXJ0aW1lIHRvIGdldCBjYXNoIiwiY29kZSI6bnVsbCwiaWNvbiI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2ODQ4Mjg3NTAyNzExMS1xdF9pY29uX3JlY2hhcmdlX3RvX2Nhc2gucG5nIiwiZGFya0ljb24iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNzA4NTE3Mjc1MDE4MTktUmVjaGFyZ2UyY2FzaC5wbmciLCJhbmRyb2lkTGluayI6Ii9haXJ0aW1lL3JlY2hhcmdlXzJfY2FzaCIsImlvc0xpbmsiOiJwYWxtcGF5Oi8vYWlydGltZS9yZWNoYXJnZTJjYXNoIn0seyJ0aXRsZSI6IlNjaGVkdWxlIFRvcC11cCIsInN1YmhlYWQiOiJOZXZlciBydW4gb3V0IG9mIGFpcnRpbWUiLCJjb2RlIjpudWxsLCJpY29uIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTY4NDgyODQ2OTA4MDE5LWN2X2ljb25fcmVmcmVzaC5wbmciLCJkYXJrSWNvbiI6bnVsbCwiYW5kcm9pZExpbmsiOiIvYWlydGltZS9hdXRvX3RvcF91cF9yb3V0ZV9wYWdlIiwiaW9zTGluayI6InBhbG1wYXk6Ly9haXJ0aW1lL3NjaGVkdWxlX2hvbWUifSx7InRpdGxlIjoiVVNTRCBlbnF1aXJ5Iiwic3ViaGVhZCI6IkNoZWNrIHBob25lIGJhbGFuY2UgYW5kIG1vcmUiLCJjb2RlIjpudWxsLCJpY29uIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTY4NDgyODY0Nzc4ODE1LWN2X2ljb25fc2hvcnRfY29kZS5wbmciLCJkYXJrSWNvbiI6bnVsbCwiYW5kcm9pZExpbmsiOiIvY29yZUltcGwvdXNzZF9saXN0X3BhZ2UiLCJpb3NMaW5rIjoicGFsbXBheTovL3RvcHVwaG9tZS91c3NkX2xpc3QifV19", "encoding": "base64"}, "redirectURL": null, "headersSize": 148, "bodySize": 1020}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 225, "receive": 1}}, {"startedDateTime": "2024-05-07T19:29:03.003+08:00", "time": 284, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "f6Zup9dDUqcw4i0qzaUL6sHjeYrewe/hyJCX9rIX681OsrPingsgEn0QOW6f53hPpQJJeTa1m+L2t8mqATKgtnHu787MbVsyrFp0L1yj8CD3/f6c91HflY3rTSpJYrSaWEKmL9scmWcliqMALygFdTzsVhnnV8g5Ioj/JMRjUsM="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.7.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"01FFAE0D0B2447B68A767616A137F063\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"KeYSIwtC\",\"requestTime\":1715081343114,\"version\":\"1.0\"}"}, "headersSize": 154, "bodySize": 512}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:04 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "ad-center:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "58"}, {"name": "x-envoy-peer-metadata-id", "value": "sidecar~172.22.48.58~ad-center-daily-748c76dc87-nrq9g.daily~daily.svc.cluster.local"}, {"name": "x-envoy-peer-metadata", "value": "CjAKDkFQUF9DT05UQUlORVJTEh4aHGNvbnN1bC1hZ2VudCxhZC1jZW50ZXItZGFpbHkKGgoKQ0xVU1RFUl9JRBIMGgpLdWJlcm5ldGVzCh4KDElOU1RBTkNFX0lQUxIOGgwxNzIuMjIuNDguNTgKGQoNSVNUSU9fVkVSU0lPThIIGgYxLjE5LjAKoQMKBkxBQkVMUxKWAyqTAwoYCgNhcHASERoPYWQtY2VudGVyLWRhaWx5ChsKE2FybXNQaWxvdEF1dG9FbmFibGUSBBoCb24KKwoWYXJtc1BpbG90Q3JlYXRlQXBwTmFtZRIRGg9hZC1jZW50ZXItZGFpbHkKGgoFZ3JvdXASERoPYWQtY2VudGVyLWRhaWx5ChoKEm1zZVBpbG90QXV0b0VuYWJsZRIEGgJvbgo0ChVtc2VQaWxvdENyZWF0ZUFwcE5hbWUSGxoZYWQtY2VudGVyLWZyYW5rZnVydC1kYWlseQokChlzZWN1cml0eS5pc3Rpby5pby90bHNNb2RlEgcaBWlzdGlvCjQKH3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLW5hbWUSERoPYWQtY2VudGVyLWRhaWx5Ci8KI3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLXJldmlzaW9uEggaBmxhdGVzdAohChdzaWRlY2FyLmlzdGlvLmlvL2luamVjdBIGGgR0cnVlCg8KA3hkYxIIGgZzdGFibGUKGgoHTUVTSF9JRBIPGg1jbHVzdGVyLmxvY2FsCioKBE5BTUUSIhogYWQtY2VudGVyLWRhaWx5LTc0OGM3NmRjODctbnJxOWcKFAoJTkFNRVNQQUNFEgcaBWRhaWx5ClEKBU9XTkVSEkgaRmt1YmVybmV0ZXM6Ly9hcGlzL2FwcHMvdjEvbmFtZXNwYWNlcy9kYWlseS9kZXBsb3ltZW50cy9hZC1jZW50ZXItZGFpbHkKIgoNV09SS0xPQURfTkFNRRIRGg9hZC1jZW50ZXItZGFpbHk="}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 1027, "compression": 468, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImV4cGlyZWRUaW1lIjo2MDAwMCwibGlzdCI6W3siYWRJZCI6IjNEQUZDNjAxQjNBMzQzOUE5REJFQjlDNzIxRjkyODhEIiwiYXBwbGljYXRpb25JZCI6IkI3OTVDNjVBMEVBMzQ3NkY5ODVBNDAyOUE4NTY2OTE2IiwiYWRTbG90SWQiOiIwMUZGQUUwRDBCMjQ0N0I2OEE3Njc2MTZBMTM3RjA2MyIsImltYWdlVXJsIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vbWVyY2hhbnQvMTY5MTQ2NTgxMTE4MzEzLndlYnAiLCJtdWx0aXBsZUltYWdlVXJsIjpudWxsLCJoNUltYWdlVXJsIjpudWxsLCJqdW1wVHlwZSI6IjAyIiwianVtcFBhcmFtcyI6IiIsInJlbGF0aXZlVXJsIjoiL3ZhbGVudGluZS9ob21lIiwiaW1hZ2VEaW1lbnRpb24iOiI5ODQqMTM4IiwiaW1hZ2VEaW1lbnRpb25MaXN0IjoiW1wiOTg0KjEzOFwiXSIsInZhbGlkU3RhcnRUaW1lIjoxNjY1NDQyODAwMDAwLCJ2YWxpZEVuZFRpbWUiOjE3MzA1MDE5OTkwMDAsInNob3dUaW1lcyI6bnVsbCwic2hvd0RhdGVzIjpudWxsLCJzaG93VGltZXNDb25kaXRpb24iOm51bGwsImNsaWNrRnJlcXVlbmN5IjpudWxsLCJjbG9zZUNvbnRyb2wiOm51bGwsInNob3dDbG9zZUJ1dHRvbiI6MCwiY291bnRyeUNvZGUiOiJORyIsInRleHRDb250ZW50IjpudWxsLCJ0ZXh0Rm9udFNpemUiOm51bGwsInRleHRGb250Q29sb3IiOiIiLCJ0ZXh0SWNvblVybCI6IiIsInRleHRTY3JvbGxTcGVlZCI6bnVsbCwiYmFja2dyb3VuZENvbG9yIjoiIiwiYWRUeXBlIjoxLCJuYXRpdmVBZFRpdGxlIjpudWxsLCJuYXRpdmVBZENvbnRlbnQiOm51bGwsIm5hdGl2ZUFkUGhvdG8iOm51bGwsImltYWdlVXJsRGFyayI6bnVsbCwidGV4dEZvbnRDb2xvckRhcmsiOm51bGwsInRleHRJY29uVXJsRGFyayI6bnVsbCwiYmFja2dyb3VuZENvbG9yRGFyayI6bnVsbCwibmF0aXZlQWRQaG90b0RhcmsiOm51bGwsImg1SW1hZ2VVcmxEYXJrIjpudWxsfV19fQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 1379, "bodySize": 559}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 283, "receive": 1}}, {"startedDateTime": "2024-05-07T19:29:03.023+08:00", "time": 424, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=AirtimeHomepage", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=AirtimeHomepage"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081343117"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "MDdUpiZcKLrNKt7XhI4YLQDL%2FPCehGfgbPInxaQZh1myALu%2FW1RC1rJtdmXQ1F90cR7ZRsq2V2OtPCTtCY8JYfDwFg0JcMS6Vzh8b6NeeErqVpVHLY0PNkZi%2B97fjPomIl0rgvRmVpYtpkiMKwfW64E2oD4Fv66ZxQ4Iq81vAVs%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Tue, 07 May 2024 11:27:21 GMT"}], "queryString": [{"name": "showLocation", "value": "AirtimeHomepage"}], "headersSize": 248, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:04 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "77"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150813440296677d0001"}, {"name": "x-envoy-upstream-service-time", "value": "208"}], "content": {"size": 77, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjEwMDAwMDEzIiwicmVzcE1zZyI6IkFjdGl2aXR5IGVuZGVkIiwiZGF0YSI6bnVsbCwic3RhdHVzIjpmYWxzZX0=", "encoding": "base64"}, "redirectURL": null, "headersSize": 104, "bodySize": 77}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 424, "receive": 0}}, {"startedDateTime": "2024-05-07T19:29:03.023+08:00", "time": 307, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/airtime/cashback/getBizConfig", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/airtime/cashback/getBizConfig"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081343111"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "I%2BUEQfGWcnUQMCHUs5X1zzCXdsupvkLr8duRsJh%2Bi3aOPbLsATjqRPqGUcFk1g0C8VlT3MNXfP04d4RwXoq%2Bos7hCGhR17Jgx67z0mlD1ZMCWLtHVQqyRqzslJBSi4RZ7Iw%2Bxd4XYexrFWw68CFIQB%2BobZzTFD6h%2BpFvrBdQwrc%3D"}, {"name": "pp_req_sign_2", "value": "H8kwISn%2BUeHFLdPNucdGhq%2FBgJwgs88rAXKttm1GcTrHoqw8NXhJ0WhKp2EmF5JZ%2Bf4%2BJT9kOEjvrZDE6lzODTOYaLzZZ7E5pxvXQN08wqZ8x4wyRMYmB0mhU3wvijEqAI1dtkNHdCrpKIu6XcF6FAXpUbkGMyn3uSHS%2BaFmZIw%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "18"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"transType\":\"04\"}"}, "headersSize": 385, "bodySize": 18}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:04 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "content-length", "value": "485"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150813440296676d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "92"}], "content": {"size": 485, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7InRyYW5zVHlwZSI6IjA0IiwiaWNvbiI6Imh0dHBzOi8vdHJhbnNzbmV0LWFwcC1pbWFnZXMtcHJvZC5zMy5ldS13ZXN0LTEuYW1hem9uYXdzLmNvbS8yMDIzMDcxNS82NGIyMTcwN2JhNTNjMjEwMjEwNDM5ZmEucG5nIiwiaGludCI6bnVsbCwiY29udGVudCI6IjxiPjxmb250IGNvbG9yPVwiI0ZGQUEwQ1wiPjUwMDAgUGFsbVBvaW50czwvZm9udD48L2I+IGF2YWlsYWJsZSIsImRhcmtDb250ZW50IjoiXCI8Yj48Zm9udCBjb2xvcj1cIiNFNjk1MDBcIj41MDAwIFBhbG1Qb2ludHM8L2ZvbnQ+PGZvbnQgY29sb3I9XCIjRkZGRkZGXCI+IGF2YWlsYWJsZTwvZm9udD48L2I+XCIiLCJ1cmwiOiIvbWFpbi9wb2ludC9saXN0IiwiYW5kcmlvZFVybCI6Ii9tYWluL3BvaW50L2xpc3QiLCJpb3NVcmwiOiJwYWxtcGF5Oi8vbWFpbi9wb2ludHNzdGF0ZW1lbnQ/dGFnPTIifX0=", "encoding": "base64"}, "redirectURL": null, "headersSize": 146, "bodySize": 485}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 307, "receive": 0}}, {"startedDateTime": "2024-05-07T19:29:03.055+08:00", "time": 234, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "I3L99jh+6yOdHzZUfSXZJIpZx0+mqBKdOusb1meZMLzcoFAHwUx2sZ5lfP6MDs2DsHJpnikb74g0iwm+KxBtFWIyqnCWb2xw+4r5Fl2Gb3kljG16n7+fVlcict6nDCdOVRio8q9E1vHGRJ55qh4RWtz685vkZc/jT+NEG+i2eLU="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.7.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"A4558A5E2CFD41C28EEBB831CE3E90B3\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"tBQPCWyh\",\"requestTime\":1715081343114,\"version\":\"1.0\"}"}, "headersSize": 159, "bodySize": 512}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:04 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "ad-center:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "10"}, {"name": "x-envoy-peer-metadata-id", "value": "sidecar~172.22.48.58~ad-center-daily-748c76dc87-nrq9g.daily~daily.svc.cluster.local"}, {"name": "x-envoy-peer-metadata", "value": "CjAKDkFQUF9DT05UQUlORVJTEh4aHGNvbnN1bC1hZ2VudCxhZC1jZW50ZXItZGFpbHkKGgoKQ0xVU1RFUl9JRBIMGgpLdWJlcm5ldGVzCh4KDElOU1RBTkNFX0lQUxIOGgwxNzIuMjIuNDguNTgKGQoNSVNUSU9fVkVSU0lPThIIGgYxLjE5LjAKoQMKBkxBQkVMUxKWAyqTAwoYCgNhcHASERoPYWQtY2VudGVyLWRhaWx5ChsKE2FybXNQaWxvdEF1dG9FbmFibGUSBBoCb24KKwoWYXJtc1BpbG90Q3JlYXRlQXBwTmFtZRIRGg9hZC1jZW50ZXItZGFpbHkKGgoFZ3JvdXASERoPYWQtY2VudGVyLWRhaWx5ChoKEm1zZVBpbG90QXV0b0VuYWJsZRIEGgJvbgo0ChVtc2VQaWxvdENyZWF0ZUFwcE5hbWUSGxoZYWQtY2VudGVyLWZyYW5rZnVydC1kYWlseQokChlzZWN1cml0eS5pc3Rpby5pby90bHNNb2RlEgcaBWlzdGlvCjQKH3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLW5hbWUSERoPYWQtY2VudGVyLWRhaWx5Ci8KI3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLXJldmlzaW9uEggaBmxhdGVzdAohChdzaWRlY2FyLmlzdGlvLmlvL2luamVjdBIGGgR0cnVlCg8KA3hkYxIIGgZzdGFibGUKGgoHTUVTSF9JRBIPGg1jbHVzdGVyLmxvY2FsCioKBE5BTUUSIhogYWQtY2VudGVyLWRhaWx5LTc0OGM3NmRjODctbnJxOWcKFAoJTkFNRVNQQUNFEgcaBWRhaWx5ClEKBU9XTkVSEkgaRmt1YmVybmV0ZXM6Ly9hcGlzL2FwcHMvdjEvbmFtZXNwYWNlcy9kYWlseS9kZXBsb3ltZW50cy9hZC1jZW50ZXItZGFpbHkKIgoNV09SS0xPQURfTkFNRRIRGg9hZC1jZW50ZXItZGFpbHk="}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 84, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImV4cGlyZWRUaW1lIjo2MDAwMCwibGlzdCI6bnVsbH19", "encoding": "base64"}, "redirectURL": null, "headersSize": 1379, "bodySize": 96}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 233, "receive": 1}}, {"startedDateTime": "2024-05-07T19:29:03.064+08:00", "time": 240, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/airtime/v2/query/auto-onoff-sale-info", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/airtime/v2/query/auto-onoff-sale-info"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081343174"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "BTYL5WgQ8USARSyjmPgVyirxyM0DGfFGWpui5rGdqqUZ%2BSNA6T3ldDcjDP1sBKqdLq4FMOahiZJZcI8Q%2BB1nDx8iUzwT6KcaPSSOQFDT1x5iq34Rl2XqTp4qL5TbiP%2FM3cWheYorvU9eDmirx%2BvMj6%2Bzi8u5fiCdvECf14E9jDg%3D"}, {"name": "pp_req_sign_2", "value": "tANoTqmqPg5V0fdrTvgZ69XgSLWJQdWW%2BTdcCXO3wSeB7ImJtFhtbGSempI5t3Yrxpfq8T9TH4dnuo%2FfE6wWQTvXmHuHj4bEmdJ4dZ2uK3cKyy%2F39EidXB8U0VI9fRFQC5JfNiP7GouPwYBrqBtEbZjK1ISkoCZykBnLMj604og%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "21"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"categoryId\":[\"10\"]}"}, "headersSize": 365, "bodySize": 21}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:04 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "67"}, {"name": "eagleeye-traceid", "value": "eaac16306117150813440736647d0001"}, {"name": "x-envoy-upstream-service-time", "value": "22"}], "content": {"size": 67, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbXSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 104, "bodySize": 67}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 238, "receive": 1}}, {"startedDateTime": "2024-05-07T19:29:03.101+08:00", "time": 249, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "T3Q15Ny181G5EGO5gw1a8cPOSq6KJO8xqXLZ9mcAwvNZ9RXOx87foezReNzrqvAs9Ams0pgWexWjBnvjIQkt021EIy3F++3R70jGjPbWZR4sj47JFmdShL3VlIRYM8FWwZqvtIY9gsiGsTYlq9dIWGzm0uhcbX4UAShKWqNwZfA="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.7.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"F6BC5D9086AB4D88B18FDCCAE76DDA8C\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"qi0vdGR6\",\"requestTime\":1715081343174,\"version\":\"1.0\"}"}, "headersSize": 158, "bodySize": 512}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:04 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "ad-center:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "28"}, {"name": "x-envoy-peer-metadata-id", "value": "sidecar~172.22.48.58~ad-center-daily-748c76dc87-nrq9g.daily~daily.svc.cluster.local"}, {"name": "x-envoy-peer-metadata", "value": "CjAKDkFQUF9DT05UQUlORVJTEh4aHGNvbnN1bC1hZ2VudCxhZC1jZW50ZXItZGFpbHkKGgoKQ0xVU1RFUl9JRBIMGgpLdWJlcm5ldGVzCh4KDElOU1RBTkNFX0lQUxIOGgwxNzIuMjIuNDguNTgKGQoNSVNUSU9fVkVSU0lPThIIGgYxLjE5LjAKoQMKBkxBQkVMUxKWAyqTAwoYCgNhcHASERoPYWQtY2VudGVyLWRhaWx5ChsKE2FybXNQaWxvdEF1dG9FbmFibGUSBBoCb24KKwoWYXJtc1BpbG90Q3JlYXRlQXBwTmFtZRIRGg9hZC1jZW50ZXItZGFpbHkKGgoFZ3JvdXASERoPYWQtY2VudGVyLWRhaWx5ChoKEm1zZVBpbG90QXV0b0VuYWJsZRIEGgJvbgo0ChVtc2VQaWxvdENyZWF0ZUFwcE5hbWUSGxoZYWQtY2VudGVyLWZyYW5rZnVydC1kYWlseQokChlzZWN1cml0eS5pc3Rpby5pby90bHNNb2RlEgcaBWlzdGlvCjQKH3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLW5hbWUSERoPYWQtY2VudGVyLWRhaWx5Ci8KI3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLXJldmlzaW9uEggaBmxhdGVzdAohChdzaWRlY2FyLmlzdGlvLmlvL2luamVjdBIGGgR0cnVlCg8KA3hkYxIIGgZzdGFibGUKGgoHTUVTSF9JRBIPGg1jbHVzdGVyLmxvY2FsCioKBE5BTUUSIhogYWQtY2VudGVyLWRhaWx5LTc0OGM3NmRjODctbnJxOWcKFAoJTkFNRVNQQUNFEgcaBWRhaWx5ClEKBU9XTkVSEkgaRmt1YmVybmV0ZXM6Ly9hcGlzL2FwcHMvdjEvbmFtZXNwYWNlcy9kYWlseS9kZXBsb3ltZW50cy9hZC1jZW50ZXItZGFpbHkKIgoNV09SS0xPQURfTkFNRRIRGg9hZC1jZW50ZXItZGFpbHk="}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 1013, "compression": 460, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImV4cGlyZWRUaW1lIjo2MDAwMCwibGlzdCI6W3siYWRJZCI6IkVFQjBFODU2NDg0NjRGNUFBNTU3MjNEMEE0MzhCQUJFIiwiYXBwbGljYXRpb25JZCI6IkI3OTVDNjVBMEVBMzQ3NkY5ODVBNDAyOUE4NTY2OTE2IiwiYWRTbG90SWQiOiJGNkJDNUQ5MDg2QUI0RDg4QjE4RkRDQ0FFNzZEREE4QyIsImltYWdlVXJsIjpudWxsLCJtdWx0aXBsZUltYWdlVXJsIjpudWxsLCJoNUltYWdlVXJsIjpudWxsLCJqdW1wVHlwZSI6IjA0IiwianVtcFBhcmFtcyI6IiIsInJlbGF0aXZlVXJsIjoiIiwiaW1hZ2VEaW1lbnRpb24iOm51bGwsImltYWdlRGltZW50aW9uTGlzdCI6bnVsbCwidmFsaWRTdGFydFRpbWUiOjE2OTg2MjA0MDAwMDAsInZhbGlkRW5kVGltZSI6MTg4ODE4MTk5OTAwMCwic2hvd1RpbWVzIjpudWxsLCJzaG93RGF0ZXMiOm51bGwsInNob3dUaW1lc0NvbmRpdGlvbiI6bnVsbCwiY2xpY2tGcmVxdWVuY3kiOm51bGwsImNsb3NlQ29udHJvbCI6bnVsbCwic2hvd0Nsb3NlQnV0dG9uIjpudWxsLCJjb3VudHJ5Q29kZSI6Ik5HIiwidGV4dENvbnRlbnQiOiJBaXJ0aW1lIEJpbGxzIFN1cGVyIExlYWd1ZSBpcyBhbHJlYWR5ISBDbGljayBoZXJlIHRvIGNsYWltIHJld2FyZHMgbm93fiIsInRleHRGb250U2l6ZSI6IjE0IiwidGV4dEZvbnRDb2xvciI6IiM2MzA2YjIiLCJ0ZXh0SWNvblVybCI6IiIsInRleHRTY3JvbGxTcGVlZCI6IjMwMDAiLCJiYWNrZ3JvdW5kQ29sb3IiOiIjRjlGNEZGIiwiYWRUeXBlIjoxLCJuYXRpdmVBZFRpdGxlIjpudWxsLCJuYXRpdmVBZENvbnRlbnQiOm51bGwsIm5hdGl2ZUFkUGhvdG8iOm51bGwsImltYWdlVXJsRGFyayI6bnVsbCwidGV4dEZvbnRDb2xvckRhcmsiOiIjMWJjMjg2IiwidGV4dEljb25VcmxEYXJrIjpudWxsLCJiYWNrZ3JvdW5kQ29sb3JEYXJrIjoiI2U0ZjdmMCIsIm5hdGl2ZUFkUGhvdG9EYXJrIjpudWxsLCJoNUltYWdlVXJsRGFyayI6bnVsbH1dfX0=", "encoding": "base64"}, "redirectURL": null, "headersSize": 1379, "bodySize": 553}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 248, "receive": 0}}, {"startedDateTime": "2024-05-07T19:29:03.119+08:00", "time": 246, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/cfront/airtime/buyProducts/v2", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/cfront/airtime/buyProducts/v2"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081343177"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "u2uqOaGnM9q1D5%2B%2BuCzy3Mr4jYBj563HfI3geZ4d7HwhWAzn%2BEXdiB6jz%2BhPqVk6jckQGUoq%2B%2F0LVQ8eUjdVrxR8f3FZxo1TgOk4QDWJbwvqxOhzt7CtlP31O8xmN4CJo0Ps%2FbivqPZYYj6f9VCGO9tlQpp2ieyNMu%2B%2BmihqgdE%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Tu<PERSON>, 07 May 2024 11:26:36 GMT"}], "queryString": [], "headersSize": 208, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:04 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150813441266681d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "28"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 1661, "compression": 1357, "mimeType": "application/json;charset=UTF-8", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 171, "bodySize": 304}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 244, "receive": 2}}, {"startedDateTime": "2024-05-07T19:29:03.12+08:00", "time": 241, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/payBills?location=04", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/payBills?location=04"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081343179"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "GlsfoYX9ZXTqWhlA25EdJkhbHavBj1isfctqrx8Dg%2FDmjOBIXzN7ikLHbT6lG%2BSbJeFI%2Bwblklbgt8Intmks4Kx5wy5QdE8v3LL05m8LZFwEAT1fKKMa1uO8r1QWIMBHccHS%2BVxVCk2xYm%2Byd6nXRXauWU%2ByT3R7clJkqID%2Fpzo%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Tu<PERSON>, 07 May 2024 11:26:34 GMT"}], "queryString": [{"name": "location", "value": "04"}], "headersSize": 228, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:04 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac16306117150813441286648d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "21"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 2491, "compression": 1888, "mimeType": "application/json;charset=UTF-8", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 171, "bodySize": 603}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 238, "receive": 3}}, {"startedDateTime": "2024-05-07T19:29:03.12+08:00", "time": 242, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/coupon/statistics", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/coupon/statistics"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081343185"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "R3VqElKmCa%2FjZyP7J0H00YA%2Bv%2FWWZOf2UVyCqqG5i%2FwMvJIMV%2FOEVfzdkK1OuNf%2FbKoZdF1zBNk6OVxYTMayAJEslQWGhwban4FmnWyCnAXp6OpG4dH1XIWKMWl%2By1uz33CTOLr10l7qRlI3pHDpUC5nGn44AWzTHiKW2cVGgd8%3D"}, {"name": "pp_req_sign_2", "value": "A7UD%2FB8lmJIl194doPKpSskslld6JCBt4TZP%2BaNBVAHDovzxTg34%2BcG6IE9SYDG32YVjbJicNUD29Gc8%2FbxQZb64doWvoJiU4qTafvDPzig4AiEv2URKvqzGsIewT5KlefTXyK0FZvPdrOaMYZhl9whea9USjtZDmvH5Kr0Ne8A%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "45"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"pageNum\":1,\"pageSize\":100,\"transType\":\"04\"}"}, "headersSize": 488, "bodySize": 45}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:04 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "157"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150813441306682d0001"}, {"name": "x-envoy-upstream-service-time", "value": "26"}], "content": {"size": 157, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7InRvdGFsIjowLCJ0b3RhbFBhZ2UiOjAsImN1clBhZ2UiOjEsInBhZ2VTaXplIjoxMDAsImRhdGFNYXAiOm51bGwsImV4dERhdGEiOm51bGwsImxpc3QiOltdfSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 105, "bodySize": 157}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 242, "receive": 0}}, {"startedDateTime": "2024-05-07T19:29:03.149+08:00", "time": 252, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "A9uoQsHyNPnLNy8b6ZgRDl6PIq+uOEGVHxUfVyETg9zqCzBbGaY4vuApaN/eNuat65X1AjkRT+hShpUmwYjrqso0RVh7QXpFM/pthOih5v/vwMQvG8PGVK4i8Kcf5xX5zg7O4ypXbixhawTJCzbPsc+n4qK283T40BBDw2a2F8s="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.7.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"98E7CAF944774A5AB456DD0CFABAD73C\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"Zgmj2Zo5\",\"requestTime\":1715081343248,\"version\":\"1.0\"}"}, "headersSize": 158, "bodySize": 512}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:04 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "ad-center:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "29"}, {"name": "x-envoy-peer-metadata-id", "value": "sidecar~172.22.48.58~ad-center-daily-748c76dc87-nrq9g.daily~daily.svc.cluster.local"}, {"name": "x-envoy-peer-metadata", "value": "CjAKDkFQUF9DT05UQUlORVJTEh4aHGNvbnN1bC1hZ2VudCxhZC1jZW50ZXItZGFpbHkKGgoKQ0xVU1RFUl9JRBIMGgpLdWJlcm5ldGVzCh4KDElOU1RBTkNFX0lQUxIOGgwxNzIuMjIuNDguNTgKGQoNSVNUSU9fVkVSU0lPThIIGgYxLjE5LjAKoQMKBkxBQkVMUxKWAyqTAwoYCgNhcHASERoPYWQtY2VudGVyLWRhaWx5ChsKE2FybXNQaWxvdEF1dG9FbmFibGUSBBoCb24KKwoWYXJtc1BpbG90Q3JlYXRlQXBwTmFtZRIRGg9hZC1jZW50ZXItZGFpbHkKGgoFZ3JvdXASERoPYWQtY2VudGVyLWRhaWx5ChoKEm1zZVBpbG90QXV0b0VuYWJsZRIEGgJvbgo0ChVtc2VQaWxvdENyZWF0ZUFwcE5hbWUSGxoZYWQtY2VudGVyLWZyYW5rZnVydC1kYWlseQokChlzZWN1cml0eS5pc3Rpby5pby90bHNNb2RlEgcaBWlzdGlvCjQKH3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLW5hbWUSERoPYWQtY2VudGVyLWRhaWx5Ci8KI3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLXJldmlzaW9uEggaBmxhdGVzdAohChdzaWRlY2FyLmlzdGlvLmlvL2luamVjdBIGGgR0cnVlCg8KA3hkYxIIGgZzdGFibGUKGgoHTUVTSF9JRBIPGg1jbHVzdGVyLmxvY2FsCioKBE5BTUUSIhogYWQtY2VudGVyLWRhaWx5LTc0OGM3NmRjODctbnJxOWcKFAoJTkFNRVNQQUNFEgcaBWRhaWx5ClEKBU9XTkVSEkgaRmt1YmVybmV0ZXM6Ly9hcGlzL2FwcHMvdjEvbmFtZXNwYWNlcy9kYWlseS9kZXBsb3ltZW50cy9hZC1jZW50ZXItZGFpbHkKIgoNV09SS0xPQURfTkFNRRIRGg9hZC1jZW50ZXItZGFpbHk="}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 1015, "compression": 468, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImV4cGlyZWRUaW1lIjo2MDAwMCwibGlzdCI6W3siYWRJZCI6IjNEMjZGRDJDQjEzMjRFMzFBMkZENEEyMEI3MzMyQUYxIiwiYXBwbGljYXRpb25JZCI6IkI3OTVDNjVBMEVBMzQ3NkY5ODVBNDAyOUE4NTY2OTE2IiwiYWRTbG90SWQiOiI5OEU3Q0FGOTQ0Nzc0QTVBQjQ1NkREMENGQUJBRDczQyIsImltYWdlVXJsIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vbWVyY2hhbnQvMTY2MjQzMzE0MDQ3ODEyLnBuZyIsIm11bHRpcGxlSW1hZ2VVcmwiOm51bGwsImg1SW1hZ2VVcmwiOm51bGwsImp1bXBUeXBlIjpudWxsLCJqdW1wUGFyYW1zIjoiIiwicmVsYXRpdmVVcmwiOiIiLCJpbWFnZURpbWVudGlvbiI6IjEwODAqNTEwIiwiaW1hZ2VEaW1lbnRpb25MaXN0IjoiW1wiMTA4MCo1MTBcIl0iLCJ2YWxpZFN0YXJ0VGltZSI6MTY2MjQxODgwMDAwMCwidmFsaWRFbmRUaW1lIjoxNzU5MzU5NTk5MDAwLCJzaG93VGltZXMiOm51bGwsInNob3dEYXRlcyI6bnVsbCwic2hvd1RpbWVzQ29uZGl0aW9uIjpudWxsLCJjbGlja0ZyZXF1ZW5jeSI6bnVsbCwiY2xvc2VDb250cm9sIjpudWxsLCJzaG93Q2xvc2VCdXR0b24iOjAsImNvdW50cnlDb2RlIjoiTkciLCJ0ZXh0Q29udGVudCI6bnVsbCwidGV4dEZvbnRTaXplIjpudWxsLCJ0ZXh0Rm9udENvbG9yIjoiIiwidGV4dEljb25VcmwiOm51bGwsInRleHRTY3JvbGxTcGVlZCI6bnVsbCwiYmFja2dyb3VuZENvbG9yIjoiIiwiYWRUeXBlIjoxLCJuYXRpdmVBZFRpdGxlIjpudWxsLCJuYXRpdmVBZENvbnRlbnQiOm51bGwsIm5hdGl2ZUFkUGhvdG8iOm51bGwsImltYWdlVXJsRGFyayI6bnVsbCwidGV4dEZvbnRDb2xvckRhcmsiOm51bGwsInRleHRJY29uVXJsRGFyayI6bnVsbCwiYmFja2dyb3VuZENvbG9yRGFyayI6bnVsbCwibmF0aXZlQWRQaG90b0RhcmsiOm51bGwsImg1SW1hZ2VVcmxEYXJrIjpudWxsfV19fQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 1379, "bodySize": 547}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 252, "receive": 0}}, {"startedDateTime": "2024-05-07T19:29:03.165+08:00", "time": 1598, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/recommendedItems?billerName=GLO", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/recommendedItems?billerName=GLO"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081343254"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "J%2BjA3sIoRld0qj6dzOxyKR9HomyNdcY%2B3WbodDA2B5vz5vBEZcz%2Fnncgxx4ojlqpMw0cRlMGyDzt%2FhLYRpXP%2FWc6807T%2FSh169ldnWeGOWeaBjUgfiejN5HhM9EcxK72HAXPKAXX9wkzi%2Fazj%2FzJwm%2F12vlIhBjPmehBB%2FQsevg%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Tu<PERSON>, 07 May 2024 11:26:37 GMT"}], "queryString": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "value": "GLO"}], "headersSize": 243, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:05 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150813441706683d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "1382"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 1420, "compression": 730, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7InRpdGxlIjoiUmVjb21tZW5kZWQgRGF0YSBPZmZlcnMiLCJpbWFnZSI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2ODkwNzM3Njk3NjcxMi1tb3JlLnBuZyIsImRhcmtJbWFnZSI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE3MDg2ODI0Mzg0MDgxMi1tb3JlLnBuZyIsImJnSW1hZ2UiOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNzA1MDQzNzk2Njc4MjAtJUU3JTg4JUIxJUU1JUJGJTgzLnBuZyIsImRhcmtCZ0ltYWdlIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTcwODY4MjQ0MzUxNjExLTE2NzExNzE5Njk1MDc5LTYzNTc0YzY5YmE1M2MyNmExZTZlZjMyNS5qcGVnIiwiaW9zTGluayI6InBhbG1wYXk6Ly9ob21lL2J1eWRhdGFidW5kbGUiLCJhbmRyb2lkTGluayI6Ii9xdWlja190ZWxsZXIvYnV5X2J1bmRsZV9uZyIsInJ1bGVUaXRsZSI6bnVsbCwicnVsZUNvbnRlbnQiOm51bGwsInJlY29tbWVuZGVkSXRlbXMiOlt7Iml0ZW1JZCI6InM5ODczNjEzODk5ODM1MCIsIml0ZW1OYW1lIjoiNDI1R0ItOTAgREFZUy3igqY1MCwwMDAiLCJwcmljZSI6NTAwMCwiZGVzY3JpcHRpb24iOm51bGwsInZhbGlkaXR5IjoiMjUgREFZUyIsInZhbGlkaXR5RXh0RGVzYyI6bnVsbCwiaXNCdXlBZ2FpbiI6ZmFsc2UsInZvbHVtZSI6Ijk5LjlHQiIsInZvbHVtZVVuaXQiOm51bGwsIml0ZW1DYXRlZ29yeSI6bnVsbCwiY29sb3IiOm51bGwsImltYWdlIjpudWxsLCJyZW1hcmsiOm51bGwsInZhbGlkaXR5RGF5IjpudWxsLCJ2b2x1bWVTaXplIjpudWxsLCJpdGVtT3JkZXIiOm51bGwsImdtdENyZWF0ZWQiOm51bGwsImJpbGxlcklkIjoiR0xPIiwiYmlsbGVyTmFtZSI6IkdMTyIsImJpbGxlckljb24iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1pbWFnZS1wcm9kLnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTY1NDA1ODY3NTAxNTE2LUdsb2JhY29tMS41LnBuZyIsImRpc2NvdW50UGFja2FnZUFtb3VudCI6bnVsbCwicmVtYWluaW5nU3RvY2siOm51bGwsImRpc2NvdW50UGFja2FnZUlkIjpudWxsLCJiYXRjaElkIjpudWxsLCJzdGFydFRpbWUiOm51bGwsInNhbGVDb3VudGRvd24iOm51bGwsImNvdW50ZG93blNob3dUaW1lIjpudWxsLCJpbml0aWFsU3RvY2siOm51bGwsImRpc2NvdW50SW5mbyI6bnVsbCwiaWQiOjUzMiwiaXRlbVR5cGUiOjAsImRpc3BsYXlPcmRlciI6MSwidGlwcyI6IuKCpjEgQ2FzaGJhY2sifV19fQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 172, "bodySize": 690}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 1594, "receive": 4}}, {"startedDateTime": "2024-05-07T19:29:03.329+08:00", "time": 222, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/dataBurialPoint/adBehavior", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/dataBurialPoint/adBehavior"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json"}, {"name": "countrycode", "value": "NG"}, {"name": "signature", "value": "W3bSvQ52EK06vYRsO3TGTXSdkiqPuiGZWKsXIddZN2GzdUOx+9DbzWOqHfxTmFxd639RJwHi3aSK1UKy8McYS9iVs5y+Yhksmy1iA1F9Qx8bQNowIarbBnsRAp8d++Fa8HQXPGZFysT9bqzm/I1HMINPjZ9ApH2yWavLhpPfa9s="}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "4882"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json", "text": "{\"bizInfo\":\"[{\\\"body\\\":{\\\"adId\\\":\\\"3DAFC601B3A3439A9DBEB9C721F9288D\\\",\\\"adSlotId\\\":\\\"01FFAE0D0B2447B68A767616A137F063\\\",\\\"adTime\\\":1715081343418,\\\"countryCode\\\":\\\"NG\\\",\\\"eventSubType\\\":\\\"\\\",\\\"eventType\\\":\\\"autoBegin\\\",\\\"num\\\":1,\\\"operator\\\":\\\"\\\",\\\"pointWindowX\\\":\\\"\\\",\\\"pointWindowY\\\":\\\"\\\",\\\"relatedId\\\":\\\"\\\",\\\"userId\\\":\\\"\\\",\\\"wifi\\\":true},\\\"head\\\":{\\\"SDKVersion\\\":\\\"1.0.36-SNAPSHOT\\\",\\\"appIdentifier\\\":\\\"com.transsnet.palmpay\\\",\\\"appVersion\\\":\\\"5.7.0\\\",\\\"appVersionCode\\\":604181603,\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"behaviorId\\\":\\\"B795C65A0EA3476F985A4029A85669161715081343418\\\",\\\"createTime\\\":1715081343423,\\\"eventTime\\\":1715081343423,\\\"gaid\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"imei\\\":\\\"\\\",\\\"ip\\\":\\\"*************\\\",\\\"macAddress\\\":\\\"please open wifi\\\",\\\"operSystem\\\":\\\"Android\\\"}},{\\\"body\\\":{\\\"adId\\\":\\\"474DDD975A5C4F61B8A8B13A16DB45F4\\\",\\\"adSlotId\\\":\\\"0CDF659D244E4A65B86D0BD9EB2D6879\\\",\\\"adTime\\\":1715081335148,\\\"countryCode\\\":\\\"NG\\\",\\\"eventSubType\\\":\\\"\\\",\\\"eventType\\\":\\\"autoBegin\\\",\\\"num\\\":1,\\\"operator\\\":\\\"\\\",\\\"pointWindowX\\\":\\\"\\\",\\\"pointWindowY\\\":\\\"\\\",\\\"relatedId\\\":\\\"\\\",\\\"userId\\\":\\\"\\\",\\\"wifi\\\":true},\\\"head\\\":{\\\"SDKVersion\\\":\\\"1.0.36-SNAPSHOT\\\",\\\"appIdentifier\\\":\\\"com.transsnet.palmpay\\\",\\\"appVersion\\\":\\\"5.7.0\\\",\\\"appVersionCode\\\":604181603,\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"behaviorId\\\":\\\"B795C65A0EA3476F985A4029A85669161715081335148\\\",\\\"createTime\\\":1715081335152,\\\"eventTime\\\":1715081335152,\\\"gaid\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"imei\\\":\\\"\\\",\\\"ip\\\":\\\"*************\\\",\\\"macAddress\\\":\\\"please open wifi\\\",\\\"operSystem\\\":\\\"Android\\\"}},{\\\"body\\\":{\\\"adId\\\":\\\"10DAD3F8FA4948418C6F88527FF8AE86\\\",\\\"adSlotId\\\":\\\"74E199B793574A2BA35FAA1C82210C84\\\",\\\"adTime\\\":1715081331509,\\\"countryCode\\\":\\\"NG\\\",\\\"eventSubType\\\":\\\"\\\",\\\"eventType\\\":\\\"autoBegin\\\",\\\"num\\\":1,\\\"operator\\\":\\\"\\\",\\\"pointWindowX\\\":\\\"\\\",\\\"pointWindowY\\\":\\\"\\\",\\\"relatedId\\\":\\\"\\\",\\\"userId\\\":\\\"\\\",\\\"wifi\\\":true},\\\"head\\\":{\\\"SDKVersion\\\":\\\"1.0.36-SNAPSHOT\\\",\\\"appIdentifier\\\":\\\"com.transsnet.palmpay\\\",\\\"appVersion\\\":\\\"5.7.0\\\",\\\"appVersionCode\\\":604181603,\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"behaviorId\\\":\\\"B795C65A0EA3476F985A4029A85669161715081331508\\\",\\\"createTime\\\":1715081331514,\\\"eventTime\\\":1715081331514,\\\"gaid\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"imei\\\":\\\"\\\",\\\"ip\\\":\\\"*************\\\",\\\"macAddress\\\":\\\"please open wifi\\\",\\\"operSystem\\\":\\\"Android\\\"}},{\\\"body\\\":{\\\"adId\\\":\\\"474DDD975A5C4F61B8A8B13A16DB45F4\\\",\\\"adSlotId\\\":\\\"0CDF659D244E4A65B86D0BD9EB2D6879\\\",\\\"adTime\\\":1715081330943,\\\"countryCode\\\":\\\"NG\\\",\\\"eventSubType\\\":\\\"\\\",\\\"eventType\\\":\\\"autoBegin\\\",\\\"num\\\":1,\\\"operator\\\":\\\"\\\",\\\"pointWindowX\\\":\\\"\\\",\\\"pointWindowY\\\":\\\"\\\",\\\"relatedId\\\":\\\"\\\",\\\"userId\\\":\\\"\\\",\\\"wifi\\\":true},\\\"head\\\":{\\\"SDKVersion\\\":\\\"1.0.36-SNAPSHOT\\\",\\\"appIdentifier\\\":\\\"com.transsnet.palmpay\\\",\\\"appVersion\\\":\\\"5.7.0\\\",\\\"appVersionCode\\\":604181603,\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"behaviorId\\\":\\\"B795C65A0EA3476F985A4029A85669161715081330943\\\",\\\"createTime\\\":1715081330947,\\\"eventTime\\\":1715081330947,\\\"gaid\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"imei\\\":\\\"\\\",\\\"ip\\\":\\\"*************\\\",\\\"macAddress\\\":\\\"please open wifi\\\",\\\"operSystem\\\":\\\"Android\\\"}},{\\\"body\\\":{\\\"adId\\\":\\\"6C6A0A5AF92F4BE0B8A1F0DE158F9664\\\",\\\"adSlotId\\\":\\\"5F7CAA79D3F247AD986B94D3807E6B97\\\",\\\"adTime\\\":1715081330932,\\\"countryCode\\\":\\\"NG\\\",\\\"eventSubType\\\":\\\"\\\",\\\"eventType\\\":\\\"autoBegin\\\",\\\"num\\\":1,\\\"operator\\\":\\\"\\\",\\\"pointWindowX\\\":\\\"\\\",\\\"pointWindowY\\\":\\\"\\\",\\\"relatedId\\\":\\\"\\\",\\\"userId\\\":\\\"\\\",\\\"wifi\\\":true},\\\"head\\\":{\\\"SDKVersion\\\":\\\"1.0.36-SNAPSHOT\\\",\\\"appIdentifier\\\":\\\"com.transsnet.palmpay\\\",\\\"appVersion\\\":\\\"5.7.0\\\",\\\"appVersionCode\\\":604181603,\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"behaviorId\\\":\\\"B795C65A0EA3476F985A4029A85669161715081330932\\\",\\\"createTime\\\":1715081330937,\\\"eventTime\\\":1715081330937,\\\"gaid\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"imei\\\":\\\"\\\",\\\"ip\\\":\\\"*************\\\",\\\"macAddress\\\":\\\"please open wifi\\\",\\\"operSystem\\\":\\\"Android\\\"}},{\\\"body\\\":{\\\"adId\\\":\\\"3665C02D8F9549E788C0AF8AD20B99FB\\\",\\\"adSlotId\\\":\\\"E1F2570CEEFD47C1A3B8ACDC0DDB9C79\\\",\\\"adTime\\\":1715081293017,\\\"countryCode\\\":\\\"NG\\\",\\\"eventSubType\\\":\\\"\\\",\\\"eventType\\\":\\\"autoBegin\\\",\\\"num\\\":1,\\\"operator\\\":\\\"\\\",\\\"pointWindowX\\\":\\\"0\\\",\\\"pointWindowY\\\":\\\"0\\\",\\\"userId\\\":\\\"\\\",\\\"wifi\\\":true},\\\"head\\\":{\\\"SDKVersion\\\":\\\"1.0.36-SNAPSHOT\\\",\\\"appIdentifier\\\":\\\"com.transsnet.palmpay\\\",\\\"appVersion\\\":\\\"5.7.0\\\",\\\"appVersionCode\\\":604181603,\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"behaviorId\\\":\\\"B795C65A0EA3476F985A4029A85669161715081293017\\\",\\\"createTime\\\":1715081330398,\\\"eventTime\\\":1715081330398,\\\"gaid\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"imei\\\":\\\"\\\",\\\"ip\\\":\\\"*************\\\",\\\"macAddress\\\":\\\"please open wifi\\\",\\\"operSystem\\\":\\\"Android\\\"}}]\",\"nonceStr\":\"Ee17LCUY\",\"requestTime\":1715081343428,\"version\":\"1.0.36-SNAPSHOT\"}"}, "headersSize": 195, "bodySize": 4882}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:04 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "analysis:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "2"}, {"name": "x-envoy-peer-metadata-id", "value": "sidecar~172.22.39.139~analysis-daily-7c8fdc4cd4-spdhh.daily~daily.svc.cluster.local"}, {"name": "x-envoy-peer-metadata", "value": "Ci8KDkFQUF9DT05UQUlORVJTEh0aG2NvbnN1bC1hZ2VudCxhbmFseXNpcy1kYWlseQoaCgpDTFVTVEVSX0lEEgwaCkt1YmVybmV0ZXMKHwoMSU5TVEFOQ0VfSVBTEg8aDTE3Mi4yMi4zOS4xMzkKGQoNSVNUSU9fVkVSU0lPThIIGgYxLjE5LjAKnAMKBkxBQkVMUxKRAyqOAwoXCgNhcHASEBoOYW5hbHlzaXMtZGFpbHkKGwoTYXJtc1BpbG90QXV0b0VuYWJsZRIEGgJvbgoqChZhcm1zUGlsb3RDcmVhdGVBcHBOYW1lEhAaDmFuYWx5c2lzLWRhaWx5ChkKBWdyb3VwEhAaDmFuYWx5c2lzLWRhaWx5ChoKEm1zZVBpbG90QXV0b0VuYWJsZRIEGgJvbgozChVtc2VQaWxvdENyZWF0ZUFwcE5hbWUSGhoYYW5hbHlzaXMtZnJhbmtmdXJ0LWRhaWx5CiQKGXNlY3VyaXR5LmlzdGlvLmlvL3Rsc01vZGUSBxoFaXN0aW8KMwofc2VydmljZS5pc3Rpby5pby9jYW5vbmljYWwtbmFtZRIQGg5hbmFseXNpcy1kYWlseQovCiNzZXJ2aWNlLmlzdGlvLmlvL2Nhbm9uaWNhbC1yZXZpc2lvbhIIGgZsYXRlc3QKIQoXc2lkZWNhci5pc3Rpby5pby9pbmplY3QSBhoEdHJ1ZQoPCgN4ZGMSCBoGc3RhYmxlChoKB01FU0hfSUQSDxoNY2x1c3Rlci5sb2NhbAopCgROQU1FEiEaH2FuYWx5c2lzLWRhaWx5LTdjOGZkYzRjZDQtc3BkaGgKFAoJTkFNRVNQQUNFEgcaBWRhaWx5ClAKBU9XTkVSEkcaRWt1YmVybmV0ZXM6Ly9hcGlzL2FwcHMvdjEvbmFtZXNwYWNlcy9kYWlseS9kZXBsb3ltZW50cy9hbmFseXNpcy1kYWlseQohCg1XT1JLTE9BRF9OQU1FEhAaDmFuYWx5c2lzLWRhaWx5"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 55, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpudWxsfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 1360, "bodySize": 70}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 221, "receive": 1}}, {"startedDateTime": "2024-05-07T19:29:03.585+08:00", "time": 223, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/dataBurialPoint/adBehavior", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/dataBurialPoint/adBehavior"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json"}, {"name": "countrycode", "value": "NG"}, {"name": "signature", "value": "M/Oi0iDKNkmFeNJLcV9yfIi58M386FVtoT75Fdr68AGhoIu24o3ARjGEKBi0sLP463sGcKaEtIrhpZimnkM7jR7zmXQwoWtPCz0PUdqeJRwnwX+ydnEY6IIc2XcTGd+cjVXGDtssoYxqMsxPXg/Jom4jPtyKjaLgcnTeCqTml8g="}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "3387"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json", "text": "{\"bizInfo\":\"[{\\\"body\\\":{\\\"adId\\\":\\\"3D26FD2CB1324E31A2FD4A20B7332AF1\\\",\\\"adSlotId\\\":\\\"98E7CAF944774A5AB456DD0CFABAD73C\\\",\\\"adTime\\\":1715081343553,\\\"countryCode\\\":\\\"NG\\\",\\\"eventSubType\\\":\\\"\\\",\\\"eventType\\\":\\\"autoBegin\\\",\\\"num\\\":1,\\\"operator\\\":\\\"\\\",\\\"pointWindowX\\\":\\\"\\\",\\\"pointWindowY\\\":\\\"\\\",\\\"relatedId\\\":\\\"B795C65A0EA3476F985A4029A85669161715081343537\\\",\\\"userId\\\":\\\"\\\",\\\"wifi\\\":true},\\\"head\\\":{\\\"SDKVersion\\\":\\\"1.0.36-SNAPSHOT\\\",\\\"appIdentifier\\\":\\\"com.transsnet.palmpay\\\",\\\"appVersion\\\":\\\"5.7.0\\\",\\\"appVersionCode\\\":604181603,\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"behaviorId\\\":\\\"B795C65A0EA3476F985A4029A85669161715081343553\\\",\\\"createTime\\\":1715081343558,\\\"eventTime\\\":1715081343558,\\\"gaid\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"imei\\\":\\\"\\\",\\\"ip\\\":\\\"*************\\\",\\\"macAddress\\\":\\\"please open wifi\\\",\\\"operSystem\\\":\\\"Android\\\"}},{\\\"body\\\":{\\\"adId\\\":\\\"EEB0E85648464F5AA55723D0A438BABE\\\",\\\"adSlotId\\\":\\\"F6BC5D9086AB4D88B18FDCCAE76DDA8C\\\",\\\"adTime\\\":1715081343537,\\\"countryCode\\\":\\\"NG\\\",\\\"eventSubType\\\":\\\"\\\",\\\"eventType\\\":\\\"autoBegin\\\",\\\"num\\\":1,\\\"operator\\\":\\\"\\\",\\\"pointWindowX\\\":\\\"\\\",\\\"pointWindowY\\\":\\\"\\\",\\\"relatedId\\\":\\\"\\\",\\\"userId\\\":\\\"\\\",\\\"wifi\\\":true},\\\"head\\\":{\\\"SDKVersion\\\":\\\"1.0.36-SNAPSHOT\\\",\\\"appIdentifier\\\":\\\"com.transsnet.palmpay\\\",\\\"appVersion\\\":\\\"5.7.0\\\",\\\"appVersionCode\\\":604181603,\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"behaviorId\\\":\\\"B795C65A0EA3476F985A4029A85669161715081343537\\\",\\\"createTime\\\":1715081343543,\\\"eventTime\\\":1715081343543,\\\"gaid\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"imei\\\":\\\"\\\",\\\"ip\\\":\\\"*************\\\",\\\"macAddress\\\":\\\"please open wifi\\\",\\\"operSystem\\\":\\\"Android\\\"}},{\\\"body\\\":{\\\"adId\\\":\\\"3D26FD2CB1324E31A2FD4A20B7332AF1\\\",\\\"adSlotId\\\":\\\"98E7CAF944774A5AB456DD0CFABAD73C\\\",\\\"adTime\\\":1715081343537,\\\"countryCode\\\":\\\"NG\\\",\\\"eventSubType\\\":\\\"\\\",\\\"eventType\\\":\\\"autoBegin\\\",\\\"num\\\":1,\\\"operator\\\":\\\"\\\",\\\"pointWindowX\\\":\\\"\\\",\\\"pointWindowY\\\":\\\"\\\",\\\"relatedId\\\":\\\"\\\",\\\"userId\\\":\\\"\\\",\\\"wifi\\\":true},\\\"head\\\":{\\\"SDKVersion\\\":\\\"1.0.36-SNAPSHOT\\\",\\\"appIdentifier\\\":\\\"com.transsnet.palmpay\\\",\\\"appVersion\\\":\\\"5.7.0\\\",\\\"appVersionCode\\\":604181603,\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"behaviorId\\\":\\\"B795C65A0EA3476F985A4029A85669161715081343537\\\",\\\"createTime\\\":1715081343541,\\\"eventTime\\\":1715081343541,\\\"gaid\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"imei\\\":\\\"\\\",\\\"ip\\\":\\\"*************\\\",\\\"macAddress\\\":\\\"please open wifi\\\",\\\"operSystem\\\":\\\"Android\\\"}},{\\\"body\\\":{\\\"adId\\\":\\\"3DAFC601B3A3439A9DBEB9C721F9288D\\\",\\\"adSlotId\\\":\\\"01FFAE0D0B2447B68A767616A137F063\\\",\\\"adTime\\\":1715081343437,\\\"countryCode\\\":\\\"NG\\\",\\\"eventSubType\\\":\\\"\\\",\\\"eventType\\\":\\\"autoBegin\\\",\\\"num\\\":1,\\\"operator\\\":\\\"\\\",\\\"pointWindowX\\\":\\\"\\\",\\\"pointWindowY\\\":\\\"\\\",\\\"relatedId\\\":\\\"B795C65A0EA3476F985A4029A85669161715081343418\\\",\\\"userId\\\":\\\"\\\",\\\"wifi\\\":true},\\\"head\\\":{\\\"SDKVersion\\\":\\\"1.0.36-SNAPSHOT\\\",\\\"appIdentifier\\\":\\\"com.transsnet.palmpay\\\",\\\"appVersion\\\":\\\"5.7.0\\\",\\\"appVersionCode\\\":604181603,\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"behaviorId\\\":\\\"B795C65A0EA3476F985A4029A85669161715081343437\\\",\\\"createTime\\\":1715081343446,\\\"eventTime\\\":1715081343446,\\\"gaid\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"imei\\\":\\\"\\\",\\\"ip\\\":\\\"*************\\\",\\\"macAddress\\\":\\\"please open wifi\\\",\\\"operSystem\\\":\\\"Android\\\"}}]\",\"nonceStr\":\"nedEEh7A\",\"requestTime\":1715081343689,\"version\":\"1.0.36-SNAPSHOT\"}"}, "headersSize": 156, "bodySize": 3387}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:04 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "analysis:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "1"}, {"name": "x-envoy-peer-metadata-id", "value": "sidecar~172.22.39.139~analysis-daily-7c8fdc4cd4-spdhh.daily~daily.svc.cluster.local"}, {"name": "x-envoy-peer-metadata", "value": "Ci8KDkFQUF9DT05UQUlORVJTEh0aG2NvbnN1bC1hZ2VudCxhbmFseXNpcy1kYWlseQoaCgpDTFVTVEVSX0lEEgwaCkt1YmVybmV0ZXMKHwoMSU5TVEFOQ0VfSVBTEg8aDTE3Mi4yMi4zOS4xMzkKGQoNSVNUSU9fVkVSU0lPThIIGgYxLjE5LjAKnAMKBkxBQkVMUxKRAyqOAwoXCgNhcHASEBoOYW5hbHlzaXMtZGFpbHkKGwoTYXJtc1BpbG90QXV0b0VuYWJsZRIEGgJvbgoqChZhcm1zUGlsb3RDcmVhdGVBcHBOYW1lEhAaDmFuYWx5c2lzLWRhaWx5ChkKBWdyb3VwEhAaDmFuYWx5c2lzLWRhaWx5ChoKEm1zZVBpbG90QXV0b0VuYWJsZRIEGgJvbgozChVtc2VQaWxvdENyZWF0ZUFwcE5hbWUSGhoYYW5hbHlzaXMtZnJhbmtmdXJ0LWRhaWx5CiQKGXNlY3VyaXR5LmlzdGlvLmlvL3Rsc01vZGUSBxoFaXN0aW8KMwofc2VydmljZS5pc3Rpby5pby9jYW5vbmljYWwtbmFtZRIQGg5hbmFseXNpcy1kYWlseQovCiNzZXJ2aWNlLmlzdGlvLmlvL2Nhbm9uaWNhbC1yZXZpc2lvbhIIGgZsYXRlc3QKIQoXc2lkZWNhci5pc3Rpby5pby9pbmplY3QSBhoEdHJ1ZQoPCgN4ZGMSCBoGc3RhYmxlChoKB01FU0hfSUQSDxoNY2x1c3Rlci5sb2NhbAopCgROQU1FEiEaH2FuYWx5c2lzLWRhaWx5LTdjOGZkYzRjZDQtc3BkaGgKFAoJTkFNRVNQQUNFEgcaBWRhaWx5ClAKBU9XTkVSEkcaRWt1YmVybmV0ZXM6Ly9hcGlzL2FwcHMvdjEvbmFtZXNwYWNlcy9kYWlseS9kZXBsb3ltZW50cy9hbmFseXNpcy1kYWlseQohCg1XT1JLTE9BRF9OQU1FEhAaDmFuYWx5c2lzLWRhaWx5"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 55, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpudWxsfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 1360, "bodySize": 70}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 222, "receive": 0}}, {"startedDateTime": "2024-05-07T19:29:04.473+08:00", "time": 429, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=AirtimeHomepage", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=AirtimeHomepage"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715081344583"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "OCph0JLi91Uad1L7fvKx2Pwery6pnaZJ9DGayob%2Be%2F1Ll%2BmZlOYM8B9N3pWsPgtk0t6zDpEVnGyzOjsYj%2FPdCt9eis3rEb7tiJM9Ql2QRvTyG62UfsUVhMD0B4HdAhws4iajqMuab6dUTlW2LYl8Jey%2Fk1MXD3C%2BdOkMEKoEi4E%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Tu<PERSON>, 07 May 2024 11:29:04 GMT"}], "queryString": [{"name": "showLocation", "value": "AirtimeHomepage"}], "headersSize": 234, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tu<PERSON>, 07 May 2024 11:29:05 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "77"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150813454806686d0001"}, {"name": "x-envoy-upstream-service-time", "value": "215"}], "content": {"size": 77, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjEwMDAwMDEzIiwicmVzcE1zZyI6IkFjdGl2aXR5IGVuZGVkIiwiZGF0YSI6bnVsbCwic3RhdHVzIjpmYWxzZX0=", "encoding": "base64"}, "redirectURL": null, "headersSize": 104, "bodySize": 77}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 428, "receive": 0}}]}}