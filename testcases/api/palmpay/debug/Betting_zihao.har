{"log": {"version": "1.2", "creator": {"name": "<PERSON>", "version": "4.6.4"}, "entries": [{"startedDateTime": "2024-05-07T19:44:17.242+08:00", "time": 253, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/online-agent-product/agent/status?categoryId=8", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/online-agent-product/agent/status?categoryId=8"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715082257351"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "IeD0UGAF%2B%2FVj1s2Tq%2Fm%2BmDNdZWhHZT2GJLiYWidD%2BYFun87Hrxnr6bVLDP359X1FPp6CFc8%2BLyofyDvss4J%2B4BLOXT79bcJ2NY6piH618RAcGgi5hLwiUQbkIsQt%2BvbbHA7XX0gOLB6dbQEAm2PVYt98wd4d10NoWr4nYmL0FAk%3D"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [{"name": "categoryId", "value": "8"}], "headersSize": 215, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:18 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "559"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150822582517678d0001"}, {"name": "x-envoy-upstream-service-time", "value": "38"}], "content": {"size": 559, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7Im1lbWJlcklkIjoiQTEyRDc5RjgxNkMxNDRCNTkzNzAzRUNGRkU1M0YzMTkiLCJpc1doaXRlTGlzdE1lbWJlciI6dHJ1ZSwiYWdlbnRMZXZlbCI6MSwibGV2ZWxEZXNjIjoiWW91ciBhZ2VudCBsZXZlbCB3aWxsIGJlIG9uIEZpcnN0IExldmVsIHdoZW4geW91ciB0cmFuc2FjdGlvbiBhbW91bnQgb2YgQWlydGltZeOAgURhdGHjgIFCZXR0aW5n44CBRWxlY3RyaWNpdHnjgIFUViBpcyBtb3JlIHRoYW4gTjEyMDAuXG5UaGUgY29tbWlzc2lvbiByYXRlIG9mIEZpcnN0IExldmVsIGFzIGZvbGxvd++8mlxuQWlydGltZSB1cCB0byAxJe+8m1xuRGF0YSB1cCB0byAxJe+8m1xuQmV0dGluZyB1cCB0byAxJe+8m1xuRWxlY3RyaWNpdHkgdXAgdG8gMSXvvJtcblRWIHVwIHRvIDElIiwiYWdlbnRTdGF0dXMiOjAsImFnZW50VHlwZSI6MSwibG9nbyI6bnVsbCwidGlwcyI6IkJlY29tZSBQYWxtUGF5IFBsdXMsIGVhcm4gY29tbWlzc2lvbi4iLCJhZ2VudFN0YXRlIjoiTGFnb3MifSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 105, "bodySize": 559}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 252, "receive": 0}}, {"startedDateTime": "2024-05-07T19:44:17.373+08:00", "time": 2414, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/cfront/betting/homepage", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/cfront/betting/homepage"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715082257480"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "o%2BqvP49EO0i0eQJuqJRWoahh91PphfYfmqypjnGmGZDyLFfX3kAq70sAtePnlvBH6xFkXt%2BBK%2FimC9JoloDPEzLbB5nD1iW4rYbhIcC3%2F%2Bk2aB59mr7qgrpxQ55FyLAA3yRZ1oBhWTtMW84dQXzZ%2FCajxUL2W5B9KzGNei%2FVzqo%3D"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "headersSize": 198, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:20 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150822583817679d0001"}, {"name": "x-application-context", "value": "betting-product:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "2193"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 4053, "compression": 3007, "mimeType": "application/json;charset=UTF-8", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 178, "bodySize": 1046}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 2406, "receive": 7}}, {"startedDateTime": "2024-05-07T19:44:17.405+08:00", "time": 248, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "gSOQ6OiynoIcUs6NHjEaq+Gay0AiZ74GIwy/go+tOFipEqEO3VYEwmACPfhLsZBeWk0+ntpB2RMyGtZrVSiu96AFdncouTDyks0VdINSER8hEAzDZ4JD5MWaHmmgV6ymlPTVK/wZVOy7/t9ihl9I5Mt99FStGlfJ8u2uZFWEXzY="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.7.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"8F807507C4FF42DEA3D464C69A2427B9\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"1eBQq8ae\",\"requestTime\":1715082257512,\"version\":\"1.0\"}"}, "headersSize": 158, "bodySize": 512}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:18 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "ad-center:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "23"}, {"name": "x-envoy-peer-metadata-id", "value": "sidecar~172.22.48.58~ad-center-daily-748c76dc87-nrq9g.daily~daily.svc.cluster.local"}, {"name": "x-envoy-peer-metadata", "value": "CjAKDkFQUF9DT05UQUlORVJTEh4aHGNvbnN1bC1hZ2VudCxhZC1jZW50ZXItZGFpbHkKGgoKQ0xVU1RFUl9JRBIMGgpLdWJlcm5ldGVzCh4KDElOU1RBTkNFX0lQUxIOGgwxNzIuMjIuNDguNTgKGQoNSVNUSU9fVkVSU0lPThIIGgYxLjE5LjAKoQMKBkxBQkVMUxKWAyqTAwoYCgNhcHASERoPYWQtY2VudGVyLWRhaWx5ChsKE2FybXNQaWxvdEF1dG9FbmFibGUSBBoCb24KKwoWYXJtc1BpbG90Q3JlYXRlQXBwTmFtZRIRGg9hZC1jZW50ZXItZGFpbHkKGgoFZ3JvdXASERoPYWQtY2VudGVyLWRhaWx5ChoKEm1zZVBpbG90QXV0b0VuYWJsZRIEGgJvbgo0ChVtc2VQaWxvdENyZWF0ZUFwcE5hbWUSGxoZYWQtY2VudGVyLWZyYW5rZnVydC1kYWlseQokChlzZWN1cml0eS5pc3Rpby5pby90bHNNb2RlEgcaBWlzdGlvCjQKH3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLW5hbWUSERoPYWQtY2VudGVyLWRhaWx5Ci8KI3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLXJldmlzaW9uEggaBmxhdGVzdAohChdzaWRlY2FyLmlzdGlvLmlvL2luamVjdBIGGgR0cnVlCg8KA3hkYxIIGgZzdGFibGUKGgoHTUVTSF9JRBIPGg1jbHVzdGVyLmxvY2FsCioKBE5BTUUSIhogYWQtY2VudGVyLWRhaWx5LTc0OGM3NmRjODctbnJxOWcKFAoJTkFNRVNQQUNFEgcaBWRhaWx5ClEKBU9XTkVSEkgaRmt1YmVybmV0ZXM6Ly9hcGlzL2FwcHMvdjEvbmFtZXNwYWNlcy9kYWlseS9kZXBsb3ltZW50cy9hZC1jZW50ZXItZGFpbHkKIgoNV09SS0xPQURfTkFNRRIRGg9hZC1jZW50ZXItZGFpbHk="}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 84, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImV4cGlyZWRUaW1lIjo2MDAwMCwibGlzdCI6bnVsbH19", "encoding": "base64"}, "redirectURL": null, "headersSize": 1379, "bodySize": 96}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 246, "receive": 1}}, {"startedDateTime": "2024-05-07T19:44:17.427+08:00", "time": 245, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "S/RaedBslIuLQ7USYshiQYGFJYitPXAdvCqYSh4oF0916fyOdwqfxvWeQXzHKp81qqUYZoNGdLrqo9c7ZExf3zcRTZ8JZsOBYrPl6fCm8AUyJVqxpMybFoP9eacRTIGvClYb+aPTXh7P5B0vsvVigtFnEKSmb7HGelOvrUp7SZg="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.7.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"D78E194720F0418B9DB2F4EA5D8EBD03\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"6UQD7W3f\",\"requestTime\":1715082257532,\"version\":\"1.0\"}"}, "headersSize": 157, "bodySize": 512}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:18 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "ad-center:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "21"}, {"name": "x-envoy-peer-metadata-id", "value": "sidecar~172.22.48.58~ad-center-daily-748c76dc87-nrq9g.daily~daily.svc.cluster.local"}, {"name": "x-envoy-peer-metadata", "value": "CjAKDkFQUF9DT05UQUlORVJTEh4aHGNvbnN1bC1hZ2VudCxhZC1jZW50ZXItZGFpbHkKGgoKQ0xVU1RFUl9JRBIMGgpLdWJlcm5ldGVzCh4KDElOU1RBTkNFX0lQUxIOGgwxNzIuMjIuNDguNTgKGQoNSVNUSU9fVkVSU0lPThIIGgYxLjE5LjAKoQMKBkxBQkVMUxKWAyqTAwoYCgNhcHASERoPYWQtY2VudGVyLWRhaWx5ChsKE2FybXNQaWxvdEF1dG9FbmFibGUSBBoCb24KKwoWYXJtc1BpbG90Q3JlYXRlQXBwTmFtZRIRGg9hZC1jZW50ZXItZGFpbHkKGgoFZ3JvdXASERoPYWQtY2VudGVyLWRhaWx5ChoKEm1zZVBpbG90QXV0b0VuYWJsZRIEGgJvbgo0ChVtc2VQaWxvdENyZWF0ZUFwcE5hbWUSGxoZYWQtY2VudGVyLWZyYW5rZnVydC1kYWlseQokChlzZWN1cml0eS5pc3Rpby5pby90bHNNb2RlEgcaBWlzdGlvCjQKH3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLW5hbWUSERoPYWQtY2VudGVyLWRhaWx5Ci8KI3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLXJldmlzaW9uEggaBmxhdGVzdAohChdzaWRlY2FyLmlzdGlvLmlvL2luamVjdBIGGgR0cnVlCg8KA3hkYxIIGgZzdGFibGUKGgoHTUVTSF9JRBIPGg1jbHVzdGVyLmxvY2FsCioKBE5BTUUSIhogYWQtY2VudGVyLWRhaWx5LTc0OGM3NmRjODctbnJxOWcKFAoJTkFNRVNQQUNFEgcaBWRhaWx5ClEKBU9XTkVSEkgaRmt1YmVybmV0ZXM6Ly9hcGlzL2FwcHMvdjEvbmFtZXNwYWNlcy9kYWlseS9kZXBsb3ltZW50cy9hZC1jZW50ZXItZGFpbHkKIgoNV09SS0xPQURfTkFNRRIRGg9hZC1jZW50ZXItZGFpbHk="}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 84, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImV4cGlyZWRUaW1lIjo2MDAwMCwibGlzdCI6bnVsbH19", "encoding": "base64"}, "redirectURL": null, "headersSize": 1379, "bodySize": 96}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 243, "receive": 1}}, {"startedDateTime": "2024-05-07T19:44:17.427+08:00", "time": 255, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "Ivt6JPsALxgUYYmkF9Nsr1wMaMxK3lVYJFj3PbBIqyLbf53OCqjvoZ+cuyoUcrCjBSeX6Z4zKjVzUHJZSn+ktTJiS41JrZPe+QQx5q4dOG2Ah7nvP60MCE3kvV1R3dV70oMG3NwTrd6BVVC5EHeQRVSYt46SBT/S+bbSW0yDu6s="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.7.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"A2514D207D424613B585431165DF038B\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"jZqTDlxV\",\"requestTime\":1715082257533,\"version\":\"1.0\"}"}, "headersSize": 160, "bodySize": 512}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:18 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "ad-center:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "31"}, {"name": "x-envoy-peer-metadata-id", "value": "sidecar~172.22.48.58~ad-center-daily-748c76dc87-nrq9g.daily~daily.svc.cluster.local"}, {"name": "x-envoy-peer-metadata", "value": "CjAKDkFQUF9DT05UQUlORVJTEh4aHGNvbnN1bC1hZ2VudCxhZC1jZW50ZXItZGFpbHkKGgoKQ0xVU1RFUl9JRBIMGgpLdWJlcm5ldGVzCh4KDElOU1RBTkNFX0lQUxIOGgwxNzIuMjIuNDguNTgKGQoNSVNUSU9fVkVSU0lPThIIGgYxLjE5LjAKoQMKBkxBQkVMUxKWAyqTAwoYCgNhcHASERoPYWQtY2VudGVyLWRhaWx5ChsKE2FybXNQaWxvdEF1dG9FbmFibGUSBBoCb24KKwoWYXJtc1BpbG90Q3JlYXRlQXBwTmFtZRIRGg9hZC1jZW50ZXItZGFpbHkKGgoFZ3JvdXASERoPYWQtY2VudGVyLWRhaWx5ChoKEm1zZVBpbG90QXV0b0VuYWJsZRIEGgJvbgo0ChVtc2VQaWxvdENyZWF0ZUFwcE5hbWUSGxoZYWQtY2VudGVyLWZyYW5rZnVydC1kYWlseQokChlzZWN1cml0eS5pc3Rpby5pby90bHNNb2RlEgcaBWlzdGlvCjQKH3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLW5hbWUSERoPYWQtY2VudGVyLWRhaWx5Ci8KI3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLXJldmlzaW9uEggaBmxhdGVzdAohChdzaWRlY2FyLmlzdGlvLmlvL2luamVjdBIGGgR0cnVlCg8KA3hkYxIIGgZzdGFibGUKGgoHTUVTSF9JRBIPGg1jbHVzdGVyLmxvY2FsCioKBE5BTUUSIhogYWQtY2VudGVyLWRhaWx5LTc0OGM3NmRjODctbnJxOWcKFAoJTkFNRVNQQUNFEgcaBWRhaWx5ClEKBU9XTkVSEkgaRmt1YmVybmV0ZXM6Ly9hcGlzL2FwcHMvdjEvbmFtZXNwYWNlcy9kYWlseS9kZXBsb3ltZW50cy9hZC1jZW50ZXItZGFpbHkKIgoNV09SS0xPQURfTkFNRRIRGg9hZC1jZW50ZXItZGFpbHk="}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 84, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImV4cGlyZWRUaW1lIjo2MDAwMCwibGlzdCI6bnVsbH19", "encoding": "base64"}, "redirectURL": null, "headersSize": 1379, "bodySize": 96}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 254, "receive": 1}}, {"startedDateTime": "2024-05-07T19:44:17.43+08:00", "time": 251, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/airtime/v2/query/auto-onoff-sale-info", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/airtime/v2/query/auto-onoff-sale-info"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715082257513"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "FtN%2FwgxWDllv5IYtKRgvSlx6%2F%2BFkeVcJmQAM%2B8c1S%2B1ethqVdj08qzJYTLImSPiKxyokKYxn2FWpi6%2BMm9JrwbKAhFOVIQ94Q9TOjPX6pK07EETGYI1grMWIIavny4AevO1M9Af%2FGMLYPJeipfaTLd57GLj0tG9L0UD63kDkOCw%3D"}, {"name": "pp_req_sign_2", "value": "PyIv%2F%2Bpl89VujiP5JtSGtjZFa968Cw1k5R%2B2QbVJbdWp1BF4fPfB1POytz7jIhdkkZrR%2BgEe0GWdWXISpt8cKOQ39VNF%2F7gcdTv2y5ZRgXgEmj74qYUHUzrWHB7BqfKwPwnZuzhRUGo3V1L1q8iYIw%2B815TJSjJcRfvsqdUEYiw%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "20"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"categoryId\":[\"8\"]}"}, "headersSize": 372, "bodySize": 20}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:18 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "67"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150822584397680d0001"}, {"name": "x-envoy-upstream-service-time", "value": "34"}], "content": {"size": 67, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbXSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 104, "bodySize": 67}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 250, "receive": 1}}, {"startedDateTime": "2024-05-07T19:44:17.43+08:00", "time": 450, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=BettingHomepage", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=BettingHomepage"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715082257534"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "MfJq3rxcQ34tBlCYU7raDe6tWr15IjVMg8PBej23xnH068ipxHA6oUeOmVbKIZN6eecy%2F6BQW9wNzU1qaUTat3Ajy0RD4zMdySCCyxog%2FWExqrAErzlyBvjJ1Z0G4AX2ok%2FtBS%2BFzcS3xHXLessaP7z20q%2BC5513U1WVJExNrh8%3D"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [{"name": "showLocation", "value": "BettingHomepage"}], "headersSize": 251, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:18 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "366"}, {"name": "eagleeye-traceid", "value": "eaac16306117150822584407597d0001"}, {"name": "x-envoy-upstream-service-time", "value": "234"}], "content": {"size": 366, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbeyJhY3RObyI6IlRlc3QtV1AiLCJjb3Vwb25Db2RlIjoiV2l0aGRyYXctY291cG9uIiwiY291cG9uTmFtZSI6IldpdGhkcmF3LWNvdXBvbiIsImNvdXBvblR5cGUiOiJDQVNIIiwibm9taW5hVmFsdWUiOjEwMDAsImxpbWl0QW10IjpudWxsLCJjb3Vwb25SZWd1bGF0aW9uIjoie1wibGltaXRBbXRcIjpcIi0xXCIsXCJ1c2VMaW1pdFR5cGVcIjpcIjBcIn0iLCJhbmRyb2lkTGlua1VybCI6bnVsbCwiaW9zTGlua1VybCI6bnVsbCwiZGVzYyI6bnVsbCwiYXJlYSI6bnVsbCwic3RhdHVzIjoiVW5jbGFpbWVkIiwibG9nbyI6bnVsbH1dLCJzdGF0dXMiOnRydWV9", "encoding": "base64"}, "redirectURL": null, "headersSize": 106, "bodySize": 366}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 449, "receive": 1}}, {"startedDateTime": "2024-05-07T19:44:17.43+08:00", "time": 251, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/airtime/payBills?location=96", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/airtime/payBills?location=96"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715082257533"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "FaMWlGF1DJ8Eat7k8tvvtJsRoN06pT8awMmw5awi6FxyEQQxrPTFQeSnlhF6rUEmOKDk1WGGECyvqauoTgxfrsiiqWvU73Lsfx%2Fwx3gSSO5kxGle0DW00HekG46Ww8jJsrdSt5Sw5lbCT%2Ba25IkOaCSH%2BbdYi21iCAUjZyBWK5w%3D"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [{"name": "location", "value": "96"}], "headersSize": 369, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:18 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac16306117150822584467598d0001"}, {"name": "x-application-context", "value": "airtime:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "35"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 2465, "compression": 1859, "mimeType": "application/json;charset=UTF-8", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 171, "bodySize": 606}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 251, "receive": 0}}, {"startedDateTime": "2024-05-07T19:44:17.432+08:00", "time": 259, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "J5wagTJtAuplYJ5UgJi0biihjCg69RrYlT805HFd37ZtKjYhgwcrfBNRMNWbZpC8wfZuszQdQIK4Iy5/SXfehvbTdr7FO9b46CjGYqQoMp+tYyuM3OwV7gziSlzPDAeHRTirDozWypqBg02Ii8riot/2g1BsyXuwgXEDYYDwUNA="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.7.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"34B15F9BA85A4A138C32C0D030DE1528\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"lnZo5P0r\",\"requestTime\":1715082257532,\"version\":\"1.0\"}"}, "headersSize": 156, "bodySize": 512}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:18 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "ad-center:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "35"}, {"name": "x-envoy-peer-metadata-id", "value": "sidecar~172.22.48.58~ad-center-daily-748c76dc87-nrq9g.daily~daily.svc.cluster.local"}, {"name": "x-envoy-peer-metadata", "value": "CjAKDkFQUF9DT05UQUlORVJTEh4aHGNvbnN1bC1hZ2VudCxhZC1jZW50ZXItZGFpbHkKGgoKQ0xVU1RFUl9JRBIMGgpLdWJlcm5ldGVzCh4KDElOU1RBTkNFX0lQUxIOGgwxNzIuMjIuNDguNTgKGQoNSVNUSU9fVkVSU0lPThIIGgYxLjE5LjAKoQMKBkxBQkVMUxKWAyqTAwoYCgNhcHASERoPYWQtY2VudGVyLWRhaWx5ChsKE2FybXNQaWxvdEF1dG9FbmFibGUSBBoCb24KKwoWYXJtc1BpbG90Q3JlYXRlQXBwTmFtZRIRGg9hZC1jZW50ZXItZGFpbHkKGgoFZ3JvdXASERoPYWQtY2VudGVyLWRhaWx5ChoKEm1zZVBpbG90QXV0b0VuYWJsZRIEGgJvbgo0ChVtc2VQaWxvdENyZWF0ZUFwcE5hbWUSGxoZYWQtY2VudGVyLWZyYW5rZnVydC1kYWlseQokChlzZWN1cml0eS5pc3Rpby5pby90bHNNb2RlEgcaBWlzdGlvCjQKH3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLW5hbWUSERoPYWQtY2VudGVyLWRhaWx5Ci8KI3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLXJldmlzaW9uEggaBmxhdGVzdAohChdzaWRlY2FyLmlzdGlvLmlvL2luamVjdBIGGgR0cnVlCg8KA3hkYxIIGgZzdGFibGUKGgoHTUVTSF9JRBIPGg1jbHVzdGVyLmxvY2FsCioKBE5BTUUSIhogYWQtY2VudGVyLWRhaWx5LTc0OGM3NmRjODctbnJxOWcKFAoJTkFNRVNQQUNFEgcaBWRhaWx5ClEKBU9XTkVSEkgaRmt1YmVybmV0ZXM6Ly9hcGlzL2FwcHMvdjEvbmFtZXNwYWNlcy9kYWlseS9kZXBsb3ltZW50cy9hZC1jZW50ZXItZGFpbHkKIgoNV09SS0xPQURfTkFNRRIRGg9hZC1jZW50ZXItZGFpbHk="}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 1254, "compression": 566, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImV4cGlyZWRUaW1lIjo2MDAwMCwibGlzdCI6W3siYWRJZCI6IkY0NzI0MjA5NTUwRTQ2QTk4QUZEQjJCNjMxRjM3RDA2IiwiYXBwbGljYXRpb25JZCI6IkI3OTVDNjVBMEVBMzQ3NkY5ODVBNDAyOUE4NTY2OTE2IiwiYWRTbG90SWQiOiIzNEIxNUY5QkE4NUE0QTEzOEMzMkMwRDAzMERFMTUyOCIsImltYWdlVXJsIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vbWVyY2hhbnQvMTY2MDAzMzYwNDAxNjUucG5nIiwibXVsdGlwbGVJbWFnZVVybCI6bnVsbCwiaDVJbWFnZVVybCI6bnVsbCwianVtcFR5cGUiOiIwMyIsImp1bXBQYXJhbXMiOiIiLCJyZWxhdGl2ZVVybCI6Imh0dHBzOi8vdGVzdGg1LnRyYW5zc3BheS5uZXQvaDUtY2hlY2tvdXQvaW5uZXI/YnJvc3dlclNvdXJjZT1leHRlcm5hbCZjb3VudHJ5Q29kZT1ORyZhcHBJZD1MMzc5MjkyNzI1NzImbWVyY2hhbnRVcmw9aHR0cHMlM0ElMkYlMkZ3d3cuaWxvdGJldC5jb20lMkZhZHBvc3QlMkZhdXRoUGFnZS5odG1sJTNGZnJvbVVybCUzRGh0dHBzJTNBJTJGJTJGd3d3Lmlsb3RiZXQuY29tJTJGYWZjMjAyNCUyRiUzRmMlM0RwYWxtcGF5dW5pb24iLCJpbWFnZURpbWVudGlvbiI6Ijk4NCoxNTAiLCJpbWFnZURpbWVudGlvbkxpc3QiOiJbXCI5ODQqMTUwXCJdIiwidmFsaWRTdGFydFRpbWUiOjE3MDQ4NDEyMDAwMDAsInZhbGlkRW5kVGltZSI6MTg5NjEzMDc5OTAwMCwic2hvd1RpbWVzIjpudWxsLCJzaG93RGF0ZXMiOm51bGwsInNob3dUaW1lc0NvbmRpdGlvbiI6bnVsbCwiY2xpY2tGcmVxdWVuY3kiOm51bGwsImNsb3NlQ29udHJvbCI6bnVsbCwic2hvd0Nsb3NlQnV0dG9uIjowLCJjb3VudHJ5Q29kZSI6Ik5HIiwidGV4dENvbnRlbnQiOm51bGwsInRleHRGb250U2l6ZSI6bnVsbCwidGV4dEZvbnRDb2xvciI6IiIsInRleHRJY29uVXJsIjoiIiwidGV4dFNjcm9sbFNwZWVkIjpudWxsLCJiYWNrZ3JvdW5kQ29sb3IiOiIiLCJhZFR5cGUiOjEsIm5hdGl2ZUFkVGl0bGUiOm51bGwsIm5hdGl2ZUFkQ29udGVudCI6bnVsbCwibmF0aXZlQWRQaG90byI6bnVsbCwiaW1hZ2VVcmxEYXJrIjpudWxsLCJ0ZXh0Rm9udENvbG9yRGFyayI6bnVsbCwidGV4dEljb25VcmxEYXJrIjpudWxsLCJiYWNrZ3JvdW5kQ29sb3JEYXJrIjpudWxsLCJuYXRpdmVBZFBob3RvRGFyayI6bnVsbCwiaDVJbWFnZVVybERhcmsiOm51bGx9XX19", "encoding": "base64"}, "redirectURL": null, "headersSize": 1379, "bodySize": 688}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 259, "receive": 0}}, {"startedDateTime": "2024-05-07T19:44:17.475+08:00", "time": 238, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/coupon/statistics", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/coupon/statistics"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715082257547"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "VQcWjaDMYb5g71gh12GRztzdZrWbaTFJbvto7dSBMyJpAFq7OAM4er9iloSy%2F%2F%2FDbGfyBTY%2FJnJ1JeZaOvbNQZIE9WYbuiQTZbB5HW4gahhuSOWMGwmZiuZxkhdb6z%2Bgek%2BKR9MNupvmx30bf5pwNdvm52ltFYJadoLYlLdsOQA%3D"}, {"name": "pp_req_sign_2", "value": "eoLycmKopxcL4nKaRyo%2FarlNMQt%2FseMPiFWpfio%2FoH5aRFK52xZC2bKtLWMJc3xn4lrsHjZiMwGbYk%2Fqiy6I7p4mCcbvHZ5ZeKh2ixprE5i1vtFMkWJci34%2B3zRaCVp7lYq09ST4IGeJz3ITbDNI8QHZVHRv4joJFjvq2Z9aI0o%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "45"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"pageNum\":1,\"pageSize\":100,\"transType\":\"96\"}"}, "headersSize": 327, "bodySize": 45}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:18 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "157"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150822584837681d0001"}, {"name": "x-envoy-upstream-service-time", "value": "24"}], "content": {"size": 157, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7InRvdGFsIjowLCJ0b3RhbFBhZ2UiOjAsImN1clBhZ2UiOjEsInBhZ2VTaXplIjoxMDAsImRhdGFNYXAiOm51bGwsImV4dERhdGEiOm51bGwsImxpc3QiOltdfSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 105, "bodySize": 157}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 237, "receive": 1}}, {"startedDateTime": "2024-05-07T19:44:17.493+08:00", "time": 240, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/ad/pullAd", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/ad/pullAd"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "signature", "value": "aM1AiAObgi9M0MX4y0OBFkmaurYFFTV0wtS82xjeP8qwvTPoBqcoB9kZB3MY/tMDS3olBcG3R4yas4hNFDV0R+eE/ZfKR/cyxn5pfmx3NVUY3gASfflAH2W95Cvet84mGem8DoB23ndQkYepvr48SKOpCzVEeMFBbLDWzYWyG6w="}, {"name": "accept", "value": "application/json"}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "countrycode", "value": "NG"}, {"name": "pp_client_ver", "value": "5.7.0"}, {"name": "imgresolution", "value": "2400*1080"}, {"name": "appchannel", "value": "googleplay"}, {"name": "accept-language", "value": "en"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "512"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"bizInfo\":\"{\\\"adSlotId\\\":\\\"15D71D47F4AA41328D4FA6792A9B8023\\\",\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"extendParam\\\":{\\\"deviceModel\\\":\\\"Infinix X671B\\\",\\\"deviceVersion\\\":\\\"Android13\\\",\\\"brand\\\":\\\"Infinix\\\",\\\"deviceInfo\\\":\\\"bee648d4b0a5ab70\\\"},\\\"gaId\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"infoId\\\":\\\"MDIzNDA5NTUxMjM0OTEx\\\",\\\"lat\\\":9999.0,\\\"lon\\\":9999.0,\\\"notShowAdIds\\\":[],\\\"userId\\\":\\\"A12D79F816C144B593703ECFFE53F319\\\"}\",\"nonceStr\":\"GGDZIAV3\",\"requestTime\":1715082257543,\"version\":\"1.0\"}"}, "headersSize": 156, "bodySize": 512}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:18 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "ad-center:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "19"}, {"name": "x-envoy-peer-metadata-id", "value": "sidecar~172.22.48.58~ad-center-daily-748c76dc87-nrq9g.daily~daily.svc.cluster.local"}, {"name": "x-envoy-peer-metadata", "value": "CjAKDkFQUF9DT05UQUlORVJTEh4aHGNvbnN1bC1hZ2VudCxhZC1jZW50ZXItZGFpbHkKGgoKQ0xVU1RFUl9JRBIMGgpLdWJlcm5ldGVzCh4KDElOU1RBTkNFX0lQUxIOGgwxNzIuMjIuNDguNTgKGQoNSVNUSU9fVkVSU0lPThIIGgYxLjE5LjAKoQMKBkxBQkVMUxKWAyqTAwoYCgNhcHASERoPYWQtY2VudGVyLWRhaWx5ChsKE2FybXNQaWxvdEF1dG9FbmFibGUSBBoCb24KKwoWYXJtc1BpbG90Q3JlYXRlQXBwTmFtZRIRGg9hZC1jZW50ZXItZGFpbHkKGgoFZ3JvdXASERoPYWQtY2VudGVyLWRhaWx5ChoKEm1zZVBpbG90QXV0b0VuYWJsZRIEGgJvbgo0ChVtc2VQaWxvdENyZWF0ZUFwcE5hbWUSGxoZYWQtY2VudGVyLWZyYW5rZnVydC1kYWlseQokChlzZWN1cml0eS5pc3Rpby5pby90bHNNb2RlEgcaBWlzdGlvCjQKH3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLW5hbWUSERoPYWQtY2VudGVyLWRhaWx5Ci8KI3NlcnZpY2UuaXN0aW8uaW8vY2Fub25pY2FsLXJldmlzaW9uEggaBmxhdGVzdAohChdzaWRlY2FyLmlzdGlvLmlvL2luamVjdBIGGgR0cnVlCg8KA3hkYxIIGgZzdGFibGUKGgoHTUVTSF9JRBIPGg1jbHVzdGVyLmxvY2FsCioKBE5BTUUSIhogYWQtY2VudGVyLWRhaWx5LTc0OGM3NmRjODctbnJxOWcKFAoJTkFNRVNQQUNFEgcaBWRhaWx5ClEKBU9XTkVSEkgaRmt1YmVybmV0ZXM6Ly9hcGlzL2FwcHMvdjEvbmFtZXNwYWNlcy9kYWlseS9kZXBsb3ltZW50cy9hZC1jZW50ZXItZGFpbHkKIgoNV09SS0xPQURfTkFNRRIRGg9hZC1jZW50ZXItZGFpbHk="}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 84, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImV4cGlyZWRUaW1lIjo2MDAwMCwibGlzdCI6bnVsbH19", "encoding": "base64"}, "redirectURL": null, "headersSize": 1379, "bodySize": 96}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 240, "receive": 0}}, {"startedDateTime": "2024-05-07T19:44:17.879+08:00", "time": 223, "request": {"method": "POST", "url": "https://test-ad.transspay.net/api/dataBurialPoint/adBehavior", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/dataBurialPoint/adBehavior"}, {"name": ":authority", "value": "test-ad.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json"}, {"name": "countrycode", "value": "NG"}, {"name": "signature", "value": "Iwzb8yPkf2J9wKrGF1aXQkOvCFIKHoeMGGWP3EdpXXEv+kmo9Gi5dVPuvHt/1Crrkno7/WNojcqept3UZeMvQ4z/jwKYTfjrMZ1mgbvjzJ1obn4uiqFvsRGDCYfmk4FSX9pGtLPZ020b9jW8lzAYoih2hQ/LwDjiIA+8ZNmKv/A="}, {"name": "authorization", "value": "Bearer MC3253537245"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "894"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json", "text": "{\"bizInfo\":\"[{\\\"body\\\":{\\\"adId\\\":\\\"F4724209550E46A98AFDB2B631F37D06\\\",\\\"adSlotId\\\":\\\"34B15F9BA85A4A138C32C0D030DE1528\\\",\\\"adTime\\\":1715082257836,\\\"countryCode\\\":\\\"NG\\\",\\\"eventSubType\\\":\\\"\\\",\\\"eventType\\\":\\\"autoBegin\\\",\\\"num\\\":1,\\\"operator\\\":\\\"\\\",\\\"pointWindowX\\\":\\\"\\\",\\\"pointWindowY\\\":\\\"\\\",\\\"relatedId\\\":\\\"\\\",\\\"userId\\\":\\\"\\\",\\\"wifi\\\":true},\\\"head\\\":{\\\"SDKVersion\\\":\\\"1.0.36-SNAPSHOT\\\",\\\"appIdentifier\\\":\\\"com.transsnet.palmpay\\\",\\\"appVersion\\\":\\\"5.7.0\\\",\\\"appVersionCode\\\":604181603,\\\"applicationId\\\":\\\"B795C65A0EA3476F985A4029A8566916\\\",\\\"behaviorId\\\":\\\"B795C65A0EA3476F985A4029A85669161715082257836\\\",\\\"createTime\\\":1715082257840,\\\"eventTime\\\":1715082257840,\\\"gaid\\\":\\\"715131e3-1810-4c6d-8dc3-c6776a8b63cc\\\",\\\"imei\\\":\\\"\\\",\\\"ip\\\":\\\"*************\\\",\\\"macAddress\\\":\\\"please open wifi\\\",\\\"operSystem\\\":\\\"Android\\\"}}]\",\"nonceStr\":\"7ChvYk1A\",\"requestTime\":1715082257844,\"version\":\"1.0.36-SNAPSHOT\"}"}, "headersSize": 158, "bodySize": 894}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:18 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-headers", "value": "PP_LNG,PP_LAT,countryCode,PP_REQ_SIGN_2,PP_DEVICE_ID,PP_DEVICE_TYPE,PP_CLIENT_VER,PP_TIMESTAMP,PP_TOKEN,PP_REQ_SIGN,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "x-application-context", "value": "analysis:dev:8080"}, {"name": "access-control-allow-methods", "value": "POST, GET, OPTIONS, DELETE"}, {"name": "access-control-max-age", "value": "3600"}, {"name": "x-envoy-upstream-service-time", "value": "1"}, {"name": "x-envoy-peer-metadata-id", "value": "sidecar~172.22.39.139~analysis-daily-7c8fdc4cd4-spdhh.daily~daily.svc.cluster.local"}, {"name": "x-envoy-peer-metadata", "value": "Ci8KDkFQUF9DT05UQUlORVJTEh0aG2NvbnN1bC1hZ2VudCxhbmFseXNpcy1kYWlseQoaCgpDTFVTVEVSX0lEEgwaCkt1YmVybmV0ZXMKHwoMSU5TVEFOQ0VfSVBTEg8aDTE3Mi4yMi4zOS4xMzkKGQoNSVNUSU9fVkVSU0lPThIIGgYxLjE5LjAKnAMKBkxBQkVMUxKRAyqOAwoXCgNhcHASEBoOYW5hbHlzaXMtZGFpbHkKGwoTYXJtc1BpbG90QXV0b0VuYWJsZRIEGgJvbgoqChZhcm1zUGlsb3RDcmVhdGVBcHBOYW1lEhAaDmFuYWx5c2lzLWRhaWx5ChkKBWdyb3VwEhAaDmFuYWx5c2lzLWRhaWx5ChoKEm1zZVBpbG90QXV0b0VuYWJsZRIEGgJvbgozChVtc2VQaWxvdENyZWF0ZUFwcE5hbWUSGhoYYW5hbHlzaXMtZnJhbmtmdXJ0LWRhaWx5CiQKGXNlY3VyaXR5LmlzdGlvLmlvL3Rsc01vZGUSBxoFaXN0aW8KMwofc2VydmljZS5pc3Rpby5pby9jYW5vbmljYWwtbmFtZRIQGg5hbmFseXNpcy1kYWlseQovCiNzZXJ2aWNlLmlzdGlvLmlvL2Nhbm9uaWNhbC1yZXZpc2lvbhIIGgZsYXRlc3QKIQoXc2lkZWNhci5pc3Rpby5pby9pbmplY3QSBhoEdHJ1ZQoPCgN4ZGMSCBoGc3RhYmxlChoKB01FU0hfSUQSDxoNY2x1c3Rlci5sb2NhbAopCgROQU1FEiEaH2FuYWx5c2lzLWRhaWx5LTdjOGZkYzRjZDQtc3BkaGgKFAoJTkFNRVNQQUNFEgcaBWRhaWx5ClAKBU9XTkVSEkcaRWt1YmVybmV0ZXM6Ly9hcGlzL2FwcHMvdjEvbmFtZXNwYWNlcy9kYWlseS9kZXBsb3ltZW50cy9hbmFseXNpcy1kYWlseQohCg1XT1JLTE9BRF9OQU1FEhAaDmFuYWx5c2lzLWRhaWx5"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 55, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpudWxsfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 1360, "bodySize": 70}, "serverIPAddress": "***********", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 223, "receive": 0}}, {"startedDateTime": "2024-05-07T19:44:19.825+08:00", "time": 268, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/cfront/betting/info?billerId=2269", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/cfront/betting/info?billerId=2269"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715082259940"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "jRKrhRZKveWA83u83v00jM%2FWqEzyD9eY85AqYqvKCettQwz5a%2BiYouatzRMRjXMo1hhwWklaq14Lib6rQ1K26GJmu9P645NW0TH%2BLXEwEbST5IyCqVYG7psEKrv2nzQd7jyCS8rsrn8f3G%2F88%2B1qJw9Hkt7e2L4rEO2OPpuhouk%3D"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [{"name": "billerId", "value": "2269"}], "headersSize": 203, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:20 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150822608337686d0001"}, {"name": "x-application-context", "value": "betting-product:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "51"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 1373, "compression": 749, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImJldHRpbmdEaXNjb3VudEluZm8iOnsic3RhdHVzIjowLCJwb3B1cFRpdGxlIjoiIiwidGV4dCI6IiIsImNvbnRlbnQiOiIifSwiYmV0dGluZ0JpbGxlck1hcmtldGluZ0VsZVJlc3BEZXRhaWwiOnsiaWQiOjEwMSwiYmlsbGVySWQiOiIyMjY5IiwiYmlsbGVyTmFtZSI6IkJldCA5amEiLCJiaWxsZXJMYWJlbExpc3QiOlsiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTcxMjQ3MjYwNTM0NzE1LURhdGElMjAlMjgxJTI5LnBuZyJdLCJiaWxsZXJEYXJrTGFiZWxMaXN0IjpudWxsLCJiaWxsZXJUaXAiOiLkuJPnlKgiLCJiaWxsZXJCYW5uZXJMaXN0IjpbeyJzb3J0IjoxLCJzdGFydFRpbWUiOjE2Njk4NDkyMDAwMDAsImVuZFRpbWUiOjE4NjQ1OTQ3OTkwMDAsImFkUGljIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTcwMTY3MDE1NjI4OTE0LWJhbm5lci5wbmciLCJkYXJrQWRQaWMiOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNzA4NTg2OTA1NjU4OS0xNzAxNjcwMTU2Mjg5MTQtYmFubmVyJTIwMS5wbmciLCJhZFVybCI6Imh0dHBzOi8vdGVzdGg1LnRyYW5zc3BheS5uZXQvaDUtY2hlY2tvdXQvaW5uZXI/YnJvc3dlclNvdXJjZT1leHRlcm5hbCZjb3VudHJ5Q29kZT1ORyZhcHBJZD1MMzc5MjkyNzI1NzImbWVyY2hhbnRVcmw9aHR0cHMlM0ElMkYlMkZ3d3cuaWxvdGJldC5jb20lMkZwYWxtUGF5QXV0aCUzRmZyb21VcmwlM0RodHRwcyUzQSUyRiUyRnd3dy5pbG90YmV0LmNvbSUyRlBhbG1QYXlBY3Rpdml0eSUyNmMlM0R0ZXN0QXV0aDEyMTUiLCJpb3NBZFVybCI6Imh0dHBzOi8vdGVzdGg1LnRyYW5zc3BheS5uZXQvaDUtY2hlY2tvdXQvaW5uZXI/YnJvc3dlclNvdXJjZT1leHRlcm5hbCZjb3VudHJ5Q29kZT1ORyZhcHBJZD1MMzc5MjkyNzI1NzImbWVyY2hhbnRVcmw9aHR0cHMlM0ElMkYlMkZ3d3cuaWxvdGJldC5jb20lMkZwYWxtUGF5QXV0aCUzRmZyb21VcmwlM0RodHRwcyUzQSUyRiUyRnd3dy5pbG90YmV0LmNvbSUyRlBhbG1QYXlBY3Rpdml0eSUyNmMlM0R0ZXN0QXV0aDEyMTUiLCJ0YWdJZCI6IiIsImFuZHJvaWRTd2l0Y2giOjEsImlvc1N3aXRjaCI6MX1dLCJhbW91bnRUaXAiOm51bGwsInJlY29tbWVuZCI6bnVsbCwib2xkVXNlclJlY29tbWVuZCI6bnVsbCwidXNzZFN0YXR1cyI6bnVsbCwiYXBwbGljYXRpb24iOm51bGwsImFwcGxpY2F0aW9uTmFtZSI6bnVsbH0sImNvbXBldGl0aW9uU3dpdGNoIjoxfX0=", "encoding": "base64"}, "redirectURL": null, "headersSize": 177, "bodySize": 624}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 264, "receive": 4}}, {"startedDateTime": "2024-05-07T19:44:19.883+08:00", "time": 237, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/pay/queryLimitAmtForOrder", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/pay/queryLimitAmtForOrder"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715082259942"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "pZys%2FsuRLbotF4QL%2FBvDApmq%2B%2BAEs0SCk9rYm5UjYyqtY%2FDLtPbBStjsXogVOzfyKnHSDwySEUHoO%2FpMX0%2Fav%2BScvd9ew2ZFEFQguBO1i0rNVYnTrMjGEXUSydq9ZNQC4JUECi3oio7%2FuJ6gBGCi3%2FT00gc2qoIOugHEqoTaQ0I%3D"}, {"name": "pp_req_sign_2", "value": "Yxscngo9VXa7FGc7hzQ73fr8G0LIi8vUCcD6vGj%2BTfIWLAbNVDF%2BOM%2F03k%2FZ%2B4tqoos5rxBNuxzxCEwChLOM2omJQxNTZmXZaopnE02DK1hHNxUJFm4EcS06sbxSTO3Wh6nN0IT%2B%2BzJMeVicn1NfvM1AcQvieI1AMbKpe7xB2E4%3D"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "content-length", "value": "18"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [], "postData": {"mimeType": "application/json; charset=UTF-8", "text": "{\"transType\":\"96\"}"}, "headersSize": 391, "bodySize": 18}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:20 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "112"}, {"name": "eagleeye-traceid", "value": "eaac162adf17150822608907687d0001"}, {"name": "x-envoy-upstream-service-time", "value": "24"}], "content": {"size": 112, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7Im1pbkFtb3VudCI6MCwibWF4QW1vdW50Ijo5MjIzMzcyMDM2ODU0Nzc1ODA3fSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 105, "bodySize": 112}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 237, "receive": 0}}, {"startedDateTime": "2024-05-07T19:44:20.156+08:00", "time": 282, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/gateway/betting/match?timeZone=GMT%2B08%3A00", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/gateway/betting/match?timeZone=GMT%2B08%3A00"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.7.0&604181603"}, {"name": "pp_timestamp", "value": "1715082260270"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.7.0&604181603 (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "77b69754-09d6-4894-bf71-21d54908bf3e"}, {"name": "pp_req_sign", "value": "KFaf8TQF8vgMwCzBo5JeRLUFVrKLJlqUm15a0YPeNClk%2B9HZ26Ra8W0LcuR9ajEfWSdvtWzd6VESc9RLo48KnNMz2AAmyeXDH9HqbqSg0uKB23hlWagxTrXEz9e6qK4VBllma3SLAjSaWFu9E%2FSbsBxW3STmaore2mcDZXgELrQ%3D"}, {"name": "accept-encoding", "value": "gzip"}], "queryString": [{"name": "timeZone", "value": "GMT+08:00"}], "headersSize": 207, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 07 May 2024 11:44:21 GMT"}, {"name": "content-type", "value": "application/json;charset=UTF-8"}, {"name": "content-length", "value": "477"}, {"name": "eagleeye-traceid", "value": "eaac16306117150822611637610d0001"}, {"name": "x-application-context", "value": "betting-product:dev:8080"}, {"name": "x-envoy-upstream-service-time", "value": "69"}], "content": {"size": 477, "mimeType": "application/json;charset=UTF-8", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7Im1hdGNoRGF0ZSI6bnVsbCwibWF0Y2hEYXkiOm51bGwsIm1hdGNoVGltZSI6bnVsbCwiYmFja2dyb3VuZFBpY3R1cmUiOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNzEyNTc1NTUxMzk0NC1UYWdzJTIwJTI4MSUyOS5wbmciLCJiYWNrZ3JvdW5kRGFya1BpY3R1cmUiOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNzEyNTc1NTU4NTY0MTMtVGFncy5wbmciLCJiYWNrZ3JvdW5kQ29sb3IiOiIjZTg0MDI2IiwiYmFja2dyb3VuZERhcmtDb2xvciI6IiMyMGRlZTgiLCJkYXRlQ29sb3IiOm51bGwsImNvbXBldGl0aW9uQ29sb3IiOm51bGwsImNvbXBldGl0aW9uTGlzdCI6W10sIm1hdGNoRGV0YWlsVjJzIjpbXX19", "encoding": "base64"}, "redirectURL": null, "headersSize": 152, "bodySize": 477}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 282, "receive": 0}}]}}