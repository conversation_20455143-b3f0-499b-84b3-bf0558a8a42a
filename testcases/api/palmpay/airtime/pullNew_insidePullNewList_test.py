# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest


class TestPullnewInsidepullnewlistTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("可领券列表").setup_hook('${setup_hooks_request($request)}')
            .get(
                "/api/scene-activity-product/bill/"
            )
            .with_params(**{"showLocation": "AirtimeHomepage"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=AirtimeHomepage",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:27:21 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "MDdUpiZcKLrNKt7XhI4YLQDL%2FPCehGfgbPInxaQZh1myALu%2FW1RC1rJtdmXQ1F90cR7ZRsq2V2OtPCTtCY8JYfDwFg0JcMS6Vzh8b6NeeErqVpVHLY0PNkZi%2B97fjPomIl0rgvRmVpYtpkiMKwfW64E2oD4Fv66ZxQ4Iq81vAVs%3D",
                    "pp_timestamp": "1715081343117",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
        )
    ]


if __name__ == "__main__":
    TestPullnewInsidepullnewlistTest().test_start()
