
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestAirtimeRecommendeditemsTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("推荐套餐").setup_hook('${setup_hooks_request($request)}')
            .get("/api/airtime/recommendedItems")
            .with_params(**{"billerName": "GLO"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/recommendedItems?billerName=GLO",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:37 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "J%2BjA3sIoRld0qj6dzOxyKR9HomyNdcY%2B3WbodDA2B5vz5vBEZcz%2Fnncgxx4ojlqpMw0cRlMGyDzt%2FhLYRpXP%2FWc6807T%2FSh169ldnWeGOWeaBjUgfiejN5HhM9EcxK72HAXPKAXX9wkzi%2Fazj%2FzJwm%2F12vlIhBjPmehBB%2FQsevg%3D",
                    "pp_timestamp": "1715081343254",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]

if __name__ == "__main__":
    TestAirtimeRecommendeditemsTest().test_start()
