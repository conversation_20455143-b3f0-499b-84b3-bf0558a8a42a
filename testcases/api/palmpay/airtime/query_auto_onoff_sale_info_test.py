
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestQueryAutoonoffsaleinfoTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("自动上下架公告组件").setup_hook('${setup_hooks_request($request)}')
            .post(
                "/api/cfront/airtime/v2/query/auto-onoff-sale-info"
            )
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/airtime/v2/query/auto-onoff-sale-info",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "21",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "BTYL5WgQ8USARSyjmPgVyirxyM0DGfFGWpui5rGdqqUZ%2BSNA6T3ldDcjDP1sBKqdLq4FMOahiZJZcI8Q%2BB1nDx8iUzwT6KcaPSSOQFDT1x5iq34Rl2XqTp4qL5TbiP%2FM3cWheYorvU9eDmirx%2BvMj6%2Bzi8u5fiCdvECf14E9jDg%3D",
                    "pp_req_sign_2": "tANoTqmqPg5V0fdrTvgZ69XgSLWJQdWW%2BTdcCXO3wSeB7ImJtFhtbGSempI5t3Yrxpfq8T9TH4dnuo%2FfE6wWQTvXmHuHj4bEmdJ4dZ2uK3cKyy%2F39EidXB8U0VI9fRFQC5JfNiP7GouPwYBrqBtEbZjK1ISkoCZykBnLMj604og%3D",
                    "pp_timestamp": "1715081343174",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_json({"categoryId": ["10"]})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        )
    ]

if __name__ == "__main__":
    TestQueryAutoonoffsaleinfoTest().test_start()
