
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestAgentStatusTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("代理状态").setup_hook('${setup_hooks_request($request)}')
            .get(
                "/api/online-agent-product/agent/status"
            )
            .with_params(**{"categoryId": "10"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/online-agent-product/agent/status?categoryId=10",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "<PERSON>e, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "snBVpaCfrWbqHDdddcZbxQIoHP6P7mX93ycKSPexbcyMX9FT2L%2FWngJ1IIp2qWLwQDknNbFY0CfTcsZRWp6dioQuayvqbcadeOOqL7W6%2BZlg3G%2BRox8TAMtNSdk3vZTA6%2F6FWQqo1uAguflSp%2B0vWcogurYFx0z0IlhZNYWDZRo%3D",
                    "pp_timestamp": "1715081342893",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        )
    ]

if __name__ == "__main__":
    TestAgentStatusTest().test_start()
