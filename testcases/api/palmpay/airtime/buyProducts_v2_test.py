
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestBuyproductsV2Test(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("DFS套餐").setup_hook('${setup_hooks_request($request)}')
            .get("/api/cfront/airtime/buyProducts/v2")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/cfront/airtime/buyProducts/v2",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:36 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "u2uqOaGnM9q1D5%2B%2BuCzy3Mr4jYBj563HfI3geZ4d7HwhWAzn%2BEXdiB6jz%2BhPqVk6jckQGUoq%2B%2F0LVQ8eUjdVrxR8f3FZxo1TgOk4QDWJbwvqxOhzt7CtlP31O8xmN4CJo0Ps%2FbivqPZYYj6f9VCGO9tlQpp2ieyNMu%2B%2BmihqgdE%3D",
                    "pp_timestamp": "1715081343177",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]

if __name__ == "__main__":
    TestBuyproductsV2Test().test_start()
