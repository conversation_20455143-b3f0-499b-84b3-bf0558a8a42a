
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestItemTipsTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("套餐cashback").setup_hook('${setup_hooks_request($request)}')
            .post("/api/cfront/airtime/item/tips")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/airtime/item/tips",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "0",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "PMGt%2FcYzyasuhl92nbJj6B5zg8Tfozd7i1ztrrcA6hqjimvwPGVQNWvl%2FkaRzKerkeHNdB%2BQJRCBDlhXAf%2B5HaorDhq44dQjHHKm71VFwE2aDY1heGWFVkgEYLjd8McPxlAeg57B6oQFDV86PybuW4fp7kJaJXggNuO358ZlSSw%3D",
                    "pp_timestamp": "1715081343009",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]

if __name__ == "__main__":
    TestItemTipsTest().test_start()
