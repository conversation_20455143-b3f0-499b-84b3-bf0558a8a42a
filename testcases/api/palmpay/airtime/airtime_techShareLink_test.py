
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestAirtimeTechsharelinkTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("外卡支付").setup_hook('${setup_hooks_request($request)}')
            .get("/api/airtime/techShareLink")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/techShareLink",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "hBh0T6wTDx2OQEnjCoWcfglQW5ieaP9SFZvqwjix3cw2qalX%2BcdemCv2Z14GyzxPVHVFMYt%2FjZ2s1YWjgOz%2FmofXJTwYhYZZvlQcKOyISo6h%2FI8MbkIJFOs11v9wuUTI2D5eW3IS1%2BjwhYQbyhFzAg8M6cMc%2FBmw6s3EyCPQngM%3D",
                    "pp_timestamp": "1715081342997",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.data", None, "assert response body data")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]

if __name__ == "__main__":
    TestAirtimeTechsharelinkTest().test_start()
