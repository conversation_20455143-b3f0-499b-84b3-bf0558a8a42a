
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestCashbackGetbizconfigTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("getBizConfig").setup_hook('${setup_hooks_request($request)}')
            .post("/api/airtime/cashback/getBizConfig")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/airtime/cashback/getBizConfig",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "18",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "I%2BUEQfGWcnUQMCHUs5X1zzCXdsupvkLr8duRsJh%2Bi3aOPbLsATjqRPqGUcFk1g0C8VlT3MNXfP04d4RwXoq%2Bos7hCGhR17Jgx67z0mlD1ZMCWLtHVQqyRqzslJBSi4RZ7Iw%2Bxd4XYexrFWw68CFIQB%2BobZzTFD6h%2BpFvrBdQwrc%3D",
                    "pp_req_sign_2": "H8kwISn%2BUeHFLdPNucdGhq%2FBgJwgs88rAXKttm1GcTrHoqw8NXhJ0WhKp2EmF5JZ%2Bf4%2BJT9kOEjvrZDE6lzODTOYaLzZZ7E5pxvXQN08wqZ8x4wyRMYmB0mhU3wvijEqAI1dtkNHdCrpKIu6XcF6FAXpUbkGMyn3uSHS%2BaFmZIw%3D",
                    "pp_timestamp": "1715081343111",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_json({"transType": "04"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]

if __name__ == "__main__":
    TestCashbackGetbizconfigTest().test_start()
