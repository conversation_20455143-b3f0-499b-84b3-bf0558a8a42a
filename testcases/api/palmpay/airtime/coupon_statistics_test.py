
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestCouponStatisticsTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("已领取券列表").setup_hook('${setup_hooks_request($request)}')
            .post("/api/cfront/coupon/statistics")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/coupon/statistics",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "45",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "R3VqElKmCa%2FjZyP7J0H00YA%2Bv%2FWWZOf2UVyCqqG5i%2FwMvJIMV%2FOEVfzdkK1OuNf%2FbKoZdF1zBNk6OVxYTMayAJEslQWGhwban4FmnWyCnAXp6OpG4dH1XIWKMWl%2By1uz33CTOLr10l7qRlI3pHDpUC5nGn44AWzTHiKW2cVGgd8%3D",
                    "pp_req_sign_2": "A7UD%2FB8lmJIl194doPKpSskslld6JCBt4TZP%2BaNBVAHDovzxTg34%2BcG6IE9SYDG32YVjbJicNUD29Gc8%2FbxQZb64doWvoJiU4qTafvDPzig4AiEv2URKvqzGsIewT5KlefTXyK0FZvPdrOaMYZhl9whea9USjtZDmvH5Kr0Ne8A%3D",
                    "pp_timestamp": "1715081343185",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_json({"pageNum": 1, "pageSize": 100, "transType": "04"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        )
    ]

if __name__ == "__main__":
    TestCouponStatisticsTest().test_start()
