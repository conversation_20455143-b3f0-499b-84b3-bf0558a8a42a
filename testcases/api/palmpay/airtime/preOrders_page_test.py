
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestPreordersPageTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("预下单").setup_hook('${setup_hooks_request($request)}')
            .get("/api/airtime/preOrders/page")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/preOrders/page",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:27:21 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "hzw5Wb3kYUmKisIf0MKvKrdpQVW5Jk7eKm1f3c95fEx%2BcgviRW9RegJu8Di3n3HFTCX30W%2BUzwtrLTFPtVMUWrKAknJy4xm9wymojjU4kw4xCwNdrB5aFlDBtPwfMhbgy%2BFWXgA5zaZ9C44npnyi6B0HVUMJbkdpvbUbk%2Bxh5TQ%3D",
                    "pp_timestamp": "1715081343012",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]

if __name__ == "__main__":
    TestPreordersPageTest().test_start()
