
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestMenuV4Test(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("套餐列表").setup_hook('${setup_hooks_request($request)}')
            .post("/api/cfront/airtime/menu/v4")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/airtime/menu/v4",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "0",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "QAcCbkrsxTgQOgCAYuSE5KZVFXlUoLwCGz0M9HE%2BmimNJGSpGJvoEimJWC%2Baeyo53xXy8yzPlMArx00B%2FkWky%2BEy07O8hr2OBt7zFBjIvgwGsTm6iHPRMI8uBZplMWqRwmZtwgdhrQEGNxEv%2BnKorlwekxkjCtHr986DQurLFyo%3D",
                    "pp_timestamp": "1715081342988",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]

if __name__ == "__main__":
    TestMenuV4Test().test_start()
