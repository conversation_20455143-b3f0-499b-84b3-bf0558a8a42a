
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestAirtimeServicesTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("话费主页底部service入口").setup_hook('${setup_hooks_request($request)}')
            .get("/api/airtime/services")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/services",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "Bek5dF2xFCdtaV3yIpv3Vultt819glOYGX7W4i9up1z3r%2Bq0%2Fr9pJXhtRZ4ef%2FNrWmKPcgiC1e0RKQde4xkpCQH%2FYGIhRhwwgfurbKgOXmxoLyQxHh9dj562R%2Ftl4Zt8U1wkNrJzFwzzTsTH4jupZL1LmJ3%2Fq1X%2FNoTzeHKzwlU%3D",
                    "pp_timestamp": "1715081343084",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]

if __name__ == "__main__":
    TestAirtimeServicesTest().test_start()
