# NOTE: Generated By HttpRunner v4.3.5
# FROM: testcases\palmpay\debug\airtime_test_zihao_test.json
from httprunner import HttpRunner, Config, Step, RunRequest


class TestCaseAirtimeTestZihaoTest(HttpRunner):

    config = Config("testcase description")

    teststeps = [
        Step(
            RunRequest("代理状态")
            .get(
                "https://ng-apptest.transspay.net/api/online-agent-product/agent/status"
            )
            .with_params(**{"categoryId": "10"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/online-agent-product/agent/status?categoryId=10",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "snBVpaCfrWbqHDdddcZbxQIoHP6P7mX93ycKSPexbcyMX9FT2L%2FWngJ1IIp2qWLwQDknNbFY0CfTcsZRWp6dioQuayvqbcadeOOqL7W6%2BZlg3G%2BRox8TAMtNSdk3vZTA6%2F6FWQqo1uAguflSp%2B0vWcogurYFx0z0IlhZNYWDZRo%3D",
                    "pp_timestamp": "1715081342893",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("套餐列表")
            .post("https://ng-apptest.transspay.net/api/cfront/airtime/menu/v4")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/airtime/menu/v4",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "0",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "QAcCbkrsxTgQOgCAYuSE5KZVFXlUoLwCGz0M9HE%2BmimNJGSpGJvoEimJWC%2Baeyo53xXy8yzPlMArx00B%2FkWky%2BEy07O8hr2OBt7zFBjIvgwGsTm6iHPRMI8uBZplMWqRwmZtwgdhrQEGNxEv%2BnKorlwekxkjCtHr986DQurLFyo%3D",
                    "pp_timestamp": "1715081342988",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("最近交易十条记录")
            .get(
                "https://ng-apptest.transspay.net/api/cfront/airtime/recharge/records/latest"
            )
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/cfront/airtime/recharge/records/latest",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "hBh0T6wTDx2OQEnjCoWcfglQW5ieaP9SFZvqwjix3cw2qalX%2BcdemCv2Z14GyzxPVHVFMYt%2FjZ2s1YWjgOz%2FmofXJTwYhYZZvlQcKOyISo6h%2FI8MbkIJFOs11v9wuUTI2D5eW3IS1%2BjwhYQbyhFzAg8M6cMc%2FBmw6s3EyCPQngM%3D",
                    "pp_timestamp": "1715081342997",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("外卡支付")
            .get("https://ng-apptest.transspay.net/api/airtime/techShareLink")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/techShareLink",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "hBh0T6wTDx2OQEnjCoWcfglQW5ieaP9SFZvqwjix3cw2qalX%2BcdemCv2Z14GyzxPVHVFMYt%2FjZ2s1YWjgOz%2FmofXJTwYhYZZvlQcKOyISo6h%2FI8MbkIJFOs11v9wuUTI2D5eW3IS1%2BjwhYQbyhFzAg8M6cMc%2FBmw6s3EyCPQngM%3D",
                    "pp_timestamp": "1715081342997",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.data", None, "assert response body data")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("套餐cashback")
            .post("https://ng-apptest.transspay.net/api/cfront/airtime/item/tips")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/airtime/item/tips",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "0",
                    "content-type": "application/json",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "PMGt%2FcYzyasuhl92nbJj6B5zg8Tfozd7i1ztrrcA6hqjimvwPGVQNWvl%2FkaRzKerkeHNdB%2BQJRCBDlhXAf%2B5HaorDhq44dQjHHKm71VFwE2aDY1heGWFVkgEYLjd8McPxlAeg57B6oQFDV86PybuW4fp7kJaJXggNuO358ZlSSw%3D",
                    "pp_timestamp": "1715081343009",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("预下单")
            .get("https://ng-apptest.transspay.net/api/airtime/preOrders/page")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/preOrders/page",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:27:21 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "hzw5Wb3kYUmKisIf0MKvKrdpQVW5Jk7eKm1f3c95fEx%2BcgviRW9RegJu8Di3n3HFTCX30W%2BUzwtrLTFPtVMUWrKAknJy4xm9wymojjU4kw4xCwNdrB5aFlDBtPwfMhbgy%2BFWXgA5zaZ9C44npnyi6B0HVUMJbkdpvbUbk%2Bxh5TQ%3D",
                    "pp_timestamp": "1715081343012",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("查询号码对应运营商")
            .get(
                "https://ng-apptest.transspay.net/api/cfront/quickteller/resolve/network/query"
            )
            .with_params(**{"mobileNo": "09551234911"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/cfront/quickteller/resolve/network/query?mobileNo=09551234911",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:36 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "wC02FlTsbHusBVyhqEz5h%2BR5GacwugzY691716TduaLwc0xO7TJATRFV47kQFmguo47GmP2VDHk2qXgAvEkt3xX9HmVnibaWdO4PgqU%2BlHOxvK1DaLG8Ir8isFupjFTpe9bSUp4Ar%2FB4qH8ruGMJGKhfPRYJ%2BvK2rGQ8SlgxqVQ%3D",
                    "pp_timestamp": "1715081343076",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.data", "GLO", "assert response body data")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("话费主页底部service入口")
            .get("https://ng-apptest.transspay.net/api/airtime/services")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/services",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "Bek5dF2xFCdtaV3yIpv3Vultt819glOYGX7W4i9up1z3r%2Bq0%2Fr9pJXhtRZ4ef%2FNrWmKPcgiC1e0RKQde4xkpCQH%2FYGIhRhwwgfurbKgOXmxoLyQxHh9dj562R%2Ftl4Zt8U1wkNrJzFwzzTsTH4jupZL1LmJ3%2Fq1X%2FNoTzeHKzwlU%3D",
                    "pp_timestamp": "1715081343084",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("可领券列表")
            .get(
                "https://ng-apptest.transspay.net/api/scene-activity-product/bill/pullNew/insidePullNewList"
            )
            .with_params(**{"showLocation": "AirtimeHomepage"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=AirtimeHomepage",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:27:21 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "MDdUpiZcKLrNKt7XhI4YLQDL%2FPCehGfgbPInxaQZh1myALu%2FW1RC1rJtdmXQ1F90cR7ZRsq2V2OtPCTtCY8JYfDwFg0JcMS6Vzh8b6NeeErqVpVHLY0PNkZi%2B97fjPomIl0rgvRmVpYtpkiMKwfW64E2oD4Fv66ZxQ4Iq81vAVs%3D",
                    "pp_timestamp": "1715081343117",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.data", None, "assert response body data")
        ),
        Step(
            RunRequest("getBizConfig")
            .post("https://ng-apptest.transspay.net/api/airtime/cashback/getBizConfig")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/airtime/cashback/getBizConfig",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "18",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "I%2BUEQfGWcnUQMCHUs5X1zzCXdsupvkLr8duRsJh%2Bi3aOPbLsATjqRPqGUcFk1g0C8VlT3MNXfP04d4RwXoq%2Bos7hCGhR17Jgx67z0mlD1ZMCWLtHVQqyRqzslJBSi4RZ7Iw%2Bxd4XYexrFWw68CFIQB%2BobZzTFD6h%2BpFvrBdQwrc%3D",
                    "pp_req_sign_2": "H8kwISn%2BUeHFLdPNucdGhq%2FBgJwgs88rAXKttm1GcTrHoqw8NXhJ0WhKp2EmF5JZ%2Bf4%2BJT9kOEjvrZDE6lzODTOYaLzZZ7E5pxvXQN08wqZ8x4wyRMYmB0mhU3wvijEqAI1dtkNHdCrpKIu6XcF6FAXpUbkGMyn3uSHS%2BaFmZIw%3D",
                    "pp_timestamp": "1715081343111",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_data({"transType": "04"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("自动上下架公告组件")
            .post(
                "https://ng-apptest.transspay.net/api/cfront/airtime/v2/query/auto-onoff-sale-info"
            )
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/airtime/v2/query/auto-onoff-sale-info",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "21",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "BTYL5WgQ8USARSyjmPgVyirxyM0DGfFGWpui5rGdqqUZ%2BSNA6T3ldDcjDP1sBKqdLq4FMOahiZJZcI8Q%2BB1nDx8iUzwT6KcaPSSOQFDT1x5iq34Rl2XqTp4qL5TbiP%2FM3cWheYorvU9eDmirx%2BvMj6%2Bzi8u5fiCdvECf14E9jDg%3D",
                    "pp_req_sign_2": "tANoTqmqPg5V0fdrTvgZ69XgSLWJQdWW%2BTdcCXO3wSeB7ImJtFhtbGSempI5t3Yrxpfq8T9TH4dnuo%2FfE6wWQTvXmHuHj4bEmdJ4dZ2uK3cKyy%2F39EidXB8U0VI9fRFQC5JfNiP7GouPwYBrqBtEbZjK1ISkoCZykBnLMj604og%3D",
                    "pp_timestamp": "1715081343174",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_data({"categoryId": ["10"]})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("DFS套餐")
            .get("https://ng-apptest.transspay.net/api/cfront/airtime/buyProducts/v2")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/cfront/airtime/buyProducts/v2",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:36 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "u2uqOaGnM9q1D5%2B%2BuCzy3Mr4jYBj563HfI3geZ4d7HwhWAzn%2BEXdiB6jz%2BhPqVk6jckQGUoq%2B%2F0LVQ8eUjdVrxR8f3FZxo1TgOk4QDWJbwvqxOhzt7CtlP31O8xmN4CJo0Ps%2FbivqPZYYj6f9VCGO9tlQpp2ieyNMu%2B%2BmihqgdE%3D",
                    "pp_timestamp": "1715081343177",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("biller快捷访问组件")
            .get("https://ng-apptest.transspay.net/api/airtime/payBills")
            .with_params(**{"location": "04"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/payBills?location=04",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "GlsfoYX9ZXTqWhlA25EdJkhbHavBj1isfctqrx8Dg%2FDmjOBIXzN7ikLHbT6lG%2BSbJeFI%2Bwblklbgt8Intmks4Kx5wy5QdE8v3LL05m8LZFwEAT1fKKMa1uO8r1QWIMBHccHS%2BVxVCk2xYm%2Byd6nXRXauWU%2ByT3R7clJkqID%2Fpzo%3D",
                    "pp_timestamp": "1715081343179",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
        Step(
            RunRequest("已领取券列表")
            .post("https://ng-apptest.transspay.net/api/cfront/coupon/statistics")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/coupon/statistics",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-length": "45",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "R3VqElKmCa%2FjZyP7J0H00YA%2Bv%2FWWZOf2UVyCqqG5i%2FwMvJIMV%2FOEVfzdkK1OuNf%2FbKoZdF1zBNk6OVxYTMayAJEslQWGhwban4FmnWyCnAXp6OpG4dH1XIWKMWl%2By1uz33CTOLr10l7qRlI3pHDpUC5nGn44AWzTHiKW2cVGgd8%3D",
                    "pp_req_sign_2": "A7UD%2FB8lmJIl194doPKpSskslld6JCBt4TZP%2BaNBVAHDovzxTg34%2BcG6IE9SYDG32YVjbJicNUD29Gc8%2FbxQZb64doWvoJiU4qTafvDPzig4AiEv2URKvqzGsIewT5KlefTXyK0FZvPdrOaMYZhl9whea9USjtZDmvH5Kr0Ne8A%3D",
                    "pp_timestamp": "1715081343185",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .with_data({"pageNum": 1, "pageSize": 100, "transType": "04"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("推荐套餐")
            .get("https://ng-apptest.transspay.net/api/airtime/recommendedItems")
            .with_params(**{"billerName": "GLO"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/recommendedItems?billerName=GLO",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:37 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "J%2BjA3sIoRld0qj6dzOxyKR9HomyNdcY%2B3WbodDA2B5vz5vBEZcz%2Fnncgxx4ojlqpMw0cRlMGyDzt%2FhLYRpXP%2FWc6807T%2FSh169ldnWeGOWeaBjUgfiejN5HhM9EcxK72HAXPKAXX9wkzi%2Fazj%2FzJwm%2F12vlIhBjPmehBB%2FQsevg%3D",
                    "pp_timestamp": "1715081343254",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        ),
    ]


if __name__ == "__main__":
    TestCaseAirtimeTestZihaoTest().test_start()
