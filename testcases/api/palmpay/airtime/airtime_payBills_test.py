
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestAirtimePaybillsTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("biller快捷访问组件").setup_hook('${setup_hooks_request($request)}')
            .get("/api/airtime/payBills")
            .with_params(**{"location": "04"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/airtime/payBills?location=04",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "<PERSON><PERSON>, 07 May 2024 11:26:34 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "GlsfoYX9ZXTqWhlA25EdJkhbHavBj1isfctqrx8Dg%2FDmjOBIXzN7ikLHbT6lG%2BSbJeFI%2Bwblklbgt8Intmks4Kx5wy5QdE8v3LL05m8LZFwEAT1fKKMa1uO8r1QWIMBHccHS%2BVxVCk2xYm%2Byd6nXRXauWU%2ByT3R7clJkqID%2Fpzo%3D",
                    "pp_timestamp": "1715081343179",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]

if __name__ == "__main__":
    TestAirtimePaybillsTest().test_start()
