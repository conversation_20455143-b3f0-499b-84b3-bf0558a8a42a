
# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\airtime\airtime_test_zihao_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest

class TestNetworkQueryTest(HttpRunner):
    config = Config("step name").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("查询号码对应运营商").setup_hook('${setup_hooks_request($request)}')
            .get(
                "/api/cfront/quickteller/resolve/network/query"
            )
            .with_params(**{"mobileNo": "09551234911"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/cfront/quickteller/resolve/network/query?mobileNo=09551234911",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 07 May 2024 11:26:36 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.7.0&604181603",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "wC02FlTsbHusBVyhqEz5h%2BR5GacwugzY691716TduaLwc0xO7TJATRFV47kQFmguo47GmP2VDHk2qXgAvEkt3xX9HmVnibaWdO4PgqU%2BlHOxvK1DaLG8Ir8isFupjFTpe9bSUp4Ar%2FB4qH8ruGMJGKhfPRYJ%2BvK2rGQ8SlgxqVQ%3D",
                    "pp_timestamp": "1715081343076",
                    "pp_token": "77b69754-09d6-4894-bf71-21d54908bf3e",
                    "user-agent": "PalmPay/5.7.0&604181603 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.data", "GLO", "assert response body data")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]

if __name__ == "__main__":
    TestNetworkQueryTest().test_start()
