# NOTE: Generated By HttpRunner v4.3.5
# FROM: testcases\pay_bill\pay_bill_test.json
from httprunner import HttpRun<PERSON>, Config, Step, RunRequest

from debugtalk import get_pp_timestamp


class TestCasePayBillTest(HttpRunner):


    config = Config("testcase description").base_url("${ENV(base_url_C)}").variables(**{"timestamp": '${get_pp_timestamp()}'})

    teststeps = [
        Step(
            RunRequest("")
            .setup_hook('${setup_hooks_request($request)}')
            # .with_variables(
            #     **{"data": '{"month": "2024-03"}'}
            # )
            .post(
                "/api/online-agent-product/annualBill/monthSavings"
            )
            .with_headers(
                **{
                    "accept-encoding": "gzip",
                    "content-length": "19",
                    "content-type": "application/json; charset=UTF-8",
                    "pp_channel": "googleplay",
                    # "pp_client_ver": "5.3.0&603020703",
                    # "pp_device_id": "bee648d4b0a5ab70012345",
                    # "pp_device_type": "ANDROID",
                    # "pp_req_sign": "${get_pp_sign(timestamp=$timestamp,token=${get_token_plugin()})}",
                    # "pp_req_sign_2": "${get_pp_sign(data=$data)}",
                    # "pp_timestamp": "${timestamp}",
                    # "pp_token": "${get_token_plugin()}",
                    "user-agent": "PalmPay/5.3.0&603020703 (Android 13)",
                }
            )
            # .with_data('{"month": "2024-03"}')
            .with_json({"month": "2024-03"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        # Step(
        #     RunRequest("")
        #     .post(
        #         "https://ng-apptest.transspay.net/api/online-agent-product/annualBill/yearSavings"
        #     )
        #     .with_headers(
        #         **{
        #             ":authority": "ng-apptest.transspay.net",
        #             ":method": "POST",
        #             ":path": "/api/online-agent-product/annualBill/yearSavings",
        #             ":scheme": "https",
        #             "accept-encoding": "gzip",
        #             "content-length": "15",
        #             "content-type": "application/json; charset=UTF-8",
        #             "pp_channel": "googleplay",
        #             "pp_client_ver": "5.3.0&603020703",
        #             "pp_device_id": "bee648d4b0a5ab70012345",
        #             "pp_device_type": "ANDROID",
        #             "pp_req_sign": "i2hFWgNO5sdcSHkaTOeHITaqseb8gQPVuM2fZaPafQDzC7jj1xHcNt6VTi0SqK2gpaxiDhhmnE%2BkC3%2B8UKE7oSu6xkE4NbBLZ0qirh0S4gVZzJJL8W7iTgMC6pBNziO5RQA8ippcjq08AfSp06I5HhUy7qha1IX7aFJg2lMgJ7g%3D",
        #             "pp_req_sign_2": "dUfBLDG0J5v1MPJzmtOxHk0LmgM2wn%2F%2FdReQTCHx%2Bk3H9y6syePgBiNMPqp2Q%2FUQGSJILrwlFvPXQNu91ggdGMKywLgiQz0CLGYOtJ6RSvYo7sokb47p6kt8RODHI4OWkUZTAf2BtMBDOHJ2aeHl5%2BCEQystIkEH8N7ikn5jGL8%3D",
        #             "pp_timestamp": "${get_pp_timestamp()}",
        #             "pp_token": "5aee7c45-8f20-499b-8630-dac085b97cd5",
        #             "user-agent": "PalmPay/5.3.0&603020703 (Android 13)",
        #         }
        #     )
        #     .with_data({"year": "2024"})
        #     .validate()
        #     .assert_equal("status_code", 200, "assert response status code")
        #     .assert_equal("body.respCode", "00000000", "assert response body respCode")
        #     .assert_equal("body.respMsg", "success", "assert response body respMsg")
        #     .assert_equal("body.status", True, "assert response body status")
        # ),
    ]


if __name__ == "__main__":
    TestCasePayBillTest().test_start()
