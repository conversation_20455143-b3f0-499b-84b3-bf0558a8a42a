{"config": {"name": "testcase description"}, "teststeps": [{"name": "", "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/online-agent-product/annualBill/monthSavings", "headers": {":authority": "ng-apptest.transspay.net", ":method": "POST", ":path": "/api/online-agent-product/annualBill/monthSavings", ":scheme": "https", "accept-encoding": "gzip", "content-length": "19", "content-type": "application/json; charset=UTF-8", "pp_channel": "googleplay", "pp_client_ver": "5.3.0&603020703", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "uke%2B1BQdyFexJVQpui8VSw3gpuBvT1JwP0XzNArOdMjXE6qjduoqWTUi3vKOpFYX8cNkI%2FtqAwFQd8eQHd8M%2BuWeaQknPQmrF5OGVeMqN9ul%2BJG87RpCxisksCQ6yXSmmLAIVaxWe43wvEU3xvQWxdSeshI0fqWFZewcWsxMuY0%3D", "pp_req_sign_2": "Va%2BqH%2B%2B%2F2fQ2JYTblgJMHyN6GqxqI%2FdY50gs4e%2B9S%2FTh6ABJyRC%2B1KmVmZFH3MsPUh6K7twqv5xFnv%2FWtugVnxmnOwhYHM6VnoGm1BQyrSG5zeycelyH66SNwlvGebqlbeqEV2NLmGfLAAzOZh%2FMYet3OLcyODLFfcBuOHZTfuk%3D", "pp_timestamp": "1709567880945", "pp_token": "5aee7c45-8f20-499b-8630-dac085b97cd5", "user-agent": "PalmPay/5.3.0&603020703 (Android 13)"}, "body": {"month": "2024-03"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}, {"check": "body.status", "assert": "equals", "expect": true, "msg": "assert response body status"}]}, {"name": "", "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/online-agent-product/annualBill/yearSavings", "headers": {":authority": "ng-apptest.transspay.net", ":method": "POST", ":path": "/api/online-agent-product/annualBill/yearSavings", ":scheme": "https", "accept-encoding": "gzip", "content-length": "15", "content-type": "application/json; charset=UTF-8", "pp_channel": "googleplay", "pp_client_ver": "5.3.0&603020703", "pp_device_id": "bee648d4b0a5ab70012345", "pp_device_type": "ANDROID", "pp_req_sign": "i2hFWgNO5sdcSHkaTOeHITaqseb8gQPVuM2fZaPafQDzC7jj1xHcNt6VTi0SqK2gpaxiDhhmnE%2BkC3%2B8UKE7oSu6xkE4NbBLZ0qirh0S4gVZzJJL8W7iTgMC6pBNziO5RQA8ippcjq08AfSp06I5HhUy7qha1IX7aFJg2lMgJ7g%3D", "pp_req_sign_2": "dUfBLDG0J5v1MPJzmtOxHk0LmgM2wn%2F%2FdReQTCHx%2Bk3H9y6syePgBiNMPqp2Q%2FUQGSJILrwlFvPXQNu91ggdGMKywLgiQz0CLGYOtJ6RSvYo7sokb47p6kt8RODHI4OWkUZTAf2BtMBDOHJ2aeHl5%2BCEQystIkEH8N7ikn5jGL8%3D", "pp_timestamp": "1709567880969", "pp_token": "5aee7c45-8f20-499b-8630-dac085b97cd5", "user-agent": "PalmPay/5.3.0&603020703 (Android 13)"}, "body": {"year": "2024"}}, "validate": [{"check": "status_code", "assert": "equals", "expect": 200, "msg": "assert response status code"}, {"check": "headers.\"Content-Type\"", "assert": "equals", "expect": "application/json", "msg": "assert response header Content-Type"}, {"check": "body.respCode", "assert": "equals", "expect": "00000000", "msg": "assert response body respCode"}, {"check": "body.respMsg", "assert": "equals", "expect": "success", "msg": "assert response body respMsg"}, {"check": "body.status", "assert": "equals", "expect": true, "msg": "assert response body status"}]}]}