# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\giftCard\giftCardApi_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest


class TestBillListTest(HttpRunner):
    config = Config("礼品卡-biller列表信息").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("礼品卡-biller列表信息").setup_hook('${setup_hooks_request($request)}')
            .post(
                "/api/live-product/giftcard/bill/list"
            )
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/live-product/giftcard/bill/list",
                    ":scheme": "https",
                    "accept": "application/json, text/plain, */*",
                    "accept-encoding": "gzip",
                    "content-length": "37",
                    "content-type": "application/json",
                    "countrycode": "NG",
                    "param_pp_app_source": "PalmPay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_client_ver_code": "609231908",
                    "pp_country_code": "NG",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "rFDZNK0oogaNyYgS3kFbu2X94VHTT0%2B7CjGNcyDv2ADGd3l56hJxyIsigPSlVfd4fc0%2FDMhjJymJtk1N9bS3B3%2BwuinbhoBtAVi4wBGIor%2BnIfCDI8NMW51o3xt8UcjS5mu%2FNRx0IOR%2FqGXHC7yjmZ0qiq0sZpK0dz5seE6Szaw%3D",
                    "pp_req_sign_2": "cupi61pXMkGc2lAqArHWnmgF1mVjfI2kZqfUNrnRs17EXmMq5SuHJeR1kAtvxMhcHImP1VpVOFGvkjBb09VLNIr4pTR9HMNqjz4HgKCDo2sJDhkoRz5dY0a1D7pDrPTWny0Iw%2F9OAymp%2B3VgaB95ky17oaIULo2af%2BCNzMFvqAc%3D",
                    "pp_timestamp": "1728380472584",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .with_json({"pageNum": 1, "pageSize": 20, "tabId": 0})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]


if __name__ == "__main__":
    TestBillListTest().test_start()
