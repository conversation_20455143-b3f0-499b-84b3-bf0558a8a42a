# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\giftCard\giftCardApi_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest


class TestPullnewInsidepullnewlistTest(HttpRunner):
    config = Config("首页可领券信息(合并后最新信息)").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("首页可领券信息(合并后最新信息)").setup_hook('${setup_hooks_request($request)}')
            .get(
                "/api/scene-activity-product/bill/pullNew/insidePullNewList"
            )
            .with_params(**{"showLocation": "v5"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=v5",
                    ":scheme": "https",
                    "accept": "application/json, text/plain, */*",
                    "accept-encoding": "gzip",
                    "countrycode": "NG",
                    "if-modified-since": "Tue, 08 Oct 2024 09:38:14 GMT",
                    "param_pp_app_source": "PalmPay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_client_ver_code": "609231908",
                    "pp_country_code": "NG",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "NCChlgRhi6eVGHLrJJr8awG61QZyyN7ekenZEcSjwBZrFc8X0VQNiHvPFMXNxuvPopijt9pLgEG6zv3pNutjaj3Ziy4BoQDGZnHwxrS3RYuKUvv6CF7W5bxkn8hu1JGeOMZ%2FkaV0v3g7hgFsv1XflU9Rspn4WzFWocugHBd2x5I%3D",
                    "pp_req_sign_2": "NCChlgRhi6eVGHLrJJr8awG61QZyyN7ekenZEcSjwBZrFc8X0VQNiHvPFMXNxuvPopijt9pLgEG6zv3pNutjaj3Ziy4BoQDGZnHwxrS3RYuKUvv6CF7W5bxkn8hu1JGeOMZ%2FkaV0v3g7hgFsv1XflU9Rspn4WzFWocugHBd2x5I%3D",
                    "pp_timestamp": "1728380434758",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]


if __name__ == "__main__":
    TestPullnewInsidepullnewlistTest().test_start()
