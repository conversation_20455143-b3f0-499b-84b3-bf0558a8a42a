# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\giftCard\giftCardApi_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest


class TestOrderPageTest(HttpRunner):
    config = Config("礼品卡历史订单记录").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("礼品卡历史订单记录").setup_hook('${setup_hooks_request($request)}')
            .post(
                "/api/live-product/gateway/giftcard/order/page"
            )
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/live-product/gateway/giftcard/order/page",
                    ":scheme": "https",
                    "accept": "application/json, text/plain, */*",
                    "accept-encoding": "gzip",
                    "content-length": "45",
                    "content-type": "application/json",
                    "countrycode": "NG",
                    "param_pp_app_source": "PalmPay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_client_ver_code": "609231908",
                    "pp_country_code": "NG",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "aNViL8%2BW5gzDb%2FVGQ%2F9FMYN5th0qIytEXRrjc9Z5ff0ehhaFLzlUaOOv%2BtM7YTIJzJOOzS%2BQzWrGAeku8VhER%2FSeXrVmHOzZD9MtNHbwCQxThsZ989p%2F1iYZ9IXkrK3l4t6x9S2dhoxOARN1z4tCnh67denIwBnaerwLR6RsDBY%3D",
                    "pp_req_sign_2": "gftaOWxi6pJ68XhStpuf%2FESU9jwzIDCSwgk1tT5lx773zR2DvlC5%2BdkJ%2FiUMiFLT9wGLjBuXWgf4wrR8MBFFh8GPoFGydCKYXhLNSanIiLVGik9FZJNEIoXmMGOJxScAcsERVGwzmkuHqj6IRm57wP7T9wqEkRalR%2FrDRfzKlpo%3D",
                    "pp_timestamp": "1728380444843",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .with_json({"categoryId": "35", "pageNum": 1, "pageSize": 10})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]


if __name__ == "__main__":
    TestOrderPageTest().test_start()
