# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\giftCard\giftCardApi_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest


class TestOrderDetailTest(HttpRunner):
    config = Config("礼品卡-订单详情页").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("礼品卡-订单详情页").setup_hook('${setup_hooks_request($request)}')
            .get(
                "/api/live-product/gateway/giftcard/order/detail"
            )
            .with_params(**{"orderNo": "3526ulgycb0035"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/live-product/gateway/giftcard/order/detail?orderNo=3526ulgycb0035",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 08 Oct 2024 09:31:20 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "LIiEQzrbyTr%2BUhrZfPo5hD0UumVr3pW7zxKS3bkJBhzcUaOQ2619V4PsPPGUdIx%2FHi37VmES3RSwPqTMqXet99paRQsVWAlURgPBSSVey1lC63bO%2Fwdvvk18mikqnDJw5JPpPv2crehPCZDfqHSV9QlRpntpP3kE6DsRhGjoCwI%3D",
                    "pp_timestamp": "1728380449345",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "PalmPay/5.16.0&609231908 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]


if __name__ == "__main__":
    TestOrderDetailTest().test_start()
