# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\giftCard\giftCardApi_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest


class TestGiftcardHomepageTest(HttpRunner):
    config = Config("礼品卡主页").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("礼品卡主页").setup_hook('${setup_hooks_request($request)}')
            .get("/api/live-product/giftcard/homepage")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/live-product/giftcard/homepage",
                    ":scheme": "https",
                    "accept": "application/json, text/plain, */*",
                    "accept-encoding": "gzip",
                    "countrycode": "NG",
                    "if-modified-since": "Tue, 08 Oct 2024 09:38:14 GMT",
                    "param_pp_app_source": "PalmPay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_client_ver_code": "609231908",
                    "pp_country_code": "NG",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "k%2Bu0T70qpLYi4r3VSbTS7368Y75pMXzdx1owOcmIlnyyUWSoimdSrUbyLskZwnNC1W3cyK9C6MsXnkazGD5yWQ5fd2iYfcHB4EY27eKHaYNeLYcvGUsKsS6EF%2Bup0%2BDnbFg%2FRwfG5qrpCIRjccEeKn5LuXkSN7DlZr0jp5om9FU%3D",
                    "pp_req_sign_2": "k%2Bu0T70qpLYi4r3VSbTS7368Y75pMXzdx1owOcmIlnyyUWSoimdSrUbyLskZwnNC1W3cyK9C6MsXnkazGD5yWQ5fd2iYfcHB4EY27eKHaYNeLYcvGUsKsS6EF%2Bup0%2BDnbFg%2FRwfG5qrpCIRjccEeKn5LuXkSN7DlZr0jp5om9FU%3D",
                    "pp_timestamp": "1728380434766",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]


if __name__ == "__main__":
    TestGiftcardHomepageTest().test_start()
