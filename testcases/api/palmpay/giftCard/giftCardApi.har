{"log": {"version": "1.2", "creator": {"name": "<PERSON>", "version": "4.6.4"}, "entries": [{"startedDateTime": "2024-10-08T17:40:35.603+08:00", "time": 347, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=v5", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=v5"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json, text/plain, */*"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.16.0&*********"}, {"name": "pp_client_ver_code", "value": "*********"}, {"name": "pp_timestamp", "value": "1728380434758"}, {"name": "pp_country_code", "value": "NG"}, {"name": "param_pp_app_source", "value": "PalmPay"}, {"name": "pp_token", "value": "f063cf90-9e93-4001-9ee8-4b9063d3d745"}, {"name": "pp_req_sign", "value": "NCChlgRhi6eVGHLrJJr8awG61QZyyN7ekenZEcSjwBZrFc8X0VQNiHvPFMXNxuvPopijt9pLgEG6zv3pNutjaj3Ziy4BoQDGZnHwxrS3RYuKUvv6CF7W5bxkn8hu1JGeOMZ%2FkaV0v3g7hgFsv1XflU9Rspn4WzFWocugHBd2x5I%3D"}, {"name": "pp_req_sign_2", "value": "NCChlgRhi6eVGHLrJJr8awG61QZyyN7ekenZEcSjwBZrFc8X0VQNiHvPFMXNxuvPopijt9pLgEG6zv3pNutjaj3Ziy4BoQDGZnHwxrS3RYuKUvv6CF7W5bxkn8hu1JGeOMZ%2FkaV0v3g7hgFsv1XflU9Rspn4WzFWocugHBd2x5I%3D"}, {"name": "countrycode", "value": "NG"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}, {"name": "if-modified-since", "value": "Tue, 08 Oct 2024 09:38:14 GMT"}], "queryString": [{"name": "showLocation", "value": "v5"}], "headersSize": 399, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 08 Oct 2024 09:40:35 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "803"}, {"name": "eagleeye-traceid", "value": "eaac162ae917283814357945844d0001"}, {"name": "x-envoy-upstream-service-time", "value": "145"}], "content": {"size": 803, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjpbeyJhY3RObyI6InRlc3QtZ2lmdGNhcmQtMDgxNSIsImNvdXBvbkNvZGUiOiJ0ZXN0LWdpZnRjYXJkIiwiY291cG9uTmFtZSI6IuekvOWTgeWNoea1i+ivleWIuCIsImNvdXBvblR5cGUiOiJDQVNIIiwibm9taW5hVmFsdWUiOjEwMDAsImxpbWl0QW10IjpudWxsLCJjb3Vwb25SZWd1bGF0aW9uIjoie1wibGltaXRBbXRcIjpcIi0xXCIsXCJ1c2VMaW1pdFR5cGVcIjpcIjBcIn0iLCJhbmRyb2lkTGlua1VybCI6bnVsbCwiaW9zTGlua1VybCI6bnVsbCwiZGVzYyI6bnVsbCwiYXJlYSI6bnVsbCwic3RhdHVzIjoiVW5jbGFpbWVkIiwibG9nbyI6bnVsbH0seyJhY3RObyI6IkJldHRpbmctaW5zaWRlIiwiY291cG9uQ29kZSI6IkJldHRpbmctaW5zaWRlMyIsImNvdXBvbk5hbWUiOiJCZXR0aW5nLWluc2lkZTMiLCJjb3Vwb25UeXBlIjoiQ0FTSCIsIm5vbWluYVZhbHVlIjo4MDAsImxpbWl0QW10IjpudWxsLCJjb3Vwb25SZWd1bGF0aW9uIjoie1wiY2F0ZWdvcnlQYXJlbnRJZFwiOlwiMVwiLFwibGltaXRBbXRcIjpcIi0xXCIsXCJjYXRlZ29yeUlkXCI6XCJbOF1cIixcInVzZUxpbWl0VHlwZVwiOlwiMFwifSIsImFuZHJvaWRMaW5rVXJsIjoiL3F1aWNrX3RlbGxlci9iZXR0aW5nX2hvbWUiLCJpb3NMaW5rVXJsIjoicGFsbXBheTovL21haW4vVFlDYWJsZVRWVmlld0NvbnRyb2xsZXI/dmNUeXBlPTMiLCJkZXNjIjpudWxsLCJhcmVhIjpudWxsLCJzdGF0dXMiOiJVbmNsYWltZWQiLCJsb2dvIjpudWxsfV0sInN0YXR1cyI6dHJ1ZX0=", "encoding": "base64"}, "redirectURL": null, "headersSize": 107, "bodySize": 803}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 346, "receive": 0}}, {"startedDateTime": "2024-10-08T17:40:35.603+08:00", "time": 276, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/cfront/coupon/statistics", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/cfront/coupon/statistics"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json, text/plain, */*"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.16.0&*********"}, {"name": "pp_client_ver_code", "value": "*********"}, {"name": "pp_timestamp", "value": "1728380434760"}, {"name": "pp_country_code", "value": "NG"}, {"name": "param_pp_app_source", "value": "PalmPay"}, {"name": "pp_token", "value": "f063cf90-9e93-4001-9ee8-4b9063d3d745"}, {"name": "pp_req_sign", "value": "SaQAyvSbTpBF59gtIaVVdpyChYOEy4gCXC0qpag9P7cfR8kD%2Bt7xfAQiWo7mwBKz9%2BJFgORLAsvZw19bittkWb8W%2FJQnoFjBW5yE%2B4CrPk8bzUdEwd%2BuxJXPgZGGxzC05WHmELUSB7euMhnygqu4KP9qP6R%2FlO0KA8gfdyO0r%2Bc%3D"}, {"name": "pp_req_sign_2", "value": "Inx9Ulx2AZrPRFAX5Aakno4oVL7fvKFN4l3xYFrXRv7bU66slgZ207NtSsSo%2FXhI1NaHIWhP2GYCP9vbGL99svQt%2FETzM0CwnFLn8RQNfgbSIKFBQxqNsL3gjMTMwHFI%2FlhIV0%2BJ1QLPCrc2rPCWCdeZYDJhpfBYxjqxDmFofgw%3D"}, {"name": "countrycode", "value": "NG"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "45"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json", "text": "{\"transType\":\"v5\",\"pageNum\":1,\"pageSize\":100}"}, "headersSize": 185, "bodySize": 45}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 08 Oct 2024 09:40:35 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "157"}, {"name": "eagleeye-traceid", "value": "eaac16266f17283814357907405d0001"}, {"name": "x-envoy-upstream-service-time", "value": "23"}], "content": {"size": 157, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7InRvdGFsIjowLCJ0b3RhbFBhZ2UiOjAsImN1clBhZ2UiOjEsInBhZ2VTaXplIjoxMDAsImRhdGFNYXAiOm51bGwsImV4dERhdGEiOm51bGwsImxpc3QiOltdfSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 106, "bodySize": 157}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 56, "wait": 219, "receive": 1}}, {"startedDateTime": "2024-10-08T17:40:35.659+08:00", "time": 1118, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/live-product/giftcard/homepage", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/live-product/giftcard/homepage"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json, text/plain, */*"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.16.0&*********"}, {"name": "pp_client_ver_code", "value": "*********"}, {"name": "pp_timestamp", "value": "1728380434766"}, {"name": "pp_country_code", "value": "NG"}, {"name": "param_pp_app_source", "value": "PalmPay"}, {"name": "pp_token", "value": "f063cf90-9e93-4001-9ee8-4b9063d3d745"}, {"name": "pp_req_sign", "value": "k%2Bu0T70qpLYi4r3VSbTS7368Y75pMXzdx1owOcmIlnyyUWSoimdSrUbyLskZwnNC1W3cyK9C6MsXnkazGD5yWQ5fd2iYfcHB4EY27eKHaYNeLYcvGUsKsS6EF%2Bup0%2BDnbFg%2FRwfG5qrpCIRjccEeKn5LuXkSN7DlZr0jp5om9FU%3D"}, {"name": "pp_req_sign_2", "value": "k%2Bu0T70qpLYi4r3VSbTS7368Y75pMXzdx1owOcmIlnyyUWSoimdSrUbyLskZwnNC1W3cyK9C6MsXnkazGD5yWQ5fd2iYfcHB4EY27eKHaYNeLYcvGUsKsS6EF%2Bup0%2BDnbFg%2FRwfG5qrpCIRjccEeKn5LuXkSN7DlZr0jp5om9FU%3D"}, {"name": "countrycode", "value": "NG"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}, {"name": "if-modified-since", "value": "Tue, 08 Oct 2024 09:38:14 GMT"}], "queryString": [], "headersSize": 353, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 08 Oct 2024 09:40:36 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "448"}, {"name": "eagleeye-traceid", "value": "eaac16266f17283814358457406d0001"}, {"name": "x-envoy-upstream-service-time", "value": "921"}], "content": {"size": 448, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7ImRlZmF1bHRCaWxsZXIiOnsiYnVzaW5lc3NUeXBlIjoiMzUiLCJiaWxsZXJJZCI6IjEwMTc0MTI1NDk4NzIzNTAiLCJiaWxsZXJOYW1lIjoiQVBQTEUiLCJiaWxsZXJTaG9ydE5hbWUiOiJBUFBMRSIsImJpbGxlckxvZ28iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNjkxNjM1OTI4NjE3MTYtNzc3LnBuZyIsIm1pbkFtb3VudCI6MCwibWF4QW1vdW50IjowLCJyZWNvbW1lbmQiOnRydWUsInN1c3BlbmQiOmZhbHNlLCJ0aXBzIjoiYmJiYmJiYmJiYmJiYiIsInVzZXJJZFRpcHMiOm51bGwsImNvdW50cmllcyI6bnVsbCwiaGlzdG9yeUNvdW50cnlDb2RlIjoiVVMifSwiYmlsbGVyc0hpc3RvcnkiOltdfSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 106, "bodySize": 448}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 1118, "receive": 0}}, {"startedDateTime": "2024-10-08T17:40:45.682+08:00", "time": 222, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/live-product/gateway/giftcard/order/page", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/live-product/gateway/giftcard/order/page"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json, text/plain, */*"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.16.0&*********"}, {"name": "pp_client_ver_code", "value": "*********"}, {"name": "pp_timestamp", "value": "1728380444843"}, {"name": "pp_country_code", "value": "NG"}, {"name": "param_pp_app_source", "value": "PalmPay"}, {"name": "pp_token", "value": "f063cf90-9e93-4001-9ee8-4b9063d3d745"}, {"name": "pp_req_sign", "value": "aNViL8%2BW5gzDb%2FVGQ%2F9FMYN5th0qIytEXRrjc9Z5ff0ehhaFLzlUaOOv%2BtM7YTIJzJOOzS%2BQzWrGAeku8VhER%2FSeXrVmHOzZD9MtNHbwCQxThsZ989p%2F1iYZ9IXkrK3l4t6x9S2dhoxOARN1z4tCnh67denIwBnaerwLR6RsDBY%3D"}, {"name": "pp_req_sign_2", "value": "gftaOWxi6pJ68XhStpuf%2FESU9jwzIDCSwgk1tT5lx773zR2DvlC5%2BdkJ%2FiUMiFLT9wGLjBuXWgf4wrR8MBFFh8GPoFGydCKYXhLNSanIiLVGik9FZJNEIoXmMGOJxScAcsERVGwzmkuHqj6IRm57wP7T9wqEkRalR%2FrDRfzKlpo%3D"}, {"name": "countrycode", "value": "NG"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "45"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json", "text": "{\"pageNum\":1,\"pageSize\":10,\"categoryId\":\"35\"}"}, "headersSize": 384, "bodySize": 45}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 08 Oct 2024 09:40:45 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162ae917283814458675860d0001"}, {"name": "x-envoy-upstream-service-time", "value": "21"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 8052, "compression": 7199, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7InRvdGFsIjoxMzAsInRvdGFsUGFnZSI6MTMsImN1clBhZ2UiOjEsInBhZ2VTaXplIjoxMCwiZGF0YU1hcCI6bnVsbCwiZXh0RGF0YSI6bnVsbCwibGlzdCI6W3sib3JkZXJObyI6IjM1MjZ1bGd5Y2IwMDM1IiwibWVtYmVySWQiOiJBMTJENzlGODE2QzE0NEI1OTM3MDNFQ0ZGRTUzRjMxOSIsImJ1c2luZXNzVHlwZSI6IjM1IiwiY2F0ZWdvcnlJZCI6IjM1Iiwib3JkZXJTdGF0dXMiOjEsInBheVR5cGUiOiIxIiwidHJhbnNUeXBlIjoidjUiLCJvcmRlckFtb3VudCI6MzUwMCwiYnVzaW5lc3NBbW91bnQiOjM1MDAsInBheUFtb3VudCI6MzUwMCwiZGlzY291bnRBbW91bnQiOjAsInBheW1lbnRNZXRob2RzRGlzY291bnQiOiIwIiwibG95YWx0eUFtb3VudCI6MCwibG95YWx0eVBvaW50IjowLCJwb2ludEJhY2tBbW91bnQiOjAsInBvaW50QmFja0NvdW50IjpudWxsLCJyZXR1cm5Qb2ludHNUcmFmZmljSWQiOm51bGwsInJldHVyblBvaW50c0JhY2tUcmFmZmljSWQiOm51bGwsInJldHVyblBvaW50QmFja0Ftb3VudCI6bnVsbCwicmV0dXJuUG9pbnRCYWNrQ291bnQiOjAsInBheUZlZUFtb3VudCI6MCwidGF4RmVlQW1vdW50IjowLCJ0b3RhbEFtb3VudCI6MzUwMCwicGF5VGltZSI6MTcyMjMzMjI0NTAwMCwiY29tcGxldGV0aW1lIjoxNzIyMzMyMjU1MDAwLCJiaWxsZXJJZCI6IjEwMTc0MTI1NDk4NzIzNTAiLCJiaWxsZXJOYW1lIjoiQVBQTEUiLCJiaWxsZXJMb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTY5MTYzNTkyODYxNzE2LTc3Ny5wbmciLCJpdGVtSWQiOiJzMTIxMTc4MjQxOTQ0MTAwMCIsIml0ZW1OYW1lIjpudWxsLCJhcHBsaWNhYmxlQ291bnRyeSI6IlVTIn0seyJvcmRlck5vIjoiMzUyNnM2NHVjOTAwMzUiLCJtZW1iZXJJZCI6IkExMkQ3OUY4MTZDMTQ0QjU5MzcwM0VDRkZFNTNGMzE5IiwiYnVzaW5lc3NUeXBlIjoiMzUiLCJjYXRlZ29yeUlkIjoiMzUiLCJvcmRlclN0YXR1cyI6MiwicGF5VHlwZSI6IjEiLCJ0cmFuc1R5cGUiOiJ2NSIsIm9yZGVyQW1vdW50IjoxNzUwMDAsImJ1c2luZXNzQW1vdW50IjoxNzUwMDAsInBheUFtb3VudCI6MTc1MDAwLCJkaXNjb3VudEFtb3VudCI6MCwicGF5bWVudE1ldGhvZHNEaXNjb3VudCI6IjAiLCJsb3lhbHR5QW1vdW50IjowLCJsb3lhbHR5UG9pbnQiOjAsInBvaW50QmFja0Ftb3VudCI6MCwicG9pbnRCYWNrQ291bnQiOm51bGwsInJldHVyblBvaW50c1RyYWZmaWNJZCI6bnVsbCwicmV0dXJuUG9pbnRzQmFja1RyYWZmaWNJZCI6bnVsbCwicmV0dXJuUG9pbnRCYWNrQW1vdW50IjpudWxsLCJyZXR1cm5Qb2ludEJhY2tDb3VudCI6MCwicGF5RmVlQW1vdW50IjowLCJ0YXhGZWVBbW91bnQiOjAsInRvdGFsQW1vdW50IjoxNzUwMDAsInBheVRpbWUiOjE3MjIyMTkwNTcwMDAsImNvbXBsZXRldGltZSI6MTcyMjIxOTYwNTAwMCwiYmlsbGVySWQiOiIxMDE3NDEyNTQ5ODcyMzUwIiwiYmlsbGVyTmFtZSI6IkFQUExFIiwiYmlsbGVyTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2OTE2MzU5Mjg2MTcxNi03NzcucG5nIiwiaXRlbUlkIjoiczEyOTA0MTUxOTgxNzEwNTAiLCJpdGVtTmFtZSI6IjEwMCIsImFwcGxpY2FibGVDb3VudHJ5IjpudWxsfSx7Im9yZGVyTm8iOiIzNTI2bjVxbGM5MDAzNSIsIm1lbWJlcklkIjoiQTEyRDc5RjgxNkMxNDRCNTkzNzAzRUNGRkU1M0YzMTkiLCJidXNpbmVzc1R5cGUiOiIzNSIsImNhdGVnb3J5SWQiOiIzNSIsIm9yZGVyU3RhdHVzIjoxLCJwYXlUeXBlIjoiMSIsInRyYW5zVHlwZSI6InY1Iiwib3JkZXJBbW91bnQiOjEwNTAwLCJidXNpbmVzc0Ftb3VudCI6MTA1MDAsInBheUFtb3VudCI6MTA1MDAsImRpc2NvdW50QW1vdW50IjowLCJwYXltZW50TWV0aG9kc0Rpc2NvdW50IjoiMCIsImxveWFsdHlBbW91bnQiOjAsImxveWFsdHlQb2ludCI6MCwicG9pbnRCYWNrQW1vdW50IjowLCJwb2ludEJhY2tDb3VudCI6bnVsbCwicmV0dXJuUG9pbnRzVHJhZmZpY0lkIjpudWxsLCJyZXR1cm5Qb2ludHNCYWNrVHJhZmZpY0lkIjpudWxsLCJyZXR1cm5Qb2ludEJhY2tBbW91bnQiOm51bGwsInJldHVyblBvaW50QmFja0NvdW50IjowLCJwYXlGZWVBbW91bnQiOjAsInRheEZlZUFtb3VudCI6MCwidG90YWxBbW91bnQiOjEwNTAwLCJwYXlUaW1lIjoxNzIxOTg1MjYzMDAwLCJjb21wbGV0ZXRpbWUiOjE3MjE5ODUyODEwMDAsImJpbGxlcklkIjoiMTAxNzQxMjU0OTg3MjM1MCIsImJpbGxlck5hbWUiOiJBUFBMRSIsImJpbGxlckxvZ28iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNjkxNjM1OTI4NjE3MTYtNzc3LnBuZyIsIml0ZW1JZCI6InMxMjkwNDE1MTk4MTcxMDUwIiwiaXRlbU5hbWUiOiI2IiwiYXBwbGljYWJsZUNvdW50cnkiOm51bGx9LHsib3JkZXJObyI6IjM1MjZuMjhjYzgwMDM1IiwibWVtYmVySWQiOiJBMTJENzlGODE2QzE0NEI1OTM3MDNFQ0ZGRTUzRjMxOSIsImJ1c2luZXNzVHlwZSI6IjM1IiwiY2F0ZWdvcnlJZCI6IjM1Iiwib3JkZXJTdGF0dXMiOjEsInBheVR5cGUiOiIxIiwidHJhbnNUeXBlIjoidjUiLCJvcmRlckFtb3VudCI6MzUwMCwiYnVzaW5lc3NBbW91bnQiOjM1MDAsInBheUFtb3VudCI6MzUwMCwiZGlzY291bnRBbW91bnQiOjAsInBheW1lbnRNZXRob2RzRGlzY291bnQiOiIwIiwibG95YWx0eUFtb3VudCI6MCwibG95YWx0eVBvaW50IjowLCJwb2ludEJhY2tBbW91bnQiOjAsInBvaW50QmFja0NvdW50IjpudWxsLCJyZXR1cm5Qb2ludHNUcmFmZmljSWQiOm51bGwsInJldHVyblBvaW50c0JhY2tUcmFmZmljSWQiOm51bGwsInJldHVyblBvaW50QmFja0Ftb3VudCI6bnVsbCwicmV0dXJuUG9pbnRCYWNrQ291bnQiOjAsInBheUZlZUFtb3VudCI6MCwidGF4RmVlQW1vdW50IjowLCJ0b3RhbEFtb3VudCI6MzUwMCwicGF5VGltZSI6MTcyMTk4MDcxOTAwMCwiY29tcGxldGV0aW1lIjoxNzIxOTgwNzQyMDAwLCJiaWxsZXJJZCI6IjEwMTc0MTI1NDk4NzIzNTAiLCJiaWxsZXJOYW1lIjoiQVBQTEUiLCJiaWxsZXJMb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTY5MTYzNTkyODYxNzE2LTc3Ny5wbmciLCJpdGVtSWQiOiJzMTIxMTc4MjQxOTQ0MTAwMCIsIml0ZW1OYW1lIjoiMiIsImFwcGxpY2FibGVDb3VudHJ5IjoiVVMifSx7Im9yZGVyTm8iOiIzNTI1N2QxMGJkMDAzNSIsIm1lbWJlcklkIjoiQTEyRDc5RjgxNkMxNDRCNTkzNzAzRUNGRkU1M0YzMTkiLCJidXNpbmVzc1R5cGUiOiIzNSIsImNhdGVnb3J5SWQiOiIzNSIsIm9yZGVyU3RhdHVzIjoxLCJwYXlUeXBlIjoiMSIsInRyYW5zVHlwZSI6InY1Iiwib3JkZXJBbW91bnQiOjM1MDAsImJ1c2luZXNzQW1vdW50IjozNTAwLCJwYXlBbW91bnQiOjM1MDAsImRpc2NvdW50QW1vdW50IjowLCJwYXltZW50TWV0aG9kc0Rpc2NvdW50IjoiMCIsImxveWFsdHlBbW91bnQiOjAsImxveWFsdHlQb2ludCI6MCwicG9pbnRCYWNrQW1vdW50IjowLCJwb2ludEJhY2tDb3VudCI6bnVsbCwicmV0dXJuUG9pbnRzVHJhZmZpY0lkIjpudWxsLCJyZXR1cm5Qb2ludHNCYWNrVHJhZmZpY0lkIjpudWxsLCJyZXR1cm5Qb2ludEJhY2tBbW91bnQiOm51bGwsInJldHVyblBvaW50QmFja0NvdW50IjowLCJwYXlGZWVBbW91bnQiOjAsInRheEZlZUFtb3VudCI6MCwidG90YWxBbW91bnQiOjM1MDAsInBheVRpbWUiOjE3MTk1Njg1OTgwMDAsImNvbXBsZXRldGltZSI6MTcxOTU2ODYwNjAwMCwiYmlsbGVySWQiOiIxMDE3NDEyNTQ5ODcyMzUwIiwiYmlsbGVyTmFtZSI6IkFQUExFIiwiYmlsbGVyTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2OTE2MzU5Mjg2MTcxNi03NzcucG5nIiwiaXRlbUlkIjoiczEyMTE3ODI0MTk0NDEwMDAiLCJpdGVtTmFtZSI6IjIuMDAiLCJhcHBsaWNhYmxlQ291bnRyeSI6IlVTIn0seyJvcmRlck5vIjoiMzUyNTc4ODFiZDAwMzUiLCJtZW1iZXJJZCI6IkExMkQ3OUY4MTZDMTQ0QjU5MzcwM0VDRkZFNTNGMzE5IiwiYnVzaW5lc3NUeXBlIjoiMzUiLCJjYXRlZ29yeUlkIjoiMzUiLCJvcmRlclN0YXR1cyI6MSwicGF5VHlwZSI6IjEiLCJ0cmFuc1R5cGUiOiJ2NSIsIm9yZGVyQW1vdW50IjozNTAwLCJidXNpbmVzc0Ftb3VudCI6MzUwMCwicGF5QW1vdW50IjozNTAwLCJkaXNjb3VudEFtb3VudCI6MCwicGF5bWVudE1ldGhvZHNEaXNjb3VudCI6IjAiLCJsb3lhbHR5QW1vdW50IjowLCJsb3lhbHR5UG9pbnQiOjAsInBvaW50QmFja0Ftb3VudCI6MCwicG9pbnRCYWNrQ291bnQiOm51bGwsInJldHVyblBvaW50c1RyYWZmaWNJZCI6bnVsbCwicmV0dXJuUG9pbnRzQmFja1RyYWZmaWNJZCI6bnVsbCwicmV0dXJuUG9pbnRCYWNrQW1vdW50IjpudWxsLCJyZXR1cm5Qb2ludEJhY2tDb3VudCI6MCwicGF5RmVlQW1vdW50IjowLCJ0YXhGZWVBbW91bnQiOjAsInRvdGFsQW1vdW50IjozNTAwLCJwYXlUaW1lIjoxNzE5NTYyMzcxMDAwLCJjb21wbGV0ZXRpbWUiOjE3MTk1NjIzOTEwMDAsImJpbGxlcklkIjoiMTAxNzQxMjU0OTg3MjM1MCIsImJpbGxlck5hbWUiOiJBUFBMRSIsImJpbGxlckxvZ28iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNjkxNjM1OTI4NjE3MTYtNzc3LnBuZyIsIml0ZW1JZCI6InMxMjExNzgyNDE5NDQxMDAwIiwiaXRlbU5hbWUiOiIyLjAwIiwiYXBwbGljYWJsZUNvdW50cnkiOiJVUyJ9LHsib3JkZXJObyI6IjM1MjU3ODJxYmQwMDM1IiwibWVtYmVySWQiOiJBMTJENzlGODE2QzE0NEI1OTM3MDNFQ0ZGRTUzRjMxOSIsImJ1c2luZXNzVHlwZSI6IjM1IiwiY2F0ZWdvcnlJZCI6IjM1Iiwib3JkZXJTdGF0dXMiOjEsInBheVR5cGUiOiIxIiwidHJhbnNUeXBlIjoidjUiLCJvcmRlckFtb3VudCI6MzUwMCwiYnVzaW5lc3NBbW91bnQiOjM1MDAsInBheUFtb3VudCI6MzUwMCwiZGlzY291bnRBbW91bnQiOjAsInBheW1lbnRNZXRob2RzRGlzY291bnQiOiIwIiwibG95YWx0eUFtb3VudCI6MCwibG95YWx0eVBvaW50IjowLCJwb2ludEJhY2tBbW91bnQiOjAsInBvaW50QmFja0NvdW50IjpudWxsLCJyZXR1cm5Qb2ludHNUcmFmZmljSWQiOm51bGwsInJldHVyblBvaW50c0JhY2tUcmFmZmljSWQiOm51bGwsInJldHVyblBvaW50QmFja0Ftb3VudCI6bnVsbCwicmV0dXJuUG9pbnRCYWNrQ291bnQiOjAsInBheUZlZUFtb3VudCI6MCwidGF4RmVlQW1vdW50IjowLCJ0b3RhbEFtb3VudCI6MzUwMCwicGF5VGltZSI6MTcxOTU2MjE4MTAwMCwiY29tcGxldGV0aW1lIjoxNzE5NTYyMTkxMDAwLCJiaWxsZXJJZCI6IjEwMTc0MTI1NDk4NzIzNTAiLCJiaWxsZXJOYW1lIjoiQVBQTEUiLCJiaWxsZXJMb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTY5MTYzNTkyODYxNzE2LTc3Ny5wbmciLCJpdGVtSWQiOiJzMTIxMTc4MjQxOTQ0MTAwMCIsIml0ZW1OYW1lIjoiMi4wMCIsImFwcGxpY2FibGVDb3VudHJ5IjoiVVMifSx7Im9yZGVyTm8iOiIzNTI1NzFrNGJkMDAzNSIsIm1lbWJlcklkIjoiQTEyRDc5RjgxNkMxNDRCNTkzNzAzRUNGRkU1M0YzMTkiLCJidXNpbmVzc1R5cGUiOiIzNSIsImNhdGVnb3J5SWQiOiIzNSIsIm9yZGVyU3RhdHVzIjo0LCJwYXlUeXBlIjoiMSIsInRyYW5zVHlwZSI6InY1Iiwib3JkZXJBbW91bnQiOjM1MDAsImJ1c2luZXNzQW1vdW50IjozNTAwLCJwYXlBbW91bnQiOjM1MDAsImRpc2NvdW50QW1vdW50IjowLCJwYXltZW50TWV0aG9kc0Rpc2NvdW50IjoiMCIsImxveWFsdHlBbW91bnQiOjAsImxveWFsdHlQb2ludCI6MCwicG9pbnRCYWNrQW1vdW50IjowLCJwb2ludEJhY2tDb3VudCI6bnVsbCwicmV0dXJuUG9pbnRzVHJhZmZpY0lkIjpudWxsLCJyZXR1cm5Qb2ludHNCYWNrVHJhZmZpY0lkIjpudWxsLCJyZXR1cm5Qb2ludEJhY2tBbW91bnQiOm51bGwsInJldHVyblBvaW50QmFja0NvdW50IjowLCJwYXlGZWVBbW91bnQiOjAsInRheEZlZUFtb3VudCI6MCwidG90YWxBbW91bnQiOjM1MDAsInBheVRpbWUiOjE3MTk1NTM3MzUwMDAsImNvbXBsZXRldGltZSI6bnVsbCwiYmlsbGVySWQiOiIxMDE3NDEyNTQ5ODcyMzUwIiwiYmlsbGVyTmFtZSI6IkFQUExFIiwiYmlsbGVyTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2OTE2MzU5Mjg2MTcxNi03NzcucG5nIiwiaXRlbUlkIjoiczEyMTE3ODI0MTk0NDEwMDAiLCJpdGVtTmFtZSI6IjIuMDAiLCJhcHBsaWNhYmxlQ291bnRyeSI6IlVTIn0seyJvcmRlck5vIjoiMzUyNTZyOXliZDAwMzUiLCJtZW1iZXJJZCI6IkExMkQ3OUY4MTZDMTQ0QjU5MzcwM0VDRkZFNTNGMzE5IiwiYnVzaW5lc3NUeXBlIjoiMzUiLCJjYXRlZ29yeUlkIjoiMzUiLCJvcmRlclN0YXR1cyI6MSwicGF5VHlwZSI6IjEiLCJ0cmFuc1R5cGUiOiJ2NSIsIm9yZGVyQW1vdW50IjozNTAwLCJidXNpbmVzc0Ftb3VudCI6MzUwMCwicGF5QW1vdW50IjozNTAwLCJkaXNjb3VudEFtb3VudCI6MCwicGF5bWVudE1ldGhvZHNEaXNjb3VudCI6IjAiLCJsb3lhbHR5QW1vdW50IjowLCJsb3lhbHR5UG9pbnQiOjAsInBvaW50QmFja0Ftb3VudCI6MCwicG9pbnRCYWNrQ291bnQiOm51bGwsInJldHVyblBvaW50c1RyYWZmaWNJZCI6bnVsbCwicmV0dXJuUG9pbnRzQmFja1RyYWZmaWNJZCI6bnVsbCwicmV0dXJuUG9pbnRCYWNrQW1vdW50IjpudWxsLCJyZXR1cm5Qb2ludEJhY2tDb3VudCI6MCwicGF5RmVlQW1vdW50IjowLCJ0YXhGZWVBbW91bnQiOjAsInRvdGFsQW1vdW50IjozNTAwLCJwYXlUaW1lIjoxNzE5NTQwNDA4MDAwLCJjb21wbGV0ZXRpbWUiOjE3MTk1NDA0MTcwMDAsImJpbGxlcklkIjoiMTAxNzQxMjU0OTg3MjM1MCIsImJpbGxlck5hbWUiOiJBUFBMRSIsImJpbGxlckxvZ28iOiJodHRwczovL3RyYW5zc25ldC1hbmRyb2lkLXVwbG9hZC1kZXYuczMuYW1hem9uYXdzLmNvbS9hY3Rpdml0eS8xNjkxNjM1OTI4NjE3MTYtNzc3LnBuZyIsIml0ZW1JZCI6InMxMjExNzgyNDE5NDQxMDAwIiwiaXRlbU5hbWUiOiIyLjAwIiwiYXBwbGljYWJsZUNvdW50cnkiOiJVUyJ9LHsib3JkZXJObyI6IjM1MjU2cXZxYmQwMDM1IiwibWVtYmVySWQiOiJBMTJENzlGODE2QzE0NEI1OTM3MDNFQ0ZGRTUzRjMxOSIsImJ1c2luZXNzVHlwZSI6IjM1IiwiY2F0ZWdvcnlJZCI6IjM1Iiwib3JkZXJTdGF0dXMiOjEsInBheVR5cGUiOiIxIiwidHJhbnNUeXBlIjoidjUiLCJvcmRlckFtb3VudCI6MzUwMCwiYnVzaW5lc3NBbW91bnQiOjM1MDAsInBheUFtb3VudCI6MzUwMCwiZGlzY291bnRBbW91bnQiOjAsInBheW1lbnRNZXRob2RzRGlzY291bnQiOiIwIiwibG95YWx0eUFtb3VudCI6MCwibG95YWx0eVBvaW50IjowLCJwb2ludEJhY2tBbW91bnQiOjAsInBvaW50QmFja0NvdW50IjpudWxsLCJyZXR1cm5Qb2ludHNUcmFmZmljSWQiOm51bGwsInJldHVyblBvaW50c0JhY2tUcmFmZmljSWQiOm51bGwsInJldHVyblBvaW50QmFja0Ftb3VudCI6bnVsbCwicmV0dXJuUG9pbnRCYWNrQ291bnQiOjAsInBheUZlZUFtb3VudCI6MCwidGF4RmVlQW1vdW50IjowLCJ0b3RhbEFtb3VudCI6MzUwMCwicGF5VGltZSI6MTcxOTUzOTg5NjAwMCwiY29tcGxldGV0aW1lIjoxNzE5NTM5OTAzMDAwLCJiaWxsZXJJZCI6IjEwMTc0MTI1NDk4NzIzNTAiLCJiaWxsZXJOYW1lIjoiQVBQTEUiLCJiaWxsZXJMb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTY5MTYzNTkyODYxNzE2LTc3Ny5wbmciLCJpdGVtSWQiOiJzMTIxMTc4MjQxOTQ0MTAwMCIsIml0ZW1OYW1lIjoiMi4wMCIsImFwcGxpY2FibGVDb3VudHJ5IjoiVVMifV19LCJzdGF0dXMiOnRydWV9", "encoding": "base64"}, "redirectURL": null, "headersSize": 130, "bodySize": 853}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 218, "receive": 4}}, {"startedDateTime": "2024-10-08T17:40:50.163+08:00", "time": 298, "request": {"method": "GET", "url": "https://ng-apptest.transspay.net/api/live-product/gateway/giftcard/order/detail?orderNo=3526ulgycb0035", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/live-product/gateway/giftcard/order/detail?orderNo=3526ulgycb0035"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.16.0&*********"}, {"name": "pp_timestamp", "value": "1728380449345"}, {"name": "content-type", "value": "application/json"}, {"name": "user-agent", "value": "PalmPay/5.16.0&********* (Android 13)"}, {"name": "pp_channel", "value": "googleplay"}, {"name": "pp_token", "value": "f063cf90-9e93-4001-9ee8-4b9063d3d745"}, {"name": "pp_req_sign", "value": "LIiEQzrbyTr%2BUhrZfPo5hD0UumVr3pW7zxKS3bkJBhzcUaOQ2619V4PsPPGUdIx%2FHi37VmES3RSwPqTMqXet99paRQsVWAlURgPBSSVey1lC63bO%2Fwdvvk18mikqnDJw5JPpPv2crehPCZDfqHSV9QlRpntpP3kE6DsRhGjoCwI%3D"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "if-modified-since", "value": "Tue, 08 Oct 2024 09:31:20 GMT"}], "queryString": [{"name": "orderNo", "value": "3526ulgycb0035"}], "headersSize": 418, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 08 Oct 2024 09:40:50 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162ae917283814503535865d0001"}, {"name": "x-envoy-upstream-service-time", "value": "90"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 3882, "compression": 2762, "mimeType": "application/json", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 130, "bodySize": 1120}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 293, "receive": 5}}, {"startedDateTime": "2024-10-08T17:41:12.607+08:00", "time": 650, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/live-product/giftcard/tab/list", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/live-product/giftcard/tab/list"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json, text/plain, */*"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.16.0&*********"}, {"name": "pp_client_ver_code", "value": "*********"}, {"name": "pp_timestamp", "value": "1728380471781"}, {"name": "pp_country_code", "value": "NG"}, {"name": "param_pp_app_source", "value": "PalmPay"}, {"name": "pp_token", "value": "f063cf90-9e93-4001-9ee8-4b9063d3d745"}, {"name": "pp_req_sign", "value": "JnbioD1WpuWV78czZHJHlL3ZMcoWwFKiaTkQJOI32apki7LSUrDUOlNCiVAeM1Fnf6BzxMthNlav1ysmsqnpmuGaY2WGq4Zx5BqipRc%2FnJLsKLrXSJwt9ki%2BDMbUEXbNbuBLb6FGt0Nni0eZs2uuvoMAQYSqIuWiKFrImu3iccg%3D"}, {"name": "pp_req_sign_2", "value": "JnbioD1WpuWV78czZHJHlL3ZMcoWwFKiaTkQJOI32apki7LSUrDUOlNCiVAeM1Fnf6BzxMthNlav1ysmsqnpmuGaY2WGq4Zx5BqipRc%2FnJLsKLrXSJwt9ki%2BDMbUEXbNbuBLb6FGt0Nni0eZs2uuvoMAQYSqIuWiKFrImu3iccg%3D"}, {"name": "countrycode", "value": "NG"}, {"name": "content-length", "value": "0"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": null, "text": ""}, "headersSize": 319, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 08 Oct 2024 09:41:13 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac162ae917283814727965892d0001"}, {"name": "x-envoy-upstream-service-time", "value": "449"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 1138, "compression": 692, "mimeType": "application/json", "text": "eyJyZXNwQ29kZSI6IjAwMDAwMDAwIiwicmVzcE1zZyI6InN1Y2Nlc3MiLCJkYXRhIjp7InRhYnMiOlt7InRhYklkIjowLCJ0YWJOYW1lIjoiQUxMIn0seyJ0YWJJZCI6MTAsInRhYk5hbWUiOiJlZWUzZWUifSx7InRhYklkIjoxMywidGFiTmFtZSI6ImVlZTM1NiJ9LHsidGFiSWQiOjEyLCJ0YWJOYW1lIjoiZWVlMzMzIn1dLCJiaWxsZXJJbmZvVm9zIjpbeyJiaWxsZXJJZCI6IjEwMDk4OTU2OTgyOTQ2NjAiLCJiaWxsZXJOYW1lIjoiQ2FuYWRhIFdpbm5lcnMiLCJiaWxsZXJMb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTY5MDg4NDIzMzc0NDItJUU3JTgxJUFGJUU1JUExJTk0LmpwZyIsInN1c3BlbmQiOmZhbHNlfSx7ImJpbGxlcklkIjoiMTAxNzU4MTI4MDA3NDc0MCIsImJpbGxlck5hbWUiOiJ0ZXN0IiwiYmlsbGVyTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2OTE2NTI4MTA3MDQxNi0lRTUlOEQlOTUlRTQlQjglQUElRTYlQjUlODElRTklODclOEYucG5nIiwic3VzcGVuZCI6ZmFsc2V9LHsiYmlsbGVySWQiOiIxMDE3NDEyNTQ5ODcyMzUwIiwiYmlsbGVyTmFtZSI6IkFQUExFIiwiYmlsbGVyTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2OTE2MzU5Mjg2MTcxNi03NzcucG5nIiwic3VzcGVuZCI6ZmFsc2V9LHsiYmlsbGVySWQiOiIxMDI3MDMyMTMxMjQ4NjAiLCJiaWxsZXJOYW1lIjoiU3RlYW0iLCJiaWxsZXJMb2dvIjoiaHR0cHM6Ly90cmFuc3NuZXQtYW5kcm9pZC11cGxvYWQtZGV2LnMzLmFtYXpvbmF3cy5jb20vYWN0aXZpdHkvMTY5MjU5Nzg5NzU0NjE3LVN0YXJUaW1lcy5qcGciLCJzdXNwZW5kIjpmYWxzZX0seyJiaWxsZXJJZCI6IjEwMjcwMzI2MDQzMDg2MCIsImJpbGxlck5hbWUiOiJMYW5yYXRuc2VyIiwiYmlsbGVyTG9nbyI6Imh0dHBzOi8vdHJhbnNzbmV0LWFuZHJvaWQtdXBsb2FkLWRldi5zMy5hbWF6b25hd3MuY29tL2FjdGl2aXR5LzE2OTI1OTc5NDQyMDA0LVpVS1UxLmpwZWciLCJzdXNwZW5kIjpmYWxzZX1dfSwic3RhdHVzIjp0cnVlfQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 131, "bodySize": 446}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 647, "receive": 3}}, {"startedDateTime": "2024-10-08T17:41:13.412+08:00", "time": 734, "request": {"method": "POST", "url": "https://ng-apptest.transspay.net/api/live-product/giftcard/bill/list", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "POST"}, {"name": ":path", "value": "/api/live-product/giftcard/bill/list"}, {"name": ":authority", "value": "ng-apptest.transspay.net"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json, text/plain, */*"}, {"name": "pp_device_id", "value": "bee648d4b0a5ab70012345"}, {"name": "pp_device_type", "value": "ANDROID"}, {"name": "pp_client_ver", "value": "5.16.0&*********"}, {"name": "pp_client_ver_code", "value": "*********"}, {"name": "pp_timestamp", "value": "1728380472584"}, {"name": "pp_country_code", "value": "NG"}, {"name": "param_pp_app_source", "value": "PalmPay"}, {"name": "pp_token", "value": "f063cf90-9e93-4001-9ee8-4b9063d3d745"}, {"name": "pp_req_sign", "value": "rFDZNK0oogaNyYgS3kFbu2X94VHTT0%2B7CjGNcyDv2ADGd3l56hJxyIsigPSlVfd4fc0%2FDMhjJymJtk1N9bS3B3%2BwuinbhoBtAVi4wBGIor%2BnIfCDI8NMW51o3xt8UcjS5mu%2FNRx0IOR%2FqGXHC7yjmZ0qiq0sZpK0dz5seE6Szaw%3D"}, {"name": "pp_req_sign_2", "value": "cupi61pXMkGc2lAqArHWnmgF1mVjfI2kZqfUNrnRs17EXmMq5SuHJeR1kAtvxMhcHImP1VpVOFGvkjBb09VLNIr4pTR9HMNqjz4HgKCDo2sJDhkoRz5dY0a1D7pDrPTWny0Iw%2F9OAymp%2B3VgaB95ky17oaIULo2af%2BCNzMFvqAc%3D"}, {"name": "countrycode", "value": "NG"}, {"name": "content-type", "value": "application/json"}, {"name": "content-length", "value": "37"}, {"name": "accept-encoding", "value": "gzip"}, {"name": "user-agent", "value": "okhttp/4.9.3"}], "queryString": [], "postData": {"mimeType": "application/json", "text": "{\"pageNum\":1,\"pageSize\":20,\"tabId\":0}"}, "headersSize": 358, "bodySize": 37}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":status", "value": "200"}, {"name": "date", "value": "Tue, 08 Oct 2024 09:41:14 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "eagleeye-traceid", "value": "eaac16266f17283814735987460d0001"}, {"name": "x-envoy-upstream-service-time", "value": "533"}, {"name": "content-encoding", "value": "gzip"}], "content": {"size": 3995, "compression": 3209, "mimeType": "application/json", "text": "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", "encoding": "base64"}, "redirectURL": null, "headersSize": 132, "bodySize": 786}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 733, "receive": 1}}]}}