# NOTE: Generated By HttpRunner v4.3.5
# FROM: testcases\palmpay\giftCard\giftCardApi_test.json
from httprunner import HttpRunner, Config, Step, RunRequest


class TestCaseGiftcardapiTest(HttpRunner):
    config = Config("testcase description")

    teststeps = [
        Step(
            RunRequest("")
            .get(
                "https://ng-apptest.transspay.net/api/scene-activity-product/bill/pullNew/insidePullNewList"
            )
            .with_params(**{"showLocation": "v5"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/scene-activity-product/bill/pullNew/insidePullNewList?showLocation=v5",
                    ":scheme": "https",
                    "accept": "application/json, text/plain, */*",
                    "accept-encoding": "gzip",
                    "countrycode": "NG",
                    "if-modified-since": "Tue, 08 Oct 2024 09:38:14 GMT",
                    "param_pp_app_source": "PalmPay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_client_ver_code": "609231908",
                    "pp_country_code": "NG",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "NCChlgRhi6eVGHLrJJr8awG61QZyyN7ekenZEcSjwBZrFc8X0VQNiHvPFMXNxuvPopijt9pLgEG6zv3pNutjaj3Ziy4BoQDGZnHwxrS3RYuKUvv6CF7W5bxkn8hu1JGeOMZ%2FkaV0v3g7hgFsv1XflU9Rspn4WzFWocugHBd2x5I%3D",
                    "pp_req_sign_2": "NCChlgRhi6eVGHLrJJr8awG61QZyyN7ekenZEcSjwBZrFc8X0VQNiHvPFMXNxuvPopijt9pLgEG6zv3pNutjaj3Ziy4BoQDGZnHwxrS3RYuKUvv6CF7W5bxkn8hu1JGeOMZ%2FkaV0v3g7hgFsv1XflU9Rspn4WzFWocugHBd2x5I%3D",
                    "pp_timestamp": "1728380434758",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("")
            .post("https://ng-apptest.transspay.net/api/cfront/coupon/statistics")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/coupon/statistics",
                    ":scheme": "https",
                    "accept": "application/json, text/plain, */*",
                    "accept-encoding": "gzip",
                    "content-length": "45",
                    "content-type": "application/json",
                    "countrycode": "NG",
                    "param_pp_app_source": "PalmPay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_client_ver_code": "609231908",
                    "pp_country_code": "NG",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "SaQAyvSbTpBF59gtIaVVdpyChYOEy4gCXC0qpag9P7cfR8kD%2Bt7xfAQiWo7mwBKz9%2BJFgORLAsvZw19bittkWb8W%2FJQnoFjBW5yE%2B4CrPk8bzUdEwd%2BuxJXPgZGGxzC05WHmELUSB7euMhnygqu4KP9qP6R%2FlO0KA8gfdyO0r%2Bc%3D",
                    "pp_req_sign_2": "Inx9Ulx2AZrPRFAX5Aakno4oVL7fvKFN4l3xYFrXRv7bU66slgZ207NtSsSo%2FXhI1NaHIWhP2GYCP9vbGL99svQt%2FETzM0CwnFLn8RQNfgbSIKFBQxqNsL3gjMTMwHFI%2FlhIV0%2BJ1QLPCrc2rPCWCdeZYDJhpfBYxjqxDmFofgw%3D",
                    "pp_timestamp": "1728380434760",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .with_data({"pageNum": 1, "pageSize": 100, "transType": "v5"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("")
            .get("https://ng-apptest.transspay.net/api/live-product/giftcard/homepage")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/live-product/giftcard/homepage",
                    ":scheme": "https",
                    "accept": "application/json, text/plain, */*",
                    "accept-encoding": "gzip",
                    "countrycode": "NG",
                    "if-modified-since": "Tue, 08 Oct 2024 09:38:14 GMT",
                    "param_pp_app_source": "PalmPay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_client_ver_code": "609231908",
                    "pp_country_code": "NG",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "k%2Bu0T70qpLYi4r3VSbTS7368Y75pMXzdx1owOcmIlnyyUWSoimdSrUbyLskZwnNC1W3cyK9C6MsXnkazGD5yWQ5fd2iYfcHB4EY27eKHaYNeLYcvGUsKsS6EF%2Bup0%2BDnbFg%2FRwfG5qrpCIRjccEeKn5LuXkSN7DlZr0jp5om9FU%3D",
                    "pp_req_sign_2": "k%2Bu0T70qpLYi4r3VSbTS7368Y75pMXzdx1owOcmIlnyyUWSoimdSrUbyLskZwnNC1W3cyK9C6MsXnkazGD5yWQ5fd2iYfcHB4EY27eKHaYNeLYcvGUsKsS6EF%2Bup0%2BDnbFg%2FRwfG5qrpCIRjccEeKn5LuXkSN7DlZr0jp5om9FU%3D",
                    "pp_timestamp": "1728380434766",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("")
            .post(
                "https://ng-apptest.transspay.net/api/live-product/gateway/giftcard/order/page"
            )
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/live-product/gateway/giftcard/order/page",
                    ":scheme": "https",
                    "accept": "application/json, text/plain, */*",
                    "accept-encoding": "gzip",
                    "content-length": "45",
                    "content-type": "application/json",
                    "countrycode": "NG",
                    "param_pp_app_source": "PalmPay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_client_ver_code": "609231908",
                    "pp_country_code": "NG",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "aNViL8%2BW5gzDb%2FVGQ%2F9FMYN5th0qIytEXRrjc9Z5ff0ehhaFLzlUaOOv%2BtM7YTIJzJOOzS%2BQzWrGAeku8VhER%2FSeXrVmHOzZD9MtNHbwCQxThsZ989p%2F1iYZ9IXkrK3l4t6x9S2dhoxOARN1z4tCnh67denIwBnaerwLR6RsDBY%3D",
                    "pp_req_sign_2": "gftaOWxi6pJ68XhStpuf%2FESU9jwzIDCSwgk1tT5lx773zR2DvlC5%2BdkJ%2FiUMiFLT9wGLjBuXWgf4wrR8MBFFh8GPoFGydCKYXhLNSanIiLVGik9FZJNEIoXmMGOJxScAcsERVGwzmkuHqj6IRm57wP7T9wqEkRalR%2FrDRfzKlpo%3D",
                    "pp_timestamp": "1728380444843",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .with_data({"categoryId": "35", "pageNum": 1, "pageSize": 10})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("")
            .get(
                "https://ng-apptest.transspay.net/api/live-product/gateway/giftcard/order/detail"
            )
            .with_params(**{"orderNo": "3526ulgycb0035"})
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "GET",
                    ":path": "/api/live-product/gateway/giftcard/order/detail?orderNo=3526ulgycb0035",
                    ":scheme": "https",
                    "accept-encoding": "gzip",
                    "content-type": "application/json",
                    "if-modified-since": "Tue, 08 Oct 2024 09:31:20 GMT",
                    "pp_channel": "googleplay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "LIiEQzrbyTr%2BUhrZfPo5hD0UumVr3pW7zxKS3bkJBhzcUaOQ2619V4PsPPGUdIx%2FHi37VmES3RSwPqTMqXet99paRQsVWAlURgPBSSVey1lC63bO%2Fwdvvk18mikqnDJw5JPpPv2crehPCZDfqHSV9QlRpntpP3kE6DsRhGjoCwI%3D",
                    "pp_timestamp": "1728380449345",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "PalmPay/5.16.0&609231908 (Android 13)",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("")
            .post("https://ng-apptest.transspay.net/api/live-product/giftcard/tab/list")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/live-product/giftcard/tab/list",
                    ":scheme": "https",
                    "accept": "application/json, text/plain, */*",
                    "accept-encoding": "gzip",
                    "content-length": "0",
                    "countrycode": "NG",
                    "param_pp_app_source": "PalmPay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_client_ver_code": "609231908",
                    "pp_country_code": "NG",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "JnbioD1WpuWV78czZHJHlL3ZMcoWwFKiaTkQJOI32apki7LSUrDUOlNCiVAeM1Fnf6BzxMthNlav1ysmsqnpmuGaY2WGq4Zx5BqipRc%2FnJLsKLrXSJwt9ki%2BDMbUEXbNbuBLb6FGt0Nni0eZs2uuvoMAQYSqIuWiKFrImu3iccg%3D",
                    "pp_req_sign_2": "JnbioD1WpuWV78czZHJHlL3ZMcoWwFKiaTkQJOI32apki7LSUrDUOlNCiVAeM1Fnf6BzxMthNlav1ysmsqnpmuGaY2WGq4Zx5BqipRc%2FnJLsKLrXSJwt9ki%2BDMbUEXbNbuBLb6FGt0Nni0eZs2uuvoMAQYSqIuWiKFrImu3iccg%3D",
                    "pp_timestamp": "1728380471781",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
        Step(
            RunRequest("")
            .post(
                "https://ng-apptest.transspay.net/api/live-product/giftcard/bill/list"
            )
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/live-product/giftcard/bill/list",
                    ":scheme": "https",
                    "accept": "application/json, text/plain, */*",
                    "accept-encoding": "gzip",
                    "content-length": "37",
                    "content-type": "application/json",
                    "countrycode": "NG",
                    "param_pp_app_source": "PalmPay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_client_ver_code": "609231908",
                    "pp_country_code": "NG",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "rFDZNK0oogaNyYgS3kFbu2X94VHTT0%2B7CjGNcyDv2ADGd3l56hJxyIsigPSlVfd4fc0%2FDMhjJymJtk1N9bS3B3%2BwuinbhoBtAVi4wBGIor%2BnIfCDI8NMW51o3xt8UcjS5mu%2FNRx0IOR%2FqGXHC7yjmZ0qiq0sZpK0dz5seE6Szaw%3D",
                    "pp_req_sign_2": "cupi61pXMkGc2lAqArHWnmgF1mVjfI2kZqfUNrnRs17EXmMq5SuHJeR1kAtvxMhcHImP1VpVOFGvkjBb09VLNIr4pTR9HMNqjz4HgKCDo2sJDhkoRz5dY0a1D7pDrPTWny0Iw%2F9OAymp%2B3VgaB95ky17oaIULo2af%2BCNzMFvqAc%3D",
                    "pp_timestamp": "1728380472584",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .with_data({"pageNum": 1, "pageSize": 20, "tabId": 0})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal(
                'headers."Content-Type"',
                "application/json",
                "assert response header Content-Type",
            )
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
            .assert_equal("body.status", True, "assert response body status")
        ),
    ]


if __name__ == "__main__":
    TestCaseGiftcardapiTest().test_start()
