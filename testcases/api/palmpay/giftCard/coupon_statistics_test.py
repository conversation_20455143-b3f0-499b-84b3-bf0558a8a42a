# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\giftCard\giftCardApi_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest


class TestCouponStatisticsTest(HttpRunner):
    config = Config("合并前领券接口").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("合并前领券接口").setup_hook('${setup_hooks_request($request)}')
            .post("/api/cfront/coupon/statistics")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/cfront/coupon/statistics",
                    ":scheme": "https",
                    "accept": "application/json, text/plain, */*",
                    "accept-encoding": "gzip",
                    "content-length": "45",
                    "content-type": "application/json",
                    "countrycode": "NG",
                    "param_pp_app_source": "PalmPay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_client_ver_code": "609231908",
                    "pp_country_code": "NG",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "SaQAyvSbTpBF59gtIaVVdpyChYOEy4gCXC0qpag9P7cfR8kD%2Bt7xfAQiWo7mwBKz9%2BJFgORLAsvZw19bittkWb8W%2FJQnoFjBW5yE%2B4CrPk8bzUdEwd%2BuxJXPgZGGxzC05WHmELUSB7euMhnygqu4KP9qP6R%2FlO0KA8gfdyO0r%2Bc%3D",
                    "pp_req_sign_2": "Inx9Ulx2AZrPRFAX5Aakno4oVL7fvKFN4l3xYFrXRv7bU66slgZ207NtSsSo%2FXhI1NaHIWhP2GYCP9vbGL99svQt%2FETzM0CwnFLn8RQNfgbSIKFBQxqNsL3gjMTMwHFI%2FlhIV0%2BJ1QLPCrc2rPCWCdeZYDJhpfBYxjqxDmFofgw%3D",
                    "pp_timestamp": "1728380434760",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .with_json({"pageNum": 1, "pageSize": 100, "transType": "v5"})
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]


if __name__ == "__main__":
    TestCouponStatisticsTest().test_start()
