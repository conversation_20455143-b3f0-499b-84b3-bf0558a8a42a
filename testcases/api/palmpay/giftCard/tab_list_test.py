# NOTE: Generated By HttpRunner v4.3.5
# FROM: D:\httprunner\demo\testcases\palmpay\giftCard\giftCardApi_test_test_xx.py
from httprunner import HttpRunner, Config, Step, RunRequest


class TestTabListTest(HttpRunner):
    config = Config("biller的卡片列表").base_url("${ENV(base_url_C)}")

    teststeps = [
        Step(
            RunRequest("biller的卡片列表").setup_hook('${setup_hooks_request($request)}')
            .post("/api/live-product/giftcard/tab/list")
            .with_headers(
                **{
                    ":authority": "ng-apptest.transspay.net",
                    ":method": "POST",
                    ":path": "/api/live-product/giftcard/tab/list",
                    ":scheme": "https",
                    "accept": "application/json, text/plain, */*",
                    "accept-encoding": "gzip",
                    "content-length": "0",
                    "countrycode": "NG",
                    "param_pp_app_source": "PalmPay",
                    "pp_client_ver": "5.16.0&609231908",
                    "pp_client_ver_code": "609231908",
                    "pp_country_code": "NG",
                    "pp_device_id": "bee648d4b0a5ab70012345",
                    "pp_device_type": "ANDROID",
                    "pp_req_sign": "JnbioD1WpuWV78czZHJHlL3ZMcoWwFKiaTkQJOI32apki7LSUrDUOlNCiVAeM1Fnf6BzxMthNlav1ysmsqnpmuGaY2WGq4Zx5BqipRc%2FnJLsKLrXSJwt9ki%2BDMbUEXbNbuBLb6FGt0Nni0eZs2uuvoMAQYSqIuWiKFrImu3iccg%3D",
                    "pp_req_sign_2": "JnbioD1WpuWV78czZHJHlL3ZMcoWwFKiaTkQJOI32apki7LSUrDUOlNCiVAeM1Fnf6BzxMthNlav1ysmsqnpmuGaY2WGq4Zx5BqipRc%2FnJLsKLrXSJwt9ki%2BDMbUEXbNbuBLb6FGt0Nni0eZs2uuvoMAQYSqIuWiKFrImu3iccg%3D",
                    "pp_timestamp": "1728380471781",
                    "pp_token": "f063cf90-9e93-4001-9ee8-4b9063d3d745",
                    "user-agent": "okhttp/4.9.3",
                }
            )
            .validate()
            .assert_equal("status_code", 200, "assert response status code")
            .assert_equal("body.respCode", "00000000", "assert response body respCode")
            .assert_equal("body.respMsg", "success", "assert response body respMsg")
        )
    ]


if __name__ == "__main__":
    TestTabListTest().test_start()
