#!/usr/bin/env python3
"""
测试脚本：验证 pytest 是否能正确识别 UI 目录下的 yaml 文件
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_ui_file_collection():
    """测试 UI 目录下的 yaml 文件是否能被正确识别"""
    
    # 模拟 pytest_collect_file 函数的逻辑
    def check_file_collection(file_path_str):
        file_path = Path(file_path_str)
        
        # 检查文件扩展名和路径
        is_yaml = file_path.suffix in [".yml", ".yaml"]
        has_ui_in_path = "UI" in file_path.parts
        
        print(f"文件: {file_path}")
        print(f"  - 是 YAML 文件: {is_yaml}")
        print(f"  - 路径包含 'UI': {has_ui_in_path}")
        print(f"  - 路径部分: {file_path.parts}")
        print(f"  - 会被收集: {is_yaml and has_ui_in_path}")
        print()
        
        return is_yaml and has_ui_in_path
    
    # 测试文件路径
    test_files = [
        "testcases/UI/demo/search.yaml",
        "testcases/UI/demo/android_bing_search.yaml", 
        "testcases/UI/web/__init__.py",  # 应该被忽略
        "testcases/api/some_test.yml",   # 应该被忽略
        "testcases/ui/demo/test.yaml",   # 小写 ui，应该被忽略
    ]
    
    print("=== 测试 UI 文件收集逻辑 ===\n")
    
    collected_files = []
    for file_path in test_files:
        if check_file_collection(file_path):
            collected_files.append(file_path)
    
    print(f"总共收集到的文件: {len(collected_files)}")
    for file in collected_files:
        print(f"  - {file}")
    
    # 验证实际文件是否存在
    print("\n=== 验证实际文件存在性 ===")
    for file_path in collected_files:
        full_path = project_root / file_path
        exists = full_path.exists()
        print(f"{file_path}: {'存在' if exists else '不存在'}")

if __name__ == "__main__":
    test_ui_file_collection()
